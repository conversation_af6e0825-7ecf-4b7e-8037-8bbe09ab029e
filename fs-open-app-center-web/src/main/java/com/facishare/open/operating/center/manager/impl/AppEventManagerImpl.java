package com.facishare.open.operating.center.manager.impl;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.ajax.code.AjaxCode;
import com.facishare.open.operating.center.api.com.EventFlag;
import com.facishare.open.operating.center.api.service.FirstTimeEventService;
import com.facishare.open.operating.center.api.service.SystemMsgNotifyService;
import com.facishare.open.operating.center.manager.AppEventManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by liqiulin on 2016/8/8.
 */
@Service
public class AppEventManagerImpl implements AppEventManager {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FirstTimeEventService firstTimeEventService;

    @Resource
    private SystemMsgNotifyService systemMsgNotifyService;

    @Override
    public Boolean openDevModeNotify(String appId, FsUserVO user) {
           // 设置返回的默认值
            Boolean isFirstTimeOpenDev = false;
        try {
            // 判断是否是第一次开启开发者模式
            BaseResult<Boolean> firstOpenDevMode = firstTimeEventService.isFirstTime(EventFlag.FIRST_OPEN_DEVELOPER_MODE, user.asStringUser());
            // 判断是否第一次开启开发者模式方法异常失败
            if(!firstOpenDevMode.isSuccess()) {
                logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                        "eventFlag[{}], eventExecutor[{}], firstOpenDevMode[{}]", EventFlag.FIRST_OPEN_DEVELOPER_MODE, user.asStringUser(), firstOpenDevMode);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, firstOpenDevMode, "是否第一次开启开发者模式判断失败异常"); // ignoreI18n
            }else{
                if (firstOpenDevMode.getResult()) {
                    // 标志设置为第一次true
                    isFirstTimeOpenDev = true;
                    // 发送消息
                    BaseResult<Boolean> firstOpenDevModeNotify = systemMsgNotifyService.developerModeMsgSent(appId, user);
                    if(!firstOpenDevModeNotify.isSuccess()) {
                        logger.warn("failed to call systemMsgNotifyService.developerModeMsgSent, " +
                                "appId[{}], user[{}], firstOpenDevModeNotify[{}]", appId,user.asStringUser(), firstOpenDevModeNotify);
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, firstOpenDevModeNotify, "首次开启开发者模式推送消息失败异常"); // ignoreI18n
                    }
                }
            return  isFirstTimeOpenDev;
            }
        } catch (Exception e) {
            logger.warn("failed to call AppEventManagerImpl.openDevModeNotify, " +
                    "appId[{}], user[{}], isFirstTimeOpenDev[{}]", appId,user.asStringUser(), isFirstTimeOpenDev);
            return isFirstTimeOpenDev;
        }
    }
}
