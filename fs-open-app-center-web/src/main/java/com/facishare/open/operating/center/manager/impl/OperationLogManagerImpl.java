package com.facishare.open.operating.center.manager.impl;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.operating.center.ajax.code.AjaxCode;
import com.facishare.open.operating.center.api.model.OperationLogVO;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.facishare.open.operating.center.manager.OperationLogManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by huyue on 2016/8/16.
 */

@Service
public class OperationLogManagerImpl implements OperationLogManager {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OperationLogService operationLogService;

//    /**
//     * 记录日志事件
//     *
//     * @param operationLogVO 日志的实体
//     * @return
//     */
//    @Override
//    public boolean saveOperationLog(OperationLogVO operationLogVO) {
//        BaseResult<Boolean> saveOperationLogResult = operationLogService.saveOperationLog(operationLogVO);
//        if(!saveOperationLogResult.isSuccess()) {
//            logger.warn("failed to call operationLogService.saveOperationLog, " +
//                    "operationLogVO[{}]",operationLogVO);
//            throw new BizException(AjaxCode.BIZ_EXCEPTION, saveOperationLogResult, "保存操作日志事件失败异常");
//        }
//        return saveOperationLogResult.getResult();
//    }

    /**
     * 读取日志事件
     *
     * @param user
     * @param appId
     * @return 日志list
     */
    @Override
    public Pager<OperationLogVO> findOperationLog(Pager<OperationLogVO> pager, FsUserVO user, String appId) {
        BaseResult<Pager<OperationLogVO>>  operationLogList =  operationLogService.findOperationLog(pager, user, appId);
        if(!operationLogList.isSuccess()) {
            logger.warn("failed to call operationLogService.findOperationLog, " +
                    "user[{}],appId[{}]",user,appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, operationLogList, "读取操作日志事件失败异常"); // ignoreI18n
        }
        return operationLogList.getResult();
    }
}
