package com.facishare.open.operating.center.manager.impl;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.ajax.code.AjaxCode;
import com.facishare.open.operating.center.manager.OperatingWebAuthManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2015年9月1日
 */
@Service
public class OperatingWebAuthManagerImpl implements OperatingWebAuthManager {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;

    /**
     * 使用token获取对应的纷享账号
     *
     * @param fsAuthXCookie
     * @return 纷享账号, 如果验证失败则抛出异常 @code{BizException(code=AjaxCode.NO_AUTHORITY)}
     */
    @Override
    public FsUserVO loadWebFsUser(String fsAuthXCookie) {
        CookieToAuth.Argument argument = new CookieToAuth.Argument();
        argument.setCookie(fsAuthXCookie);
        argument.setFsToken(null);
        argument.setIp(null);
        CookieToAuth.Result<AuthXC> cookieToAuthResult = activeSessionAuthorizeService.cookieToAuthXC(argument);
        if (!cookieToAuthResult.isSucceed()
                || !ValidateStatus.NORMAL.equals(cookieToAuthResult.getValidateStatus())) {
            logger.warn("activeSessionAuthorizeService.cookieToAuthXC failed, argument={}, resultUser=[{}]", argument, cookieToAuthResult);
            throw new BizException(AjaxCode.USER_NOT_LOGIN, "验证用户登录信息失败"); // ignoreI18n
        }
        AuthXC authXC = cookieToAuthResult.getBody();
        return new FsUserVO(authXC.getEnterpriseAccount(), authXC.getEmployeeId(), authXC.getAccount());
    }

}
