package com.facishare.open.operating.center.manager.impl;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.ajax.code.AjaxCode;
import com.facishare.open.operating.center.api.com.EventFlag;
import com.facishare.open.operating.center.api.service.SystemMsgNotifyService;
import com.facishare.open.operating.center.manager.FirstTimeEventManager;
import com.facishare.open.operating.center.manager.ServiceEventManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by liqiulin on 2016/8/8.
 */
@Service
public class ServiceEventManagerImpl implements ServiceEventManager {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FirstTimeEventManager firstTimeEventManager;

    @Resource
    private SystemMsgNotifyService systemMsgNotifyService;

    @Override
    public boolean isFirstTimeSendGroupMsg(FsUserVO fsUserVO, String appId) {
        String eventFlag = EventFlag.SERVICE_ADMIN_SEND_GROUP_MSG;
        boolean isFirstTime = firstTimeEventManager.isFirstTime(fsUserVO, eventFlag, false);
        // 是第一次则下发运营消息
        if(isFirstTime) {
            BaseResult<Boolean> msgResult = systemMsgNotifyService.groupMsgSent(appId, fsUserVO);
            if(!msgResult.isSuccess()) {
                logger.warn("failt to call systemMsgNotifyService.groupMsgSent, appId[{}], fsUserVO[{}], msgResult[{}]",
                        appId, fsUserVO, msgResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, msgResult, "下发第一次群发运营消息失败"); // ignoreI18n
            }
        }
        return isFirstTime;
    }

    /**
     * 是否在应用内群发过消息
     *
     * @param fsUserVO
     * @param appId
     * @return
     */
    @Override
    public boolean isSendGroupMsgInApp(FsUserVO fsUserVO, String appId) {
        String eventFlag = EventFlag.SERVICE_ADMIN_SEND_GROUP_MSG + "-" + appId;
        // 是第一次则下发运营消息
        return firstTimeEventManager.isFirstTime(fsUserVO, eventFlag, false);
    }

    @Override
    public boolean isFirstTimeEnterDashboard(FsUserVO fsUserVO, String appId) {
        String eventFlag = "FIRST_ENTER_SERVICE_DASHBOARDS" + "-" + appId;
        return firstTimeEventManager.isFirstTime(fsUserVO, eventFlag, false);
    }

}
