package com.facishare.open.operating.center.controller;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.operating.center.ajax.result.AjaxResult;
import com.facishare.open.operating.center.api.model.OperationLogVO;
import com.facishare.open.operating.center.common.BaseController;
import com.facishare.open.operating.center.manager.OperationLogManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by huyue on 2016/8/16.
 */


@Controller
@RequestMapping("/open/appcenter/operating/operation/log")
public class OperationLogEventController extends BaseController {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private OperationLogManager operationLogManager;

//    @RequestMapping("/saveOperationLog")
//    @ResponseBody
//    public AjaxResult saveOperationLog(@ModelAttribute OperationLogVO operationLogVO) {
//        checkParamNotBlank(operationLogVO, "日志为空值");
//        boolean saveOperationLog = operationLogManager.saveOperationLog(operationLogVO);
//        Map<String, Object> result = new HashMap<>();
//        result.put("saveOperationLog", saveOperationLog);
//        return new AjaxResult(result);
//    }

    @RequestMapping("/findOperationLog")
    @ResponseBody
    public AjaxResult findOperationLog(@ModelAttribute FsUserVO fsUserVO,
                                       @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                       @RequestParam(value = "appId", required = true) String appId) {
        checkParamNotBlank(appId, "应用ID为空"); // ignoreI18n
        Pager<OperationLogVO> pager = new Pager<>();
        pager.setCurrentPage(currentPage);
        pager.setPageSize(pageSize);
        Pager<OperationLogVO> operationLog = operationLogManager.findOperationLog(pager, fsUserVO, appId);
        Map<String, Object> result = new HashMap<>();
        result.put("pager", operationLog);
        result.put("currentTime", new Date().getTime());
        return new AjaxResult(result);
    }
}
