package com.facishare.open.operating.center.controller;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.operating.center.ajax.result.AjaxResult;
import com.facishare.open.operating.center.common.BaseController;
import com.facishare.open.operating.center.manager.FirstTimeEventManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by liq<PERSON>lin on 2016/8/8.
 */
@Controller
@RequestMapping("/open/appcenter/operating/first/time/event")
public class FirstTimeEventController extends BaseController {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FirstTimeEventManager firstTimeEventManager;

    /**
     * 判断用户是否第一次执行事件
     *
     * @param fsUserVO 用户.
     * @param onlyQuery 是否仅查询不更新事件状态，为空则默认会同步更新事件记录
     * @return AjaxResult.
     */
    @RequestMapping("/isFirstTime")
    @ResponseBody
    public AjaxResult isFirstTime(@ModelAttribute FsUserVO fsUserVO,
                                  @RequestParam(value = "eventFlag", required = false) String eventFlag,
                                  @RequestParam(value = "onlyQuery", required = false) Boolean onlyQuery) {
        checkParamNotBlank(eventFlag, "事件标识为空值"); // ignoreI18n

        boolean onlyQueryArg = false; // 默认会同步更新事件记录
        if (onlyQuery != null && onlyQuery == true) {
            onlyQueryArg = true;
        }
        boolean isFirstTime = firstTimeEventManager.isFirstTime(fsUserVO, eventFlag, onlyQueryArg);

        Map<String, Object> result = new HashMap<>();
        result.put("isFirstTime", isFirstTime);
        return new AjaxResult(result);
    }

}
