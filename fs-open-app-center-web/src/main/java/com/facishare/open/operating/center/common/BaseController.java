package com.facishare.open.operating.center.common;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.ajax.code.AjaxCode;
import com.facishare.open.operating.center.ajax.result.AjaxResult;
import com.facishare.open.operating.center.manager.OperatingWebAuthManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 基本的controller的父类.
 *
 * <AUTHOR>
 * @date 2015-11-30.
 */
public abstract class BaseController {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    protected static final AjaxResult SUCCESS = new AjaxResult("OK");
    @Resource
    protected OperatingWebAuthManager webAuthManager;

    protected void checkParamNotBlank(Object obj, String message) {
        if (null == obj) {
            throw new BizException(AjaxCode.PARAM_ERROR, message);
        }

        if (obj instanceof String && StringUtils.isBlank((String) obj)) {
            throw new BizException(AjaxCode.PARAM_ERROR, message);
        }
    }

    protected void checkParamTrue(boolean trueValue, String message) {
        if (!trueValue) {
            throw new BizException(AjaxCode.PARAM_ERROR, message);
        }
    }

    protected void checkParamRegex(String obj, String regex, String message) {
        checkParamNotBlank(obj, message);
        obj = obj.trim();
        if (null != regex && !obj.matches(regex)) {
            throw new BizException(AjaxCode.PARAM_ERROR, message);
        }
    }

    /**
     * 异常通用处理
     *
     * @param request 请求.
     * @param e 异常
     * @throws Exception
     */
    @ExceptionHandler
    @ResponseBody
    public AjaxResult exception(HttpServletRequest request, Exception e) throws Exception {
        logger.error("req["+request.getRequestURI()+"] " + e.getLocalizedMessage(),e);
        if (e instanceof BizException) {
            BizException biz = (BizException) e;
            return new AjaxResult(biz.getErrCode(), biz.getErrDescription());
        }
        if(e instanceof IllegalArgumentException){
            return new AjaxResult(AjaxCode.PARAM_ERROR, e.getLocalizedMessage());
        }
        return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "系统异常"); // ignoreI18n
    }

    /**
     * 获取用户信息
     * @param fsAuthXCCookie cookie.
     * @return 用户.
     */
    @ModelAttribute
    public FsUserVO processFsUserVOFormFSAuthX(@CookieValue(value = "FSAuthXC", required = false) String fsAuthXCCookie) {
        if (StringUtils.isBlank(fsAuthXCCookie)) {
            throw new BizException(AjaxCode.USER_NOT_LOGIN, "USER_NOT_LOGIN", "用户未登录"); // ignoreI18n
        }
        return webAuthManager.loadWebFsUser(fsAuthXCCookie);
    }

}
