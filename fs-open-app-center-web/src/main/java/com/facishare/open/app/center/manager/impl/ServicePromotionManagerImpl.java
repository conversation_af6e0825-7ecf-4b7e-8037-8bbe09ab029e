package com.facishare.open.app.center.manager.impl;

import com.facishare.common.proxy.helper.StringUtils;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.cons.CacheConstants;
import com.facishare.open.app.center.manager.ServicePromotionManager;
import com.facishare.open.app.center.model.ServicePromotionVO;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.github.jedis.support.MergeJedisCmd;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by linchf on 2016/8/23.
 */
@Service
public class ServicePromotionManagerImpl implements ServicePromotionManager {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;

    @Override
    public ServicePromotionVO queryServicePromotionVO(FsUserVO user) {
        String fsEa = user.getEnterpriseAccount();
        Integer userId = user.getUserId();
        //企业服务号数key
        String eaServiceNumberKey = String.format(CacheConstants.ACCESS_EA_SERVICE_NUMBER, user.getEnterpriseAccount());

        //1.获取企业服务号数
        String eaServiceNumStr = jedis.get(eaServiceNumberKey);
        if (StringUtils.isBlank(eaServiceNumStr)) {
            com.facishare.open.common.result.BaseResult<Long> result =
                    openAppService.queryServiceCount(fsEa);
            if (!result.isSuccess() || null == result.getResult()) {
                logger.warn("queryServiceCount failed. fsEa[{}], result[{}]", fsEa, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取企业服务号数失败!"); // ignoreI18n
            }
            Long eaServiceNum = result.getResult();
            eaServiceNumStr = eaServiceNum.toString();
            jedis.set(eaServiceNumberKey, eaServiceNumStr);
        }
        //2.判断企业服务号数
        Integer eaServiceNum = Integer.valueOf(eaServiceNumStr);
        if (eaServiceNum > 0) {
            return new ServicePromotionVO();
        }
        //3.获取企业管理员列表
        //企业系统管理员列表key
        String eaSystemAdminKey = String.format(CacheConstants.ACCESS_EA_SYSTEM_ADMIN, fsEa);
        Set<String> adminIds = jedis.smembers(eaSystemAdminKey);
        if (org.springframework.util.CollectionUtils.isEmpty(adminIds)) {
            BaseResult<List<Integer>> result = openAppAddressBookEmployeeService.getAdminIds(fsEa);
            if (!result.isSuccess() || null == result.getResult()) {
                logger.warn("getAdminIds failed. fsEa[{}], result[{}]", fsEa, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取企业管理员列表失败!"); // ignoreI18n
            }
            List<Integer> adminIdList = result.getResult();
            if (!org.springframework.util.CollectionUtils.isEmpty(adminIdList)) {
                String[] adminString = adminIdList.stream().map(id -> ("" + id))
                        .collect(Collectors.toList()).toArray(new String[adminIdList.size()]);
                jedis.sadd(eaSystemAdminKey, adminString);
                jedis.expire(eaSystemAdminKey, 86400);
                CollectionUtils.addAll(adminIds, adminString);
            }
        }
        //4.判断用户是否为系统管理员
        if (adminIds.contains(userId.toString())) {
            //5.判断该管理员是否关闭服务号推广
            String eaClosePromotionAdminKey = String.format(CacheConstants.ACCESS_EA_CLOSE_SERVICE_PROMOTION_ADMIN, fsEa);
            Set<String> closePromotionAdminIds = jedis.smembers(eaClosePromotionAdminKey);
            if (!org.springframework.util.CollectionUtils.isEmpty(closePromotionAdminIds)) {
                if (closePromotionAdminIds.contains(userId.toString())) {
                    return new ServicePromotionVO();
                }
            }
            return queryServicePromotionStatics();
        }
        return new ServicePromotionVO();
    }

    @Override
    public void delEaServiceCountCache(FsUserVO user, String appId) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            logger.warn("loadOpenAppFast failed. appId[{}], result[{}]", appId, appResult);
            return;
        }
        if (appResult.getResult().getAppType() == AppCenterEnum.AppType.SERVICE.value()) {
            jedis.del(String.format(CacheConstants.ACCESS_EA_SERVICE_NUMBER, user.getEnterpriseAccount()));
        }
    }

    @Override
    public void addEaClosePromotionAdminCache(FsUserVO user) {
        String eaClosePromotionAdminKey = String.format(CacheConstants.ACCESS_EA_CLOSE_SERVICE_PROMOTION_ADMIN
                , user.getEnterpriseAccount());
        jedis.sadd(eaClosePromotionAdminKey, user.getUserId().toString());
    }

    /**
     * 获取服务号推广静态数据.
     *
     * @return ServicePromotionVO
     */
    private ServicePromotionVO queryServicePromotionStatics() {
        ServicePromotionVO servicePromotionVO = new ServicePromotionVO();

        servicePromotionVO.setTitle(ConfigCenter.SERVICE_PROMOTION_TITLE);
        servicePromotionVO.setServiceName(ConfigCenter.SERVICE_PROMOTION_SERVICE_NAME);
        servicePromotionVO.setPicUrl(ConfigCenter.SERVICE_PROMOTION_PIC_URL);
        servicePromotionVO.setLeadingWords(ConfigCenter.SERVICE_PROMOTION_LEADING_WORDS);
        servicePromotionVO.setLearnMore(ConfigCenter.SERVICE_PROMOTION_LEARN_MORE);
        servicePromotionVO.setBuildUrl(ConfigCenter.SERVICE_PROMOTION_BUILD_URL);
        servicePromotionVO.setIntroduction(ConfigCenter.SERVICE_PROMOTION_INTRODUCTION);
        servicePromotionVO.setTemplateId(ConfigCenter.SERVICE_PROMOTION_TEMPLATE_ID);
        return servicePromotionVO;
    }

}
