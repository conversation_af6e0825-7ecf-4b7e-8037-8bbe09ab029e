package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.ajax.result.AppPagerAjaxResult;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.AppBindManager;
import com.facishare.open.app.center.manager.LinkServiceManager;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.material.api.enums.MessageSendTypeEnum;
import com.facishare.open.material.api.model.MaterialMessageServiceDTO;
import com.facishare.open.material.api.model.vo.MessageRecordVO;
import com.facishare.open.material.api.service.MaterialMessageService;
import com.facishare.open.msg.constant.CustomerServiceMsgType;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 消息中心服务
 * <AUTHOR>
 * @date 2016年3月4日
 */
@Controller
@RequestMapping("/open/appcenter/msgcenter")
public class MessageCenterController extends BaseController {

    private static final int MESSAGE_STATUS_READ_UNREPLIED = 2;

    private static final int BIND_STATUS_ENABLE = 1;

    @Resource
    private MaterialMessageService materialMessageService;

    @Resource
    private MessageExhibitionService messageExhibitionService;

    @Resource
    private AppBindManager appBindManager;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private LinkServiceManager linkServiceManager;

    @Value("${fs.open.app.center.AppBindController.nearbyCustomersAppId}")
    private String nearbyCustomersAppId;

    /**
     * 查询用户会话消息列表
     * 
     * @param user
     * @param currentPage
     * @param pageSize
     * @param appId
     * @param status
     * @param lastMessageId
     * @return
     */
    @RequestMapping("/queryUserSessions")
    @ResponseBody
    public AppPagerAjaxResult queryUserSessions(@ModelAttribute FsUserVO user,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "lastMessageId", defaultValue = "0") Long lastMessageId) { 

        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkAppAdmin(user, appId);

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("ea", user.getEnterpriseAccount());
        paramMap.put("appId", appId);
        paramMap.put("status", status);
        paramMap.put("lastMessageId", lastMessageId);

        MessageExhibitionResult<Pager<UserSessionVO>> result = queryAllUserSessions(appId, user, currentPage, pageSize, status, lastMessageId);
        if (!result.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询失败"); // ignoreI18n
        }
        return new AppPagerAjaxResult(result.getData());
    }

    /**
     * 查询服务号用户消息统计数据
     * @param user : 用户
     * @param startTime :查询的开始时间
     * @param endTime :查询的终止时间
     * @param appId :服务号ID
     * @return AjaxResult
     */
    @RequestMapping("/queryServiceAppMsg")
    @ResponseBody
    public AjaxResult queryStatEnterpriseAppMsg(@ModelAttribute FsUserVO user,
                                                @RequestParam(value = "startTime") Long startTime,
                                                @RequestParam(value = "endTime") Long endTime,
                                                @RequestParam(value = "appId") String appId) {

        checkParamTrue(endTime > startTime, "时间有误"); // ignoreI18n
        checkService(appId);

        if (linkServiceManager.isLinkService(appId)) {
            StatCrossAppMsgVO result = statLinkServiceAppMsg(startTime, endTime, appId, user.getEnterpriseAccount());
            return new AjaxResult(result);
        } else {
            StatInternalAppMsgVO result = statEnterpriseAppMsg(startTime, endTime, appId, user.getEnterpriseAccount());
            return new AjaxResult(result);
        }
    }


    /**
     * 查询客服人员反馈消息的各类统计数据
     * @param user : 用户
     * @param startTime :查询的开始时间
     * @param endTime :查询的终止时间
     * @param appId :互联服务号ID
     * @return AjaxResult
     */
    @RequestMapping("/queryCustomerMsg")
    @ResponseBody
    public AjaxResult queryCustomerMsg(@ModelAttribute FsUserVO user,
                                                 @RequestParam(value = "startTime") Long startTime,
                                                 @RequestParam(value = "endTime") Long endTime,
                                                 @RequestParam(value = "appId") String appId) {
        checkService(appId);
        checkParamTrue(endTime > startTime, "时间有误"); // ignoreI18n
        StatCustomerMsgVO result = statCustomerMsg(startTime, endTime, appId, user.getEnterpriseAccount());
        return new AjaxResult(result);
    }

    /**
     * 查询应用消息列表
     * 
     * @param user
     * @param currentPage
     * @param pageSize
     * @param appId
     * @param status
     * @return
     */
    @RequestMapping("/queryAppMessages")
    @ResponseBody
    public AppPagerAjaxResult queryAppMessages(@ModelAttribute FsUserVO user,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "status") Integer status) { 

        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkAppAdmin(user, appId);

        Pager<MessageRecordVO> pager = new Pager<>();
        pager.setCurrentPage(currentPage);
        pager.setPageSize(pageSize);

        BaseResult<Pager<MessageRecordVO>> result = materialMessageService.queryPageByAppId(pager, appId,
                user.getEnterpriseAccount(), Lists.newArrayList(MessageSendTypeEnum.GROUP_SEND_MSG));

        if (!result.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询失败"); // ignoreI18n
        }

        return new AppPagerAjaxResult(result.getResult());
    }

    @RequestMapping("/revokeAppMessage")
    @ResponseBody
    public AjaxResult revokeAppMessage(@ModelAttribute FsUserVO user,
                                       @RequestBody MaterialMessageServiceDTO.RevokeMsg.Arg arg) {
        checkParamNotBlank(arg.getAppId(), "appId is blank");
        checkParamNotBlank(arg.getMessageId(), "messageId is blank");

        checkAppAdmin(user, arg.getAppId());
        arg.setEa(user.getEa());

        BaseResult<Void> result = materialMessageService.revokeMsg(arg);
        if (!result.isSuccess()) {
            throw new BizException(result);
        }

        return new AjaxResult(result.getResult());
    }

    /**
     * 删除应用消息
     *
     * @param user
     * @param appId
     * @param messageId
     * @return
     */
    @RequestMapping("/deleteAppMessages")
    @ResponseBody
    public AjaxResult deleteAppMessages(@ModelAttribute FsUserVO user,
                                       @RequestParam(value = "appId", required = false) String appId,
                                       @RequestParam(value = "messageId", required = false) String messageId) {
        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkParamNotBlank(messageId, "请选择要删除的消息"); // ignoreI18n

        checkAppAdmin(user, appId);

        BaseResult<String> result = materialMessageService.removeMessage(messageId);
        if (!result.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "删除失败"); // ignoreI18n
        }
        return new AjaxResult(result.getResult());
    }

    /**
     * 查询单一用户会话消息列表
     * 
     * @param user
     * @param currentPage
     * @param pageSize
     * @param appId
     * @param sessionId  
     * @param lastMessageId
     * @return
     */
    @RequestMapping("/querySingleUserSessionMessages")
    @ResponseBody
    public AppPagerAjaxResult querySingleUserSessionMessages(@ModelAttribute FsUserVO user,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "sessionId", required = false) String sessionId,
            @RequestParam(value = "lastMessageId", defaultValue = "0") Long lastMessageId) { 

        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkParamNotBlank(sessionId, "请选择相应的用户会话消息"); // ignoreI18n
        checkAppAdmin(user, appId);

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("ea", user.getEnterpriseAccount());
        paramMap.put("appId", appId);
        paramMap.put("sessionId", sessionId);
        paramMap.put("lastMessageId", lastMessageId);

        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("querySingleUserSessionMessages openAppService.loadOpenApp fail.appId[{}], fsUserVO[{}], appResult[{}]", appId, user, appResult);
            throw new BizException(appResult);
        }

        QuerySingleUserSessionVO querySingleUserSessionVO = new QuerySingleUserSessionVO();
        querySingleUserSessionVO.setAppId(appId);
        querySingleUserSessionVO.setSessionId(sessionId);
        querySingleUserSessionVO.setEa(user.getEnterpriseAccount());
        querySingleUserSessionVO.setLastMessageId(lastMessageId);
        querySingleUserSessionVO.setCurrentPage(currentPage);
        querySingleUserSessionVO.setPageSize(pageSize);
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            querySingleUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            querySingleUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }
        MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> result = messageExhibitionService.querySingleUserSessionMessages(querySingleUserSessionVO);
        if (!result.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询失败"); // ignoreI18n
        }

        return new AppPagerAjaxResult(result.getData());
    }

    /**
     * 同步单一用户会话消息状态 为“已读”
     * @param user
     * @param appId
     * @param sessionId
     * @param lastMessageId
     * @param status                2表示已读
     * @return
     */
    @RequestMapping("/updateSingleUserSessionStatus")
    @ResponseBody
    public AjaxResult updateSingleUserSessionStatus(@ModelAttribute FsUserVO user,
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "sessionId", required = false) String sessionId,
            @RequestParam(value = "lastMessageId", defaultValue = "0") Long lastMessageId,
            @RequestParam(value = "status") Integer status) {

        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkParamNotBlank(sessionId, "请选择相应的用户会话消息"); // ignoreI18n
        if (status != MESSAGE_STATUS_READ_UNREPLIED) {
            throw new BizException(AjaxCode.PARAM_ERROR, "请输入正确的会话消息状态"); // ignoreI18n
        }
        checkAppAdmin(user, appId);

        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], fsUserVO[{}], appResult[{}]", appId, user, appResult);
            throw new BizException(appResult);
        }
        UpdateSingleUserSessionVO updateSingleUserSessionVO = new UpdateSingleUserSessionVO();
        updateSingleUserSessionVO.setUpStreamEa(user.getEnterpriseAccount());
        updateSingleUserSessionVO.setSessionId(sessionId);
        updateSingleUserSessionVO.setLastMessageId(lastMessageId);
        updateSingleUserSessionVO.setStatus(status);
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            updateSingleUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            updateSingleUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }

        MessageExhibitionResult<Void> result = messageExhibitionService.updateSingleUserSessionStatus(updateSingleUserSessionVO);
        if (!result.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, result.getErrDescription());
        }

        return SUCCESS;
    }

    /**
     * 单一用户消息会话新消息提示
     * 
     * @param user
     * @param appId
     * @param sessionId
     * @param lastMessageId
     * @return
     */
    @RequestMapping("/promptSingleUserSessionUnReadMessages")
    @ResponseBody
    public AjaxResult promptSingleUserSessionUnReadMessages(@ModelAttribute FsUserVO user,
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "sessionId", required = false) String sessionId,
            @RequestParam(value = "lastMessageId", defaultValue = "0") Long lastMessageId) {

        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkParamNotBlank(sessionId, "请选择相应的用户会话消息"); // ignoreI18n
        checkAppAdmin(user, appId);

        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], FsUserVO[{}], appResult[{}]", appId, user, appResult);
            throw new BizException(appResult);
        }
        CountSingleUserSessionUnReadMessageVO countSingleUserSessionUnReadMessageVO = new CountSingleUserSessionUnReadMessageVO();
        countSingleUserSessionUnReadMessageVO.setUpStreamEa(user.getEnterpriseAccount());
        countSingleUserSessionUnReadMessageVO.setSessionId(sessionId);
        countSingleUserSessionUnReadMessageVO.setLastMessageId(lastMessageId);
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            countSingleUserSessionUnReadMessageVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            countSingleUserSessionUnReadMessageVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }

        MessageExhibitionResult<Long> result = messageExhibitionService.countSingleUserSessionUnReadMessages(countSingleUserSessionUnReadMessageVO);
        if (!result.isSuccess()) {
            logger.warn("call messageExhibitionService.countSingleUserSessionUnReadMessages fail. CountSingleUserSessionUnReadMessageVO[{}], appResult[{}]", countSingleUserSessionUnReadMessageVO, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询失败"); // ignoreI18n
        }

        return new AjaxResult(result.getData());
    }

    /**
     * 用户会话消息列表新消息提示
     * 
     * @param user
     * @param appId
     * @param lastMessageId
     * @return
     */
    @RequestMapping("/promptUserSessionsUnReadMessages")
    @ResponseBody
    public AjaxResult promptSingleUserSessionUnReadMessages(@ModelAttribute FsUserVO user,
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "lastMessageId", defaultValue = "0") Long lastMessageId) {

        checkParamNotBlank(appId, "请选择要处理的应用"); // ignoreI18n
        checkAppAdmin(user, appId);

        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], FsUserVO[{}], appResult[{}]", appId, user, appResult);
            throw new BizException(appResult);
        }

        ExistUnReadUserSessionVO existUnReadUserSessionVO = new ExistUnReadUserSessionVO();
        existUnReadUserSessionVO.setUpStreamEa(user.getEnterpriseAccount());
        existUnReadUserSessionVO.setAppId(appId);
        existUnReadUserSessionVO.setLastMessageId(lastMessageId);
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            existUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            existUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }
        MessageExhibitionResult<Boolean> result = messageExhibitionService.existUnReadUserSessions(existUnReadUserSessionVO);
        if (!result.isSuccess()) {
            logger.warn("call messageExhibitionService.existUnReadUserSessions fail. ExistUnReadUserSessionVO[{}], appResult[{}]", existUnReadUserSessionVO, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询失败"); // ignoreI18n
        }

        return new AjaxResult(result.getData());
    }

    @RequestMapping("/promptUnReadStatusforApps")
    @ResponseBody
    public AjaxResult promptUnReadStatusforApps(@ModelAttribute FsUserVO user) {
        List<OpenAppDO> appDOs = appBindManager.queryAppsByFsAppAdmin(user);

        List<String> appIds = appDOs
                .stream()
                .filter(appDO -> !nearbyCustomersAppId.equals(appDO.getAppId())
                        && appDO.getBindStatus() == BIND_STATUS_ENABLE).map(appDO -> appDO.getAppId())
                .collect(Collectors.toList());

        MessageExhibitionResult<Map<String, Boolean>> result = messageExhibitionService
                .queryUnReadUserSessionsByAppIds(user.getEnterpriseAccount(), appIds);

        if (result.isSuccess() && !result.getData().isEmpty()) {
            return new AjaxResult(Boolean.TRUE);
        }

        return new AjaxResult(Boolean.FALSE);
    }

    private MessageExhibitionResult<Pager<UserSessionVO>> queryAllUserSessions(String appId, FsUserVO user, Integer currentPage,
                                   Integer pageSize, Integer status, Long lastMessageId){
        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], fsUserVO[{}], appResult[{}]", appId, user, appResult);
            throw new BizException(appResult);
        }
        QueryAllUserSessionVO queryAllUserSessionVO = new QueryAllUserSessionVO();
        queryAllUserSessionVO.setCurrentPage(currentPage);
        queryAllUserSessionVO.setPageSize(pageSize);
        queryAllUserSessionVO.setEa(user.getEnterpriseAccount());
        queryAllUserSessionVO.setAppId(appId);
        queryAllUserSessionVO.setStatus(status);
        queryAllUserSessionVO.setLastMessageId(lastMessageId);
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            queryAllUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            queryAllUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }
        return messageExhibitionService.queryAllUserSessions(queryAllUserSessionVO);
    }

    private StatInternalAppMsgVO statEnterpriseAppMsg(Long startTime, Long endTime, String appId, String fsEa) {
        MessageExhibitionResult<StatInternalAppMsgVO> result = messageExhibitionService.statInternalAppMsg(startTime, endTime, appId, fsEa, true);
        if (!result.isSuccess()) {
            logger.warn("call messageExhibitionService.statInternalAppMsg error,startTime[{}], endTime[{}], " +
                    "appId[{}], fsEa[{}], result[{}]",startTime, endTime, appId, fsEa, result);
            throw new BizException(result);
        }
        return result.getData();
    }

    private StatCrossAppMsgVO statLinkServiceAppMsg(Long startTime, Long endTime, String appId, String fsEa) {
        MessageExhibitionResult<StatCrossAppMsgVO> result = messageExhibitionService.statCrossAppMsg(startTime, endTime, appId, fsEa, true);
        if (!result.isSuccess()) {
            logger.error("call messageExhibitionService.statCrossAppMsg error,startTime[{}], endTime[{}], " +
                    "appId[{}], fsEa[{}], result[{}]",startTime, endTime, appId, fsEa, result);
            throw new BizException(result);
        }
        return result.getData();
    }

    private StatCustomerMsgVO statCustomerMsg(Long startTime, Long endTime, String appId, String fsEa) {
        MessageExhibitionResult<StatCustomerMsgVO> result = messageExhibitionService.statCustomerMsg(startTime, endTime, appId, fsEa);
        if (!result.isSuccess()) {
            logger.error("call messageExhibitionService.statCustomerMsg error,startTime[{}], endTime[{}], " +
                    "appId[{}], fsEa[{}], result[{}]",startTime, endTime, appId, fsEa, result);
            throw new BizException(result);
        }
        return result.getData();
    }

}
