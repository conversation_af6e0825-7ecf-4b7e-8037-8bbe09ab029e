package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.Employee;
import com.facishare.open.app.center.api.model.vo.OuterServiceWechatVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.manager.OuterContactsManager;
import com.facishare.open.app.center.manager.OuterServiceManager;
import com.facishare.open.app.center.model.outers.CommissionerVO;
import com.facishare.open.app.center.model.outers.OuterContactsVO;
import com.facishare.open.app.center.model.outers.args.OuterContactsQueryPagerArgs;
import com.facishare.open.app.center.model.outers.args.SaveServiceCommissionersArgs;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.autoreplymsg.model.WorkbenchMessageVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.facishare.open.msg.service.SendMessageService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.vo.FSUserVO;
import com.facishare.wechat.proxy.model.vo.ModifyBindInfoVo;
import com.facishare.wechat.proxy.model.vo.QueryBindInfoVo;
import com.facishare.wechat.proxy.model.wx.WechatUserBindFsResult;
import com.facishare.wechat.proxy.model.wx.WechatUserBindInfoResult;
import com.facishare.wechat.proxy.service.WechatUserBindInfoService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * impl.
 * Created by zenglb on 2016/11/8.
 */
@Service
public class OuterContactsManagerImpl implements OuterContactsManager {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private WechatUserBindInfoService wechatUserBindInfoService;
    @Resource
    private OuterServiceManager outerServiceManager;
    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;
    @Resource
    private MessageExhibitionService messageExhibitionService;
    @Resource
    private MsgCustomerService msgCustomerService;
    @Resource
    private SendMessageService sendMessageService;

    @Override
    public Pager<OuterContactsVO> queryPager(FsUserVO fsUserVO, OuterContactsQueryPagerArgs args) {
        // 查询绑定关系.
        OuterServiceWechatVO outerServiceWechatVO = outerServiceManager.queryOuterServiceWechat(fsUserVO, null, args.getAppId(), CommonConstant.VALID);
        if (null == outerServiceWechatVO){
            throw new BizException(AjaxCode.BIZ_EXCEPTION,"服务号不存在"); // ignoreI18n
        }

        //拼接分页.
        Pager<WechatUserBindInfoResult> pager = new Pager<>();
        pager.setCurrentPage(args.getCurrentPage());
        pager.setPageSize(args.getPageSize());

        //查询条件拼接.
        QueryBindInfoVo queryBindInfoVo = new QueryBindInfoVo();
        queryBindInfoVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        queryBindInfoVo.setWxAppId(outerServiceWechatVO.getWxAppId());
        if (null != args.getUserId() && args.getUserId() > 0) {
            queryBindInfoVo.setFsUserId(args.getUserId().longValue());
        }
        if (!Strings.isNullOrEmpty(args.getSearchText())) {
            queryBindInfoVo.setNickName(args.getSearchText());
        }

        ModelResult<Pager<WechatUserBindInfoResult>> pagerModelResult = wechatUserBindInfoService.queryWechatUserByPage(pager, queryBindInfoVo);
        if (!pagerModelResult.isSuccess()) {
            logger.error("queryWechatUserByPage failed.fsUserVO[{}], args[{}], result[{}]", fsUserVO, args, pagerModelResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询失败."); // ignoreI18n
        }

        Pager<WechatUserBindInfoResult> weChatUserBindInfoResultPager = pagerModelResult.getResult();
        Pager<OuterContactsVO> result = new Pager<>();
        result.setCurrentPage(args.getCurrentPage());
        result.setPageSize(args.getPageSize());
        result.setRecordSize(weChatUserBindInfoResultPager.getRecordSize());
        if (!CollectionUtils.isEmpty(weChatUserBindInfoResultPager.getData())) {
            // 获取员工名称.
            Set<Integer> employeeIds = new HashSet<>();
            List<String> wxOpenIdList = new ArrayList<>();
            weChatUserBindInfoResultPager.getData().forEach(weChatUserBindInfoResult -> {
                if (!CollectionUtils.isEmpty(weChatUserBindInfoResult.getFsUserList())) {
                    employeeIds.addAll(weChatUserBindInfoResult.getFsUserList().stream().map(FSUserVO::getFsUserId).map(Long::intValue).collect(Collectors.toList()));
                }
                wxOpenIdList.add(weChatUserBindInfoResult.getWxOpenId());
            });
            Map<Integer, Employee> employeeMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(employeeIds)){
                BaseResult<List<Employee>> employeesNoAdminId = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsUserVO.getEnterpriseAccount(), Lists.newArrayList(employeeIds));
                if (!employeesNoAdminId.isSuccess()){
                    logger.error("getEmployeesNoAdminId failed, fsUserVO[{}], args[{}], employeeIds[{}], result[{}]", fsUserVO, args, employeeIds, employeesNoAdminId);
                } else {
                    if (!CollectionUtils.isEmpty(employeesNoAdminId.getResult())){
                        employeesNoAdminId.getResult().forEach(employee -> employeeMap.put(employee.getEmployeeId(), employee));
                    }
                }
            }
            // 上线消息数
            Map<String, Long> msgNumMap = new HashMap<>();
            MessageExhibitionResult<Map<String, Long>> mapMessageExhibitionResult = messageExhibitionService.countSendNumBySender(args.getAppId(), fsUserVO.getEnterpriseAccount(), wxOpenIdList);
            if (!mapMessageExhibitionResult.isSuccess()){
                logger.warn("countSendNumBySender failed, fsUserVO[{}], args[{}], wxOpenIdList[{}], result[{}]", fsUserVO, args, wxOpenIdList, mapMessageExhibitionResult);
            }else{
                msgNumMap.putAll(mapMessageExhibitionResult.getData());
            }

            result.setData(weChatUserBindInfoResultPager.getData().stream().map(weChatUserBindInfoResult -> {
                OuterContactsVO outerContactsVO = new OuterContactsVO();
                outerContactsVO.setWxOpenId(weChatUserBindInfoResult.getWxOpenId());
                outerContactsVO.setWxUserName(weChatUserBindInfoResult.getNickName());
                outerContactsVO.setWxAvatar(weChatUserBindInfoResult.getHeadImgUrl());
                outerContactsVO.setUserTags(weChatUserBindInfoResult.getTagList());
                outerContactsVO.setMsgNum(0L);
                if (null != msgNumMap.get(weChatUserBindInfoResult.getWxOpenId())){
                    outerContactsVO.setMsgNum(msgNumMap.get(weChatUserBindInfoResult.getWxOpenId()));
                }
                if (!CollectionUtils.isEmpty(weChatUserBindInfoResult.getFsUserList())) {
                    outerContactsVO.setCommissioners(weChatUserBindInfoResult.getFsUserList().stream().map(user->{
                        int userId = (int)user.getFsUserId();
                        Employee employee = employeeMap.get(userId);
                        if (null != employee){
                            return new CommissionerVO(userId, employee.getName());
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList()));
                }
                outerContactsVO.setLastModifiedTime(weChatUserBindInfoResult.getLastMsgTime());
                return outerContactsVO;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public void saveServiceCommissioners(FsUserVO fsUserVO, SaveServiceCommissionersArgs args) {
        // 查询绑定关系.
        OuterServiceWechatVO outerServiceWechatVO = outerServiceManager.queryOuterServiceWechat(fsUserVO, null, args.getAppId(), CommonConstant.VALID);
        if (null == outerServiceWechatVO){
            throw new BizException(AjaxCode.BIZ_EXCEPTION,"服务号不存在"); // ignoreI18n
        }
        List<Long> userIds = args.getUserIds().stream().map(Integer::longValue).collect(Collectors.toList());
        //加载原先设置的服务专员.
        QueryBindInfoVo queryBindInfoVo = new QueryBindInfoVo();
        queryBindInfoVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        queryBindInfoVo.setWxAppId(outerServiceWechatVO.getWxAppId());
        queryBindInfoVo.setWxOpenIdList(Lists.newArrayList(args.getWxOpenId()));
        ModelResult<List<WechatUserBindFsResult>> listModelResult = wechatUserBindInfoService.queryBindFsUser(queryBindInfoVo);
        if (!listModelResult.isSuccess() || CollectionUtils.isEmpty(listModelResult.getResult())){
            logger.warn("queryBindFsUser failed, fsUserVO[{}], args[{}], queryBindInfoVo[{}], result[{}]", fsUserVO, args, queryBindInfoVo, listModelResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询绑定关系失败"); // ignoreI18n
        }
        List<Long> srcUserIds = listModelResult.getResult().get(0).getFsUserList().stream().map(FSUserVO::getFsUserId).collect(Collectors.toList());

        List<Long> delUserIds = srcUserIds.stream().filter(userId -> !userIds.contains(userId)).collect(Collectors.toList());
        List<Long> addUserIds = userIds.stream().filter(userId -> !srcUserIds.contains(userId)).collect(Collectors.toList());
        //删除服务专员
        if (!CollectionUtils.isEmpty(delUserIds)) {
            ModifyBindInfoVo modifyBindInfoVo = new ModifyBindInfoVo();
            modifyBindInfoVo.setWxAppId(outerServiceWechatVO.getWxAppId());
            modifyBindInfoVo.setWxOpenId(args.getWxOpenId());
            modifyBindInfoVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
            modifyBindInfoVo.setFsUserIdList(delUserIds);
            ModelResult<Boolean> booleanModelResult = wechatUserBindInfoService.removeBindInfo(modifyBindInfoVo);
            if (!booleanModelResult.isSuccess() || !booleanModelResult.getResult()) {
                logger.warn("removeBindInfo failed, fsUser[{}], args[{}], modifyBindInfoVo[{}], result[{}]",
                        fsUserVO, args, modifyBindInfoVo, booleanModelResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "修改服务专员失败"); // ignoreI18n
            }

            QueryBindInfoVo queryBindInfoArgs = new QueryBindInfoVo();
            queryBindInfoArgs.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
            queryBindInfoArgs.setWxAppId(outerServiceWechatVO.getWxAppId());
            queryBindInfoArgs.setWxOpenIdList(Collections.singletonList(args.getWxOpenId()));
            Pager<WechatUserBindInfoResult> pager = new Pager<>();
            pager.setCurrentPage(1);
            pager.setPageSize(1);
            ModelResult<Pager<WechatUserBindInfoResult>> pagerModelResult = wechatUserBindInfoService.queryWechatUserByPage(pager, queryBindInfoArgs);
            if (!pagerModelResult.isSuccess() && CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
                logger.error("queryWeChatUserByPage failed, queryBindInfoArgs[{}], args[{}], pagerModelResult[{}]",
                        queryBindInfoArgs, args, pagerModelResult);
            } else {
                String wxNickName = pagerModelResult.getResult().getData().get(0).getNickName();
                String promptContent = String.format("您已不是%1$s的服务专员", wxNickName); // ignoreI18n
                List<Integer> receivers = delUserIds.stream().map(Long::intValue).collect(Collectors.toList());
                MessageResult messageResult = sendMessageService.sendWechatPromptTextMessage(args.getAppId(), fsUserVO.getEnterpriseAccount(), args.getWxOpenId(),promptContent , receivers);
                if (!messageResult.isSuccess()) {
                    logger.error("sendWechatPromptTextMessage failed,args[{}], promptContent[{}], toUsers[{}],result[{}]", args, promptContent, delUserIds, messageResult);
                }
            }
        }
        //添加服务专员.
        if (!CollectionUtils.isEmpty(addUserIds)) {
            ModifyBindInfoVo modifyBindInfoVo = new ModifyBindInfoVo();
            modifyBindInfoVo.setWxAppId(outerServiceWechatVO.getWxAppId());
            modifyBindInfoVo.setWxOpenId(args.getWxOpenId());
            modifyBindInfoVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
            modifyBindInfoVo.setFsUserIdList(addUserIds);
            ModelResult<Boolean> booleanModelResult = wechatUserBindInfoService.addBindInfo(modifyBindInfoVo);
            if (!booleanModelResult.isSuccess() || !booleanModelResult.getResult()) {
                logger.warn("addBindInfo failed, fsUser[{}], args[{}], modifyBindInfoVo[{}], result[{}]",
                        fsUserVO, args, modifyBindInfoVo, booleanModelResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "修改服务专员失败"); // ignoreI18n
            }
            List<Integer> addUserIdIntegers = addUserIds.stream().map(Long::intValue).collect(Collectors.toList());
            sendWorkbenchMessage(fsUserVO, args.getAppId(), outerServiceWechatVO.getWxAppId(), args.getWxOpenId(), addUserIdIntegers) ;
        }
    }

    private void sendWorkbenchMessage(FsUserVO fsUserVO, String appId, String  wxAppId, String wxOpenId, List<Integer> userIds) {
        QueryBindInfoVo queryBindInfoArgs = new QueryBindInfoVo();
        queryBindInfoArgs.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        queryBindInfoArgs.setWxAppId(wxAppId);
        queryBindInfoArgs.setWxOpenIdList(Collections.singletonList(wxOpenId));
        Pager<WechatUserBindInfoResult> pager = new Pager<>();
        pager.setCurrentPage(1);
        pager.setPageSize(1);
        ModelResult<Pager<WechatUserBindInfoResult>> pagerModelResult = wechatUserBindInfoService.queryWechatUserByPage(pager, queryBindInfoArgs);
        if (!pagerModelResult.isSuccess() && CollectionUtils.isEmpty(pagerModelResult.getResult().getData())){
            logger.error("queryWechatUserByPage failed,  fsUserVO[{}], appId[{}], wxAppId[{}], wxOpenId[{}], userIds[{}], result[{}]",
                    fsUserVO, appId, wxAppId, wxOpenId, userIds, pagerModelResult);
            return;
        }

        String wxNickName = pagerModelResult.getResult().getData().get(0).getNickName();

        //3.发送消息
        WorkbenchMessageVO workbenchMessageVO = new WorkbenchMessageVO();
        workbenchMessageVO.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        workbenchMessageVO.setAppId(appId);
        workbenchMessageVO.setSessionType(1);  //1-微信客服工作台
        workbenchMessageVO.setWorkbenchType(5);
        workbenchMessageVO.setSenderId(fsUserVO.getUserId());
        workbenchMessageVO.setSenderOpenId("");
        workbenchMessageVO.setLastSummary(String.format(ConfigCenter.OUTER_CONTACTS_LAST_SUMMARY, wxNickName));
        workbenchMessageVO.setCustomServiceRepresentiveIDs(userIds);
        CustomerSessionResult<Void> result = msgCustomerService.sendWorkbenchMessage(workbenchMessageVO);
        if (!result.isSuccess()) {
            logger.error("sendWorkbenchMessage failed. workbenchMessageVO[{}], result[{}]", workbenchMessageVO, result);
        }
    }
}
