package com.facishare.open.app.center.controller;

import com.facishare.open.app.ad.api.enums.AppAdBannerTypeEnum;
import com.facishare.open.app.ad.api.model.FsAdAppBannerDO;
import com.facishare.open.app.ad.api.service.AppCenterBannerService;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * banner位接口
 *
 * <AUTHOR>
 * @date 2015年8月3日
 */
@Controller
@RequestMapping("/open/appcenter/banner")
public class FsBannerController extends BaseController {

    @Resource
    private AppCenterBannerService appCenterBannerService;

    /**
     * 查询banner位数据.
     *
     * @param user 操作人.
     * @return 是否成功.
     */
    @RequestMapping("/queryWebBanners")
    @ResponseBody
    public AjaxResult queryWebBanners(@ModelAttribute FsUserVO user) {
        BaseResult<List<FsAdAppBannerDO>> result = appCenterBannerService.queryAppBanners(AppAdBannerTypeEnum.WEB);
        if (!result.isSuccess() || null == result.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "拉取banner失败"); // ignoreI18n
        }
        return new AjaxResult(result.getResult());
    }
}
