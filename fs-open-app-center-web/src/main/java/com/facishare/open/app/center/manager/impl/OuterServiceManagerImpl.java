package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.*;
import com.facishare.open.app.center.api.model.vo.OpenCustomerVO;
import com.facishare.open.app.center.api.model.vo.OuterServiceWechatVO;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.IntegerResult;
import com.facishare.open.app.center.api.service.AppIconService;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.api.service.OpenFsUserBindAppService;
import com.facishare.open.app.center.api.service.outer.OpenCustomerService;
import com.facishare.open.app.center.api.service.outer.OuterServiceWechatService;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.cons.ServiceFeatureTypeEnum;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.center.model.AppCreateForm;
import com.facishare.open.app.center.model.outers.OuterServiceDashboardStatisticsVO;
import com.facishare.open.app.center.model.outers.OuterServiceForm;
import com.facishare.open.app.center.mq.item.AppOrServiceCreateItem;
import com.facishare.open.app.center.mq.item.tags.AppCenterMqTagsConstant;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.autoreplymsg.model.WorkbenchSessionVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.utils.EncodingAesKeyUtil;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.facishare.open.oauth.model.arg.AppArg;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.oauth.result.CommonResult;
import com.facishare.open.oauth.result.CreateAppResult;
import com.facishare.open.oauth.service.AppService;
import com.facishare.open.oauth.service.EaAuthService;
import com.facishare.open.operating.center.api.service.OperatingAppMessageService;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.constants.BusinessType;
import com.facishare.wechat.proxy.model.auth.BindInfo;
import com.facishare.wechat.proxy.model.vo.QueryBindInfoVo;
import com.facishare.wechat.proxy.service.WechatAuthService;
import com.facishare.wechat.proxy.service.WechatUserBindInfoService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 * User: zhouq
 * Date: 2016/11/1
 */
@Service
public class OuterServiceManagerImpl implements OuterServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(OuterServiceManagerImpl.class);
    //mq日志分开打印
    private Logger MQ_LOG = LoggerFactory.getLogger("CENTER_MQ_LOG");

    @Resource
    private OuterServiceWechatService outerServiceWechatService;

    @Resource
    private WechatAuthService wechatAuthService;

    @Resource
    private WechatUserBindInfoService wechatUserBindInfoService;

    @Resource
    private AppManager appManager;

    @Resource
    private ServiceNumberManager serviceNumberManager;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private OpenFsUserBindAppService openFsUserBindAppService;

    @Resource
    private WebAuthManager webAuthManager;

    @Resource
    private DictManager dictManager;

    @Resource
    private OpenAppAdminService openAppAdminService;

    @Resource
    private MessageExhibitionService messageExhibitionService;

    @Resource
    private OpenCustomerService openCustomerService;

    @Resource
    private AppMessageToMqManager appMessageToMqManager;

    @Resource
    private AppService appService;

    @Resource
    private AppIconService appIconService;

    @Resource
    private EaAuthService eaAuthService;

    @Resource
    private MsgAutoReplyService msgAutoReplyService;

    @Resource
    private MsgCustomerService msgCustomerService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private OperatingAppMessageService operatingAppMessageService;

    @Override
    public OuterServiceWechatVO queryOuterServiceWechat(FsUserVO user, String wxAppId, String appId, Integer status) {
        OuterServiceWechatVO entity = new OuterServiceWechatVO();
        entity.setAppId(appId);
        entity.setWxAppId(wxAppId);
        entity.setFsEa(user.getEnterpriseAccount());
        entity.setStatus(status);
        BaseResult<OuterServiceWechatVO> result = outerServiceWechatService.queryOuterServiceWechat(entity);
        if (!result.isSuccess()) {
            logger.warn("failed to call queryOuterServiceWechat, OuterServiceWechatVO[{}], result[{}]", entity, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询外联服务号和微信公众号失败"); // ignoreI18n
        }
        return result.getResult();
    }

    @Override
    public BindInfo queryWechat(FsUserVO user, String wxAppId) {
        ModelResult<BindInfo> modelResult = wechatAuthService.getBindInfoByAppId(wxAppId, BusinessType.BUSI_TYPE_APP_CENTER);
        if (!modelResult.isSuccess() || null == modelResult.getResult()) {
            logger.warn("failed to call getBindInfoByAppId, wxAppId[{}], BusinessType[{}], modelResult[{}]", wxAppId, BusinessType.BUSI_TYPE_APP_CENTER, modelResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询微信公众号信息失败"); // ignoreI18n
        }
        return modelResult.getResult();
    }

    @Override
    public Map<String, Object> createOuterServiceByForm(FsUserVO user, OuterServiceForm form, AppCenterEnum.AppType appType) {
        AppCreateForm appCreateForm = new AppCreateForm();
        appCreateForm.setAppName(form.getAppName());
        appCreateForm.setAppDesc(form.getAppDesc());
        appCreateForm.setAppLogo(form.getAppLogo());
        appCreateForm.setAppAdmins(form.getAppAdmins());

        //1. 创建外联服务号(oauth)
        AppArg appArg = new AppArg();
        appArg.setAppType(AppTypeEnum.OUT_SERVICE_APP);
        appArg.setEncodingAesKey(EncodingAesKeyUtil.generateEncodingAesKey());
        CreateAppResult appResult = appService.createApp(appArg);
        if (!appResult.isSuccess()) {
            logger.warn("call oauth createApp fail appArg [{}], appResult[{}], form[{}]", appArg, appResult, form);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "创建外联服务号失败"); // ignoreI18n
        }
        String appId = appResult.getAppId();
        if (StringUtils.isNotBlank(form.getAppLogo())) {
            processIconByLogo(appId, form.getAppLogo());
        }
        OpenAppDO app = new OpenAppDO();
        app.setAppId(appId);
        app.setAppName(form.getAppName());
        app.setAppDesc(form.getAppDesc());
        app.setServiceName(app.getAppName());
        app.setCreaterType(CommonConstant.APP_CREATER_FS_ACCOUNT);
        app.setAppCreater(user.getEnterpriseAccount());
        app.setStatus(AppStatus.ON_LINE.status());
        app.setAppType(appType.value());
        app.setAppMode(CommonConstant.APP_MODE_COMMON);
        app.setAppClass(CommonConstant.APP_CLASS_CUSTOM);
        app.setPayType(PayTypeEnum.FREE.getPayType());
        app.setGmtCreate(new Date());
        app.setGmtModified(new Date());
        //2. 创建外联服务号(center) && 创建企信的session头像
        AppResult createResult = openAppService.createOpenApp(app);
        if (!createResult.isSuccess()) {
            logger.warn("create outer service by appcenter fail, result:{}", createResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, createResult, "创建外联服务号失败"); // ignoreI18n
        }
        //3. 保存外联服务号管理员
        List<String> newAppAdminIds = new ArrayList<>();
        for (Integer userId : form.getAppAdmins()) {
            newAppAdminIds.add(new FsUserVO(user.getEnterpriseAccount(), userId).asStringUser());
        }
        com.facishare.open.app.center.api.result.BaseResult<Void> adminResult = openAppAdminService.updateAppAdminIds(user, appId, newAppAdminIds);
        if (!adminResult.isSuccess()) {
            logger.error("failed to updateAppAdminIds , user={}, appId={}, newAppAdminIds={}, result={}",
                    user, appId, newAppAdminIds, adminResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, adminResult, "保存应用管理员失败"); // ignoreI18n
        }
        //4. 企业对外联服务号授权（管理员可见应用 && 应用可访问纷享企业的scope组）
        CommonResult saveResult = eaAuthService.saveEaAuth(user.asStringUser(), null,
                user.getEnterpriseAccount(), appId, Lists.newArrayList());
        if (!saveResult.isSuccess()) {
            logger.error("eaAuthService.saveEaAuth fail , result:{}", saveResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, saveResult, saveResult.getErrDescription());
        }
        //5. 需要新增服务号工作标题，管理员描述。客服描述字段。推送给企信
//        modifyServiceWorkBeanchSession(user, new OpenAppDO(appId, form.getAppName()), Lists.newArrayList(form.getAppAdmins()));
        modifyServiceWorkBeanchSession(user, app, Lists.newArrayList());
        //6. 更新微信客服二级session
        updateWorkbenchSession(user, appId);
        //7. 开启微信客服开关
        MsgBaseResult msgBaseResult = msgAutoReplyService.setCustomServiceReplySwitchWithType(user.getEnterpriseAccount(), appId, CommonConstant.SERVICE_NUMBER_ON, CommonConstant.IS_OUTER_SERVICE);
        if (!msgBaseResult.isSuccess()) {
            logger.warn("failed to call msgAutoReplyService.setCustomServiceReplySwitch, enterpriseAccount={}, appId={}, result={}",
                    user.getEnterpriseAccount(), appId, msgBaseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置微信客服开关失败"); // ignoreI18n
        }
        //8. 保存外联服务号和微信公众号绑定关系
        OuterServiceWechatVO outerServiceWechatVO = new OuterServiceWechatVO();
        outerServiceWechatVO.setAppId(appId);
        outerServiceWechatVO.setStatus(CommonConstant.VALID);
        outerServiceWechatVO.setFsEa(user.getEnterpriseAccount());
        outerServiceWechatVO.setWxAppId(form.getWxAppId());
        outerServiceWechatVO.setGmtCreate(new Date());
        BaseResult<Boolean> addResult = outerServiceWechatService.addOuterServiceWechat(outerServiceWechatVO);
        if (!addResult.isSuccess()) {
            logger.warn("failed to addOuterServiceWechat, outerServiceWechatVO[{}], result[{}]", outerServiceWechatVO, addResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "保存外联服务号和微信公众号绑定关系失败"); // ignoreI18n
        }
        //9. 回调设置微信公众号和外联服务号的绑定关系
        ModelResult<Boolean> bindResult = wechatAuthService.confirmBindForOpen(user.getEnterpriseAccount(), form.getWxAppId(), appId);
        if (!bindResult.isSuccess()) {
            logger.warn("failed to confirmBindForOpen, fsEa[{}], wxAppId[{}], appId[{}], result[{}]", user.getEnterpriseAccount(), form.getWxAppId(), appId, bindResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "保存外联服务号和微信公众号绑定关系失败"); // ignoreI18n
        }
        //10. 设置管理员为微信客服主管
        OpenCustomerVO openCustomerVO = new OpenCustomerVO();
        openCustomerVO.setAppId(appId);
        openCustomerVO.setWxAppId(form.getWxAppId());
        openCustomerVO.setFsEa(user.getEnterpriseAccount());
        openCustomerVO.setRole(OpenCustomerRoleEnum.MANAGER.getCode());
        openCustomerVO.setSrcType(OpenCustomerSrcTypeEnum.BY_HAND.getCode());
        BaseResult<Void> createOpenCustomersResult = openCustomerService.createOpenCustomers(user, openCustomerVO, Lists.newArrayList(form.getAppAdmins()));
        if (!createOpenCustomersResult.isSuccess()) {
            logger.warn("failed to createOpenCustomers, user[{}], openCustomerVO[{}], userIds[{}], result[{}]", user, openCustomerVO, form.getAppAdmins(), createOpenCustomersResult);
        }
        //11. 发送消息到mq
        appOrServiceCreateToMq(form.getAppName(), appType, user, appId);
        //12. 通知更新外联服务号列表
        notifyOuterServiceModified(user, appId);
        Map<String, Object> result = new HashMap<>();
        result.put("appId", appId);
        return result;
    }

    @Override
    public Map<String, Object> loadOuterServiceByAppId(FsUserVO user, String appId) {
        final boolean isFsAdmin = webAuthManager.isFsAdmin(user);
        boolean isAppAdmin = webAuthManager.isAppAdmin(user, appId);
        if (!isFsAdmin && !isAppAdmin) {
            logger.warn("user is neither admin nor app admin, user={}, appId={}", user, appId);
            throw new BizException(AjaxCode.NO_AUTHORITY, "权限不足！"); // ignoreI18n
        }

        AppResult appResult = openAppService.loadOpenApp(appId);
        if (!appResult.isSuccess()) {
            logger.warn("failed to call loadOpenApp, user={}, appId={}, result={}", user, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载外联服务号详情失败"); // ignoreI18n
        }

        OpenAppDO openAppDO = appResult.getResult();
        //获取启用停用状态
        IntegerResult integerResult = openFsUserBindAppService.queryAppBindStatus(user, appId);
        if (integerResult.isSuccess()) {
            openAppDO.setBindStatus(integerResult.getResult());
        } else {
            logger.error("queryAppBindStatus error , user[{}], appId[{}], result[{}]", user, appId, integerResult);
        }
        Map<String, Object> ajaxDataMap = Maps.newHashMap();
        try {
            OuterServiceWechatVO outerServiceWechatVO = queryOuterServiceWechat(user, null, appId, CommonConstant.VALID);
            if (null != outerServiceWechatVO) {
                ajaxDataMap.put("isBindFromWechat", CommonConstant.YES);
                ajaxDataMap.put("bindInfo", queryWechat(user, outerServiceWechatVO.getWxAppId()));
            } else {
                ajaxDataMap.put("isBindFromWechat", CommonConstant.NO);
                ajaxDataMap.put("bindInfo", null);
            }
        } catch (Exception e) {
            logger.warn("failed to call queryWechat, user[{}], appId[{}]", user, appId, e);
        }

        ajaxDataMap.put("appId", appId);
        String logoUrl = appManager.queryAppIconUrl(openAppDO.getAppId(), IconType.WEB);
        ajaxDataMap.put("appLogoUrl", logoUrl);
        ajaxDataMap.put("serviceLogoUrl", logoUrl);
        ajaxDataMap.put("appName", openAppDO.getAppName());
        ajaxDataMap.put("appDesc", openAppDO.getAppDesc());
        ajaxDataMap.put("appType", openAppDO.getAppType());
        ajaxDataMap.put("status", openAppDO.getStatus());
        ajaxDataMap.put("bindStatus", openAppDO.getBindStatus());
        ajaxDataMap.put("appMode", openAppDO.getAppMode());
        ajaxDataMap.put("appModeName", dictManager.loadValueByKey(CommonConstant.DICT_TYPE_APP_MODE, openAppDO.getAppMode() + ""));
        ajaxDataMap.put("serviceName", openAppDO.getServiceName());
        ajaxDataMap.put("dev", openAppDO.getOpenDevDO());
        ajaxDataMap.put("appClass", openAppDO.getAppClass());
        ajaxDataMap.put("appClassName", dictManager.loadValueByKey(CommonConstant.DICT_TYPE_APP_CLASS, openAppDO.getAppClass() + ""));

        ajaxDataMap.put("appAdminIds", getAppAdminIds(user, appId));
        ajaxDataMap.put("isFsAdmin", isFsAdmin ? 1 : 0);
        ajaxDataMap.put("isThisAppAdmin", isAppAdmin ? 1 : 0);

        return ajaxDataMap;
    }

    /**
     * 发送Mq消息
     * @param appName
     * @param appType
     * @param user
     * @param appId
     */
    private void appOrServiceCreateToMq(String appName, AppCenterEnum.AppType appType, FsUserVO user, String appId) {
        CommonThreadPoolUtils.getExecutor().execute(() -> {
            AppOrServiceCreateItem appOrServiceCreateItem = new AppOrServiceCreateItem();
            appOrServiceCreateItem.setEnterpriseAccount(user.getEnterpriseAccount());
            appOrServiceCreateItem.setAppName(appName);
            appOrServiceCreateItem.setAppId(appId);
            appOrServiceCreateItem.setUserId(user.getUserId());
            appOrServiceCreateItem.setAppType(appType.value());
            appOrServiceCreateItem.setOperationTime(new Date());
            operatingAppMessageService.appOrServiceCreate(appOrServiceCreateItem);
        });
    }


    /**
     * 服务号工作标题，管理员描述。客服描述字段。推送给企信
     *
     * @param fsAdminUser 用户
     * @param openAppDO   应用信息
     * @param appAdminIds 应用管理员
     * @return boolean
     */
    private boolean modifyServiceWorkBeanchSession(FsUserVO fsAdminUser, OpenAppDO openAppDO, List<Integer> appAdminIds) {
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setAdmins(appAdminIds);
        platformMetaSessionVO.setCustomerSessionName(openAppDO.getServiceName());
        platformMetaSessionVO.setCustomerSessionSubName(CenterConstants.CUSTOMER_SESSION_SUB_NAME);
        platformMetaSessionVO.setCustomerSessionPortrait(appManager.queryAppIconUrl(openAppDO.getAppId(), IconType.WEB));
        platformMetaSessionVO.setAdminDescription(openAppDO.getAppDesc());
        platformMetaSessionVO.setCustomerDescription(openAppDO.getAppDesc());
        return serviceNumberManager.modifyServiceWorkBenchSession(fsAdminUser, openAppDO.getAppId(), platformMetaSessionVO, ServiceTypeEnum.CREATE, CommonConstant.IS_OUTER_SERVICE);
    }

    /**
     * 更新微信客服二级session
     */
    private void updateWorkbenchSession(FsUserVO fsAdminUser, String appId) {
        // 添加微客服.
        WorkbenchSessionVO customerSession = new WorkbenchSessionVO();
        customerSession.setAppId(appId);
        customerSession.setEnterpriseAccount(fsAdminUser.getEnterpriseAccount());
        customerSession.setSessionIcon(ConfigCenter.ADD_OPEN_CUSTOMER_ICON);
        customerSession.setSessionName("微信客服"); // ignoreI18n
        customerSession.setSessionType(1);    //1-微信客服
        customerSession.setWorkbenchType(4);  //4-微信客服
        customerSession.setUrl(String.format(ConfigCenter.ADD_OPEN_CUSTOMER_NOTICE_URL, appId));   //要+http
        CustomerSessionResult<Boolean> customerResult = msgCustomerService.updateWorkbenchSession(customerSession);
        if (null == customerResult || !customerResult.isSuccess()) {
            logger.error("updateWorkbenchSession failed, fsAdminUser[{}], customerSession[{}], result[{}]", fsAdminUser, customerSession, customerResult);
        }
        // 添加外部联系人的session.
        WorkbenchSessionVO outerContacts = new WorkbenchSessionVO();
        outerContacts.setAppId(appId);
        outerContacts.setEnterpriseAccount(fsAdminUser.getEnterpriseAccount());
        outerContacts.setSessionIcon(ConfigCenter.OUTER_CONTACTS_ICON);
        outerContacts.setSessionName("外部联系人"); // ignoreI18n
        outerContacts.setSessionType(1);    //1-微客服
        outerContacts.setWorkbenchType(5);  //5-外部联系人
        outerContacts.setUrl("fs://outerService/contacts");
        CustomerSessionResult<Boolean> outerContactsResult = msgCustomerService.updateWorkbenchSession(outerContacts);
        if (null == outerContactsResult || !outerContactsResult.isSuccess()) {
            logger.error("updateWorkbenchSession failed, fsAdminUser[{}], outerContacts[{}], result[{}]", fsAdminUser, outerContacts, outerContactsResult);
        }
    }

    /**
     * 获取应用管理员ID列表
     *
     * @param user  用户
     * @param appId 应用Id
     * @return List<String>
     */
    private List<String> getAppAdminIds(FsUserVO user, String appId) {
        com.facishare.open.app.center.api.result.BaseResult<List<String>> openAppAdminIdsResult =
                openAppAdminService.queryAppAdminIdList(user.getEnterpriseAccount(), appId);
        if (!openAppAdminIdsResult.isSuccess()) {
            logger.warn("failed to call queryAppAdminIdList, user[{}], appId[{}], result[{}]",
                    user, appId, openAppAdminIdsResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询外联服务号管理员列表失败"); // ignoreI18n
        }
        return openAppAdminIdsResult.getResult().stream()
                .map(adminId -> String.valueOf(new FsUserVO(adminId).getUserId()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> queryOuterServiceFeatureList(FsUserVO user, String appId) {
        Map<String, Object> result = new HashMap<>();
        List<String> basicList = new ArrayList<>();
        basicList.add(ServiceFeatureTypeEnum.WECHAT_MSG.getType());
        basicList.add(ServiceFeatureTypeEnum.SETTING.getType());
        result.put("basic", basicList);

        List<String> extensionList = new ArrayList<>();
        extensionList.add(ServiceFeatureTypeEnum.CUSTOM_MENU.getType());
        extensionList.add(ServiceFeatureTypeEnum.WECHAT_CUSTOMER.getType());
        extensionList.add(ServiceFeatureTypeEnum.OUTER_CONTACTS.getType());
        result.put("extensions", extensionList);
        return result;
    }

    @Override
    public OuterServiceDashboardStatisticsVO queryOuterServiceDashboardStatistics(FsUserVO user, String appId, String wxAppId) {
        OuterServiceDashboardStatisticsVO statisticsVO = new OuterServiceDashboardStatisticsVO();
        statisticsVO.setAppId(appId);
        QueryBindInfoVo queryBindInfoVo = new QueryBindInfoVo();
        queryBindInfoVo.setWxAppId(wxAppId);
        queryBindInfoVo.setEnterpriseAccount(user.getEnterpriseAccount());
        ModelResult<Long> modelResult = wechatUserBindInfoService.queryWechatUserAmount(queryBindInfoVo);
        if (!modelResult.isSuccess()) {
            logger.warn("failed to call queryWechatUserAmount, queryBindInfoVo[{}], result[{}]", queryBindInfoVo, modelResult);
        } else {
            statisticsVO.setWechatUserAmount(modelResult.getResult());
        }
        MessageExhibitionResult<Long> messageExhibitionResult = messageExhibitionService.countReceiveNumByAppId(user.getEnterpriseAccount(), appId);
        if (!messageExhibitionResult.isSuccess()) {
            logger.warn("failed to call countSendNumByOpenID, fsea[{}], appid[{}], result[{}]", user.getEnterpriseAccount(), appId, modelResult);
        } else {
            statisticsVO.setWechatMsgAmount(messageExhibitionResult.getData());
        }
        statisticsVO.setDataEndTime(new Date().getTime());
        return statisticsVO;
    }

    @Override
    public boolean unBindWechat(FsUserVO user, String lang, OuterServiceWechatVO outerServiceWechatVO) {
        String wxAppId = outerServiceWechatVO.getWxAppId();
        //解除wechat（丁成）微信公众号授权绑定状态
        ModelResult<Boolean> bindResult = wechatAuthService.unbindToAppId(user.getEnterpriseAccount(), wxAppId);
        if (!bindResult.isSuccess()) {
            logger.warn("failed to unbindToAppId, fsea[{}], wxAppId[{}], result[{}]", user.getEnterpriseAccount(), wxAppId, bindResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "微信公众号取消授权失败!"); // ignoreI18n
        }

        OuterServiceWechatVO entity = new OuterServiceWechatVO();
        entity.setAppId(outerServiceWechatVO.getAppId());
        entity.setWxAppId(wxAppId);
        entity.setFsEa(user.getEnterpriseAccount());
        entity.setStatus(CommonConstant.INVALID);
        entity.setUnbindSource(CommonConstant.UNBIND_FROM_OUTER_SERVICE);
        BaseResult<Boolean> result = outerServiceWechatService.updateOuterServiceWechat(entity);
        if (!result.isSuccess()) {
            logger.warn("failed to call updateOuterServiceWechat, OuterServiceWechatVO[{}], result[{}]", entity, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "微信公众号取消授权失败"); // ignoreI18n
        }
        // 增加取消授权操作日志
        addBindLogToMq(user, lang, wxAppId, outerServiceWechatVO.getAppId());
        return true;
    }

    private void addBindLogToMq(FsUserVO user, String lang, String wxAppId, String appId) {
        try {
            AppResult appResult = openAppService.loadOpenAppFast(appId);
            if (!appResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "外联服务号不存在"); // ignoreI18n
            }
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                    user.getUserId(), System.currentTimeMillis()+ "", "取消了微信公众号的授权", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        } catch (Exception e) {
            logger.warn("failed to call unBindWechatToMq: user={}, wxAppId={}, switchType={}, appId={}, tags={}",
                    user, wxAppId, CommonConstant.CANCEL_AUTHORIZATION_OUTER_SERVICE, appId, AppCenterMqTagsConstant.BIND_OUTER_SERVICE_TAGS, e);
        }
    }

    @Override
    public boolean checkWechat(FsUserVO user, String appId, String wxAppId, List<Integer> permissionTypes) {
        if (CollectionUtils.isEmpty(permissionTypes) && Strings.isNullOrEmpty(appId)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "参数错误"); // ignoreI18n
        }
        OuterServiceWechatVO entity = new OuterServiceWechatVO();
        entity.setAppId(appId);
        entity.setFsEa(user.getEnterpriseAccount());
        BaseResult<OuterServiceWechatVO> result = outerServiceWechatService.queryOuterServiceWechat(entity);
        if (!result.isSuccess() || null == result.getResult()) {
            logger.warn("failed to call queryOpenServiceExternalWechat, OuterServiceWechatVO[{}], result[{}]", entity, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "该外联服务号没有绑定微信公众号！"); // ignoreI18n
        }
        String oldWxAppId = result.getResult().getWxAppId();
        if (!Strings.isNullOrEmpty(wxAppId) && !wxAppId.equals(oldWxAppId)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "微信公众号参数错误"); // ignoreI18n
        }
        Integer unbindSource = result.getResult().getUnbindSource();
        if (null != unbindSource) {
            if (CommonConstant.UNBIND_FROM_WECHAT == unbindSource) {
                throw new BizException(AjaxCode.UNBIND_OR_SUBSCRIBE_EXCEPTION, ConfigCenter.UNBIND_FROM_WECHAT_TOOLTIP);
            } else if (CommonConstant.UNBIND_FROM_OUTER_SERVICE == unbindSource) {
                throw new BizException(AjaxCode.UNBIND_OR_SUBSCRIBE_EXCEPTION, ConfigCenter.UNBIND_FROM_OUTER_SERVICE_TOOLTIP);
            }
        }
        ModelResult<BindInfo> modelResult = wechatAuthService.getBindInfoByAppId(oldWxAppId, BusinessType.BUSI_TYPE_APP_CENTER);
        if (!modelResult.isSuccess() || null == modelResult.getResult()) {
            logger.warn("failed to call getBindInfoByAppId, wechatAppId[{}], BusinessType[{}], modelResult[{}]", oldWxAppId, BusinessType.BUSI_TYPE_APP_CENTER, modelResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询微信公众号信息失败"); // ignoreI18n
        }
        BindInfo bindInfo = modelResult.getResult();
        if (CommonConstant.WECHAT_SERVICE == bindInfo.getType()) {
            if (CommonConstant.WECHAT_IS_UNAUTHERIZED == bindInfo.getVerifyType()) { //未认证的服务号
                throw new BizException(AjaxCode.UNAUTHERIZED_EXCEPTION, ConfigCenter.WECHAT_IS_UNAUTHERIZED_TOOLTIP);
            }
            List<Integer> allPermissionTypes = bindInfo.getPermission();
            if (!CollectionUtils.isEmpty(allPermissionTypes) && !allPermissionTypes.containsAll(permissionTypes)) { //验证是否有此项功能的权限
                throw new BizException(AjaxCode.UNBIND_OR_SUBSCRIBE_EXCEPTION, ConfigCenter.WECHAT_UNBIND_PERMISSON_TOOLTIP);
            }
        } else {//订阅号
            throw new BizException(AjaxCode.UNBIND_OR_SUBSCRIBE_EXCEPTION, ConfigCenter.WECHAT_SUBSCRIBE_TOOLTIP);
        }
        return true;
    }

    private void processIconByLogo(String appId, String appLogo) {
        List<IconType> types = Collections.singletonList(IconType.LOGO);
        String masterId;
        if(appLogo.contains("group1/M00") || appLogo.contains("group0/M00")){
            masterId = appLogo.substring(appLogo.indexOf("=group") + 1);
            if (masterId.contains("&")) {
                masterId = masterId.substring(0, masterId.indexOf("&"));
            }
        } else {//新逻辑走北京图片url
            masterId = appLogo.substring(appLogo.indexOf("=") + 1);
            if (masterId.contains("&")) {
                masterId = masterId.substring(0, masterId.indexOf("&"));
            }
        }

        com.facishare.open.app.center.api.result.BaseResult result = appIconService.update(appId, types, masterId);
        if (!result.isSuccess()) {
            logger.warn("Add icon fail. result: {}.", result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, result.getErrDescription());
        }
    }

    /**
     * 通知手机端重新拉取外联服务号列表
     * @param user  用户
     * @param appId 外联服务号id
     */
    public void notifyOuterServiceModified(FsUserVO user, String appId) {
        BaseResult<Void> baseResult = openCustomerService.notifyOuterServiceModified(user, appId);
        if (!baseResult.isSuccess()) {
            logger.warn("failed to call notifyOuterServiceModified, user=[{}], appId={}, result={}", user, appId, baseResult.toString());
        }
    }

    @Override
    public Map<String, Object> queryAppNameAndBindStatus(FsUserVO user, String appId) {
        Map<String, Object> ajaxDataMap = Maps.newHashMap();

        //查询关联状态
        OuterServiceWechatVO entity = new OuterServiceWechatVO();
        entity.setAppId(appId);
        entity.setFsEa(user.getEnterpriseAccount());
        entity.setStatus(CommonConstant.VALID);
        BaseResult<OuterServiceWechatVO> result = outerServiceWechatService.queryOuterServiceWechat(entity);
        if (!result.isSuccess()) {
            logger.warn("outerServiceWechatService.queryOuterServiceWechat failed, OuterServiceWechatVO[{}], result[{}]", entity, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询外联服务号和微信服务号失败"); // ignoreI18n
        }
        OuterServiceWechatVO outerServiceWechatVO = result.getResult();
        if (null == outerServiceWechatVO) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "外联服务号没有关联的微信服务号"); // ignoreI18n
        }

        //查询应用名称
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            logger.warn("openAppService.loadOpenApp failed, user[{}], appId[{}], result[{}]", user, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载外联服务号详情失败"); // ignoreI18n
        }
        OpenAppDO openAppDO = appResult.getResult();
        if (openAppDO == null) {
            logger.warn("openAppService.loadOpenApp app is null, user[{}], appId[{}], result[{}]", user, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "应用不存在"); // ignoreI18n
        }
        if (openAppDO.getStatus() == AppStatus.DELETED.getStatus()) {
            logger.warn("openAppService.loadOpenApp app is deleted, user[{}], appId[{}], result[{}]", user, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "应用已被删除"); // ignoreI18n
        }

        ajaxDataMap.put("appName", openAppDO.getAppName());
        return ajaxDataMap;
    }
}
