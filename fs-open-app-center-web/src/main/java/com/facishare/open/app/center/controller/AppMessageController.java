package com.facishare.open.app.center.controller;

import org.apache.rocketmq.common.message.Message;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.enums.AppStatus;
import com.facishare.open.app.center.api.model.enums.BuriedPointBizEnum;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.api.utils.AppStatLogKit;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.AppMessageManager;
import com.facishare.open.app.center.model.AppMessageVO;
import com.facishare.open.app.center.model.TemplateMessageParam;
import com.facishare.open.app.center.model.cons.CommonConstants;
import com.facishare.open.app.center.mq.item.ServiceReplyMsgItem;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.enums.MessageSendStatType;
import com.facishare.open.common.enums.MonitorTypeEnum;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.material.api.enums.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.common.rocketmq.AutoConfRocketMQSender;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.cloud.utils.JsonUtils;
import com.github.jedis.support.MergeJedisCmd;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * APP消息.
 * Created by xialf on 2015/8/26.
 */
@Controller
@RequestMapping("/open/appcenter/appmsg")
public class AppMessageController extends BaseController {
    //mq日志分开打印
    private Logger MQ_LOG = LoggerFactory.getLogger("CENTER_MQ_LOG");

    @Resource
    private AppMessageManager appMessageManager;

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;

    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;

    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private AutoConfRocketMQSender autoConfRocketMQSender;


    /**
     * 获取企业中可见该APP的AppViewDO（部门IDs，员工IDs）.
     *
     * @param user  操作人.
     * @param appId 应用id
     * @return 返回企业可见该APP的AppViewDO
     */
    @RequestMapping("/queryView")
    @ResponseBody
    public AjaxResult queryView(@ModelAttribute FsUserVO user,
                                @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        //验证应用管理员
        checkAppAdmin(user, appId);
        AppViewDO msgTargetView = appMessageManager.queryTargetView(user, appId);
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("appId", appId);
        retMap.put("view", msgTargetView);
        return new AjaxResult(retMap);

    }

    @RequestMapping("/sendImage")
    @ResponseBody
    public AjaxResult sendImage(@ModelAttribute FsUserVO user, @RequestBody AppMessageVO appMsgForm) {
        checkParamNotBlank(appMsgForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getAppId(), "请选择发送消息的应用"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getSendType(), "请设置消息发送类型"); // ignoreI18n
        checkParamTrue(Objects.equals(appMsgForm.getSendType(), MessageSendTypeEnum.GROUP_SEND_MSG.getCode()), "send type is invalid");
        checkParamTrue(Objects.nonNull(appMsgForm.getImageContentVO()), "image vo is null");
        checkParamTrue(Objects.nonNull(appMsgForm.getImageContentVO().getPath()), "imageContent.path is null");
        checkParamTrue(Objects.nonNull(appMsgForm.getImageContentVO().getName()), "imageContent.name is null");
        checkParamTrue(Objects.nonNull(appMsgForm.getImageContentVO().getExtensionName()), "imageContent.extensionName is null");
        checkParamTrue(Objects.nonNull(appMsgForm.getImageContentVO().getSize()), "imageContent.size is null");
        checkParamTrue(appMsgForm.getImageContentVO().getSize() <= ConfigCenter.GROUP_SEND_IMAGE_MESSAGE_MAX_IMAGE_SIZE, "imageContent.size Over maximum");

        //判断是否是应用管理员
        checkAppAdmin(user, appMsgForm.getAppId());

        //发送消息
        String messageId = appMessageManager.sendImageMessage(user, appMsgForm);

        // 记录发送埋点日志
        writeSendBuriedLog(user, appMsgForm, messageId, MessageTypeEnum.IMAGE);

        return new AjaxResult(messageId);
    }

    private void writeSendBuriedLog(FsUserVO user, AppMessageVO appMsgForm, String messageId, MessageTypeEnum messageType) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("appId", appMsgForm.getAppId());
            AppViewDO targets = JsonKit.json2object(appMsgForm.getTargets(), AppViewDO.class);
            map.put("targetUsers", appMessageManager.convertToUserIds(user, targets));
            map.put("messageSendStatType", MessageSendStatType.APP_CENTER_WEB_ADMIN_SEND.getType());
            map.putAll(JsonUtils.parseToMap(appMsgForm));
            map.put("messageType", messageType.getType());
            if (Objects.equals(MessageTypeEnum.TEXT, messageType)) {
                map.put("action", BuriedPointBizEnum.GROUP_SEND_TEXT_MSG.getAction());
            } else if (Objects.equals(MessageTypeEnum.IMAGE, messageType)) {
                map.put("action", BuriedPointBizEnum.GROUP_SEND_IMAGE_MSG.getAction());
            }
            map.put("messageId", messageId);
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("writeSendBuriedLog failed , user[{}], appMsgForm[{}], messageId[{}]", user, appMsgForm, messageId, e);
        }
    }

    /**
     * 发送应用消息到指定目标.
     * 已知使用业务：服务号群发文本消息，服务号管理员回复用户上行消息
     *
     * @param user       操作人.
     * @param appMsgForm 消息必须信息：目标
     * @return 成功或失败
     */
    @RequestMapping({"/send", "/sendTextMsg"})
    @ResponseBody
    public AjaxResult sendTextMsg(@ModelAttribute FsUserVO user, @RequestBody AppMessageVO appMsgForm) {
        checkParamNotBlank(appMsgForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getAppId(), "请选择发送消息的应用"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getContent(), "请填写消息内容"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getSendType(), "请设置消息发送类型"); // ignoreI18n

        //判断是否是应用管理员
        checkAppAdmin(user, appMsgForm.getAppId());

        //校验消息类型
        MessageSendTypeEnum messageSendType = MessageSendTypeEnum.getByCode(appMsgForm.getSendType());


        String messageId = appMessageManager.sendTextMessage(user, appMsgForm);

        //管理员回复 需要写入MQ
        if (Objects.equals(MessageSendTypeEnum.ADMIN_REPLY_SEND, messageSendType)) {
            serviceReplyMsgToMq(user, appMsgForm.getAppId());
        }

        AppResult appResult = openAppService.loadOpenAppFast(appMsgForm.getAppId());
        if(!appResult.isSuccess() || null == appResult.getResult()){
            throw new BizException(AjaxCode.BIZ_EXCEPTION,appResult,"应用不存在"); // ignoreI18n
        }
        AppStatLogKit.logSendMsg(appMsgForm.getAppId(), MonitorTypeEnum.STATISTICS_SEND_MSG,user.getEnterpriseAccount(),user.getUserId(),appResult.getResult().getAppType(), MessageSendStatType.APP_CENTER_WEB_ADMIN_SEND, MessageTypeEnum.TEXT.getType());

        writeSendBuriedLog(user, appMsgForm, messageId, MessageTypeEnum.TEXT);

        return new AjaxResult(messageId);

    }

    /**
     * 发送应用消息到指定目标.
     *
     * @param user       操作人.
     * @param appMsgForm 消息必须信息：目标
     * @return 成功或失败
     */
    @RequestMapping({"/sendImgTextMsg"})
    @ResponseBody
    public AjaxResult sendImgTextMsg(@ModelAttribute FsUserVO user, @RequestBody AppMessageVO appMsgForm) {
        checkParamNotBlank(appMsgForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getAppId(), "请选择发送消息的应用"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getMaterialId(), "请选择素材"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getMaterialType(), "请设置素材类型"); // ignoreI18n
        checkParamNotBlank(appMsgForm.getSendType(), "请设置消息发送类型"); // ignoreI18n
        // 验证消息发送类型
        MessageSendTypeEnum messageSendType = MessageSendTypeEnum.getByCode(appMsgForm.getSendType());
        //判断是否是应用管理员
        checkAppAdmin(user, appMsgForm.getAppId());

        AppResult appResult = openAppService.loadOpenAppFast(appMsgForm.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION,appResult,"应用不存在"); // ignoreI18n
        }

        if (Objects.equals(messageSendType, MessageSendTypeEnum.GROUP_SEND_MSG)
                || Objects.equals(messageSendType, MessageSendTypeEnum.NOTICE_SEND)) {
            checkParamNotBlank(appMsgForm.getIsFixedTime(), "请设置发送时间类型"); // ignoreI18n
            //校验是否定时发送  不为空才检测
            if (!Objects.equals(appMsgForm.getIsFixedTime(), CommonConstant.YES)
                    && !Objects.equals(appMsgForm.getIsFixedTime(), CommonConstant.NO)) {
                throw new BizException(AjaxCode.PARAM_ERROR, "请选择正确的发送时间类型"); // ignoreI18n
            }
            if (Objects.equals(appMsgForm.getIsFixedTime(), CommonConstant.YES)) {
                if (appMsgForm.getSetTime() == null) {
                    throw new BizException(AjaxCode.PARAM_ERROR, "定时发送时间不能为空"); // ignoreI18n
                } else if (appMsgForm.getSetTime().compareTo(System.currentTimeMillis()) <= 0) {
                    throw new BizException(AjaxCode.PARAM_ERROR, "定时发送时间不能小于当前服务器时间"); // ignoreI18n
                }
            }
        }

        final String msgId = appMessageManager.sendImgTextMessage(user, appMsgForm);

        //管理员回复 需要写入MQ
        if (Objects.equals(MessageSendTypeEnum.ADMIN_REPLY_SEND.getCode(), appMsgForm.getSendType())) {
            serviceReplyMsgToMq(user, appMsgForm.getAppId());
        }

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("appId", appMsgForm.getAppId());
            AppViewDO targets = JsonKit.json2object(appMsgForm.getTargets(), AppViewDO.class);
            map.put("targetUsers", appMessageManager.convertToUserIds(user, targets));
            map.putAll(JsonUtils.parseToMap(appMsgForm));
            map.put("messageId", msgId);
            map.put("action", BuriedPointBizEnum.GROUP_SEND_IMAGE_TEXT_MSG.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.GROUP_SEND_IMAGE_TEXT_MSG, map, e);
        }
        return new AjaxResult(msgId);
    }

    /**
     * 发送模板消息到指定目标.
     *
     * @param user                 操作人.
     * @param templateMessageParam 模板消息属性
     * @return 成功或失败
     */
    @RequestMapping("/sendTemplateMsg")
    @ResponseBody
    public AjaxResult sendTemplateMsg(@ModelAttribute FsUserVO user,
                                      @RequestBody TemplateMessageParam templateMessageParam) {
        checkParamNotBlank(templateMessageParam, "请填写表单"); // ignoreI18n
        checkParamNotBlank(templateMessageParam.getAppId(), "请选择应用"); // ignoreI18n
        checkAppAdmin(user, templateMessageParam.getAppId());
        AppResult appResult = openAppService.loadOpenAppFast(templateMessageParam.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        AjaxResult result = doSendTemplateMsg(user, templateMessageParam);
        AppStatLogKit.logSendMsg(templateMessageParam.getAppId(), MonitorTypeEnum.STATISTICS_SEND_MSG, user.getEnterpriseAccount(), user.getUserId(), appResult.getResult().getAppType(), MessageSendStatType.APP_CENTER_WEB_ADMIN_SEND, MessageTypeEnum.TEMPLATE.getType());
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("appId", templateMessageParam.getAppId());
            map.put("messageSendStatType", MessageSendStatType.APP_CENTER_WEB_ADMIN_SEND.getType());
            map.put("messageType", MessageTypeEnum.TEMPLATE.getType());
            map.putAll(JsonUtils.parseToMap(templateMessageParam));
            map.put("targetUsers", appMessageManager.convertToUserIds(user, templateMessageParam.getTargets()));
            map.put("action", BuriedPointBizEnum.APP_SEND_TEMPLATE_MSG.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.APP_SEND_TEMPLATE_MSG, map, e);
        }
        return result;
    }

    /**
     * 发送模板消息到指定目标(普通登录用户).
     *
     * @param user                 操作人.
     * @param templateMessageParam 模板消息属性
     * @return 成功或失败
     */
    @RequestMapping("/sendTemplateMsgByFsUser")
    @ResponseBody
    public AjaxResult sendTemplateMsgByFsUser(@ModelAttribute FsUserVO user,
                                              @RequestBody TemplateMessageParam templateMessageParam) {
        checkParamNotBlank(templateMessageParam, "请填写表单"); // ignoreI18n
        AppResult appResult = openAppService.loadOpenAppFast(templateMessageParam.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        AjaxResult result = doSendTemplateMsg(user, templateMessageParam);
        AppStatLogKit.logSendMsg(templateMessageParam.getAppId(), MonitorTypeEnum.STATISTICS_SEND_MSG, user.getEnterpriseAccount(), user.getUserId(), appResult.getResult().getAppType(), MessageSendStatType.APP_CENTER_WEB_USER_SEND, MessageTypeEnum.TEMPLATE.getType());
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("appId", templateMessageParam.getAppId());
            map.put("messageSendStatType", MessageSendStatType.APP_CENTER_WEB_ADMIN_SEND.getType());
            map.put("messageType", MessageTypeEnum.TEMPLATE.getType());
            map.put("targetUsers", appMessageManager.convertToUserIds(user, templateMessageParam.getTargets()));
            map.putAll(JsonUtils.parseToMap(templateMessageParam));
            map.put("action", BuriedPointBizEnum.APP_SEND_TEMPLATE_MSG.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.APP_SEND_TEMPLATE_MSG, map, e);
        }
        return result;
    }

    /**
     * 发送模板消息.
     *
     * @param user                 操作人.
     * @param templateMessageParam 模板参数
     * @return 发送结果
     */
    private AjaxResult doSendTemplateMsg(FsUserVO user,
                                         final TemplateMessageParam templateMessageParam) {
        try {
            templateMessageParam.checkParam();
        } catch (IllegalArgumentException e) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, e.getMessage());
        }

        //获取登录用户信息
        final String appId = templateMessageParam.getAppId();

        //要求在可见范围中
        BaseResult<List<UserCanViewListVO>> canViewVOsResult =
                openFsUserAppViewService.queryComponentsByFsUser(user, AppAccessTypeEnum.WEB);
        if (!canViewVOsResult.isSuccess()) {
            logger.warn("failed to call openFsUserAppViewService.queryComponentsByFsUser: user={}", user);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "判断用户身份错误"); // ignoreI18n
        }
        final Set<String> canViewAppIds = canViewVOsResult.getResult().stream()
                .map(UserCanViewListVO::getAppId)
                .collect(Collectors.toSet());
        if (!canViewAppIds.contains(appId)) {
            logger.info("user should not see this app: user={}, appId={}",
                    user, appId);
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "用户不在该应用的web组件可见范围内"); // ignoreI18n
        }
        logger.info("send app template message: user=[{}], templateMessageParam=[{}]", user, templateMessageParam);
        appMessageManager.sendTemplateMessage(user, templateMessageParam);
        return SUCCESS;
    }

    /**
     * 发送消息提醒应用管理员申请试用
     *
     * @param user  操作人.
     * @param appId 应用id
     * @return 成功或失败
     */
    @RequestMapping("/detail/sendMesToAdmin")
    @ResponseBody
    public AjaxResult sendMesToAdmin(@ModelAttribute FsUserVO user,
                                     @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "appId不能为空"); // ignoreI18n
        Map<String, Object> map = new HashMap<>();

        //查看用户是否已经发送过提醒
        String s = jedis.get(String.format(CommonConstants.SEND_MES_TO_ADMIN, appId, user.asStringUser()));

        if ("1".equals(s)) {
            return new AjaxResult(map);
        }

        //设置用于已经发送过提醒
        jedis.set(String.format(CommonConstants.SEND_MES_TO_ADMIN, appId, user.asStringUser()), "1");

        Long count = jedis.incr(String.format(CommonConstants.SEND_MES_TO_ADMIN_COUNT, user.asStringUser()));

        if (count == null || count % ConfigCenter.NOTIFY_ADMIN_LIMIT_COUNT != 0) {
            return new AjaxResult(map);
        }

        //每天每个应用只通知管理员一次
        Long notifyTimePerDay = jedis
                .incr(String.format(CommonConstants.SEND_MES_TO_ADMIN_NOTIFY_PER_DAY, appId, user.getEnterpriseAccount(),
                        DateFormatUtils.ISO_DATE_FORMAT.format(new Date())));

        if (notifyTimePerDay != null && notifyTimePerDay > 1) {
            return new AjaxResult(map);
        }

        //同一个应用，企业只通知管理员三次
        Long notifyTime = jedis
                .incr(String.format(CommonConstants.SEND_MES_TO_ADMIN_NOTIFY_TIME, appId, user.getEnterpriseAccount()));
        if (notifyTime != null && notifyTime > ConfigCenter.NOTIFY_ADMIN_TIMES) {
            return new AjaxResult(map);
        }

        //发送模版消息的提醒
        TemplateMessageParam templateMessageParam = new TemplateMessageParam();
        templateMessageParam.setAppId(ConfigCenter.NOTIFY_ADMIN_TEMPLATE_APP_ID);

        //获取企业管理员
        BaseResult<List<Integer>> r = openAppAddressBookEmployeeService.getAdminIds(user.getEnterpriseAccount());
        List<Integer> result = r.getResult();
        EmployeeRange employeeRange = new EmployeeRange();
        employeeRange.setMember(result);

        templateMessageParam.setTargets(employeeRange);

        templateMessageParam.setUrl(ConfigCenter.NOTIFY_ADMIN_TEMPLATE_URL);
        Map<String, Map<String, String>> data = new HashMap<>();

        Map<String, String> first = new HashMap<>();
        first.put("value", String.format(ConfigCenter.NOTIFY_ADMIN_TEMPLATE_FIRST, count + ""));
        data.put("first", first);

        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess() || AppStatus.ON_LINE.getStatus() != appResult.getResult().getStatus()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "加载app失败"); // ignoreI18n
        }

        Map<String, String> keyword1 = new HashMap<>();
        keyword1.put("value", "【" + appResult.getResult().getAppName() + "】");
        data.put("keyword1", keyword1);

        Map<String, String> remark = new HashMap<>();
        remark.put("value",ConfigCenter.NOTIFY_ADMIN_TEMPLATE_REMARK);
        data.put("remark", remark);


        templateMessageParam.setData(data);
        templateMessageParam.setTemplateId(ConfigCenter.NOTIFY_ADMIN_TEMPLATE_ID);


        //发送模版消息
        appMessageManager.sendTemplateMessage(user, templateMessageParam);
        logger.info("mock send template message " + ToStringBuilder.reflectionToString(user) + ",appId" + appId);

        AppStatLogKit.logSendMsg(appId, MonitorTypeEnum.STATISTICS_SEND_MSG, user.getEnterpriseAccount(), user.getUserId(), appResult.getResult().getAppType(), MessageSendStatType.NOTIFY_ADD_APP_SEND, MessageTypeEnum.TEMPLATE.getType());
        Map<String, Object> statMap = new HashMap<>();
        try {
            statMap.put("fsEa", user.getEnterpriseAccount());
            statMap.put("userId", user.getUserId());
            statMap.put("appId", templateMessageParam.getAppId());
            statMap.put("messageSendStatType", MessageSendStatType.NOTIFY_ADD_APP_SEND.getType());
            statMap.put("messageType", MessageTypeEnum.TEMPLATE.getType());
            statMap.put("targets", JsonUtils.parseToMap(templateMessageParam.getTargets()));
            statMap.put("targetUsers", appMessageManager.convertToUserIds(user, templateMessageParam.getTargets()));
            statMap.put("action", BuriedPointBizEnum.APP_SEND_TEMPLATE_MSG.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), statMap);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.APP_SEND_TEMPLATE_MSG, map, e);
        }
        return new AjaxResult(map);
    }

    /**
     * 用户是否可以发送消息提醒应用管理员试用
     *
     * @param user  操作人.
     * @param appId 应用id
     * @return 成功或失败
     */
    @RequestMapping("/detail/isVisible")
    @ResponseBody
    public AjaxResult isVisible(@ModelAttribute FsUserVO user,
                                @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "appId不能为空"); // ignoreI18n
        Map<String, Object> map = new HashMap<>();

        //判断是否是应用管理员
        if (isFsAdmin(user)) {
            map.put("isVisible", 3); //1 展示 2 不展示 3管理员
            return new AjaxResult(map);
        }

        //查看用户是否已经发送过提醒
        String s = jedis.get(String.format(CommonConstants.SEND_MES_TO_ADMIN, appId, user.asStringUser()));
        int result = 1;
        if ("1".equals(s)) {
            result = 2;
        }
        map.put("isVisible", result); //1 展示 2 不展示


        return new AjaxResult(map);
    }

    /**
     * 通知同事一起试用.
     *
     * @param user  操作人
     * @param appId 应用id
     * @return 通知发送是否成功
     */
    @RequestMapping("/notifyColleaguesToTry")
    @ResponseBody
    public AjaxResult notifyColleaguesToTry(@ModelAttribute FsUserVO user,
                                @RequestParam(value = "appId", required = false) String appId) {
        checkFsAdmin(user);
        checkParamNotBlank(appId, "appId不能为空"); // ignoreI18n
        final String msgId = appMessageManager.notifyColleaguesToTry(user, appId);
        return new AjaxResult(msgId);
    }

    /**
     * 管理员回复用户信息写入MQ
     * @param user 用户
     * @param appId 服务号ID
     */
    private void serviceReplyMsgToMq(FsUserVO user, String appId) {
        ServiceReplyMsgItem serviceReplyMsgItem = new ServiceReplyMsgItem();
        serviceReplyMsgItem.setAppId(appId);
        serviceReplyMsgItem.setEnterpriseAccount(user.getEnterpriseAccount());
        serviceReplyMsgItem.setUserId(user.getUserId());
        Message message = new Message("Customer_Service_Msg_Event", serviceReplyMsgItem.toProto());
        message.setTags("customerReplyTags");//方便其他系统根据不同业务监听不同应用
        message.setKeys(UUID.randomUUID().toString());
/*       if ( null == autoConfRocketMQSender.send(message)) {
            MQ_LOG.warn("serviceReplyMsgToMq failed, message={}, user={}", message, user);
            return;
        }*/
        MQ_LOG.warn("serviceReplyMsgToMq success, message={}, user={}", message, user);
    }

}
