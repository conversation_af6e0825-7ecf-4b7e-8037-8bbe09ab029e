package com.facishare.open.app.center.controller;

import com.facishare.open.app.ad.api.enums.AppAdBannerTypeEnum;
import com.facishare.open.app.ad.api.model.FsAdAppBannerDO;
import com.facishare.open.app.ad.api.service.AppCenterBannerService;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.ajax.result.AppPagerAjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.OpenAppCreateTemplateTypeEnum;
import com.facishare.open.app.center.api.model.property.OpenAppProperties;
import com.facishare.open.app.center.api.model.enums.AppStatus;
import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.StatusResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.CacheConstants;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.center.model.*;
import com.facishare.open.app.center.mq.item.tags.AppCenterMqTagsConstant;
import com.facishare.open.app.center.threadlocal.UserContextHolder;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.DateUtil;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.material.api.enums.ArticleCategorySwitchEnum;
import com.facishare.open.material.api.enums.ServiceCommentSwitchEnum;
import com.facishare.open.material.api.model.ArticleCommentListResult;
import com.facishare.open.material.api.model.LastReadTimeArg;
import com.facishare.open.material.api.model.LastReadTimeResult;
import com.facishare.open.material.api.model.ServiceGroupSendMsgStatResult;
import com.facishare.open.material.api.model.vo.ArticleCommentVO;
import com.facishare.open.material.api.service.ArticleCategorySwitchService;
import com.facishare.open.material.api.service.MaterialMessageService;
import com.facishare.open.material.api.service.ServiceCommentSwitchService;
import com.facishare.open.material.api.service.ServiceGroupSendMsgStatService;
import com.facishare.open.msg.model.StatCrossAppMsgVO;
import com.facishare.open.msg.model.StatInternalAppMsgVO;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.facishare.open.support.model.ArticleVO;
import com.facishare.open.support.service.ArticleService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务号Controller
 * <p/>
 * Created by liqiulin on 2016/6/22.
 */
@Controller
@RequestMapping("/open/appcenter/service")
public class ServiceController extends BaseController {
    @Resource
    private ServiceCommentSwitchService serviceCommentSwitchService;
    @Resource
    private ArticleCategorySwitchService articleCategorySwitchService;
    @Resource
    private ServiceNumberManager serviceNumberManager;
    @Resource
    private ServicePromotionManager servicePromotionManager;
    @Resource
    private AppManager appManager;
    @Resource
    private AppMessageToMqManager appMessageToMqManager;
    @Resource
    private ServiceManager serviceManager;
    @Resource
    private MaterialMessageService materialMessageService;
    @Resource
    private OpenAppAdminService openAppAdminService;
    @Resource
    private AppCenterBannerService appCenterBannerService;
    @Resource
    private AppCreateTemplateManager appCreateTemplateManager;
    @Resource
    private ArticleService articleService;
    @Resource
    private MessageExhibitionService messageExhibitionService;
    @Resource
    private ServiceGroupSendMsgStatService serviceGroupSendMsgStatService;
    @Resource
    private OperationLogService operationLogService;

    /**
     * 服务号工作台数据统计接口
     *
     * @param user
     * @param appId
     * @param period 周期 1 前一天 2 前一周 3 前一月
     * @return
     */
    @Deprecated
    @RequestMapping("/getStatistics")
    @ResponseBody
    public AjaxResult getStatistics(@ModelAttribute FsUserVO user,
                                    @RequestParam(value = "appId", required = false) String appId,
                                    @RequestParam(value = "period", required = false) Integer period) {
        checkParamNotBlank(period, "统计周期为空"); // ignoreI18n
        checkParamNotBlank(appId, "服务号id不能为空"); // ignoreI18n
        checkService(appId);
        checkAppAdmin(user, appId);

        LocalDate now = LocalDate.now();
        LocalDate endDate = now.minusDays(1);
        LocalDate startDate = now.minusDays(1);
        ServiceStatisticPeriod periodEnum = ServiceStatisticPeriod.getByCode(period);
        if (Objects.equals(periodEnum, ServiceStatisticPeriod.YESTERDAY)) {
            startDate = now.minusDays(1);
        } else if (Objects.equals(periodEnum, ServiceStatisticPeriod.LAST_WEEK)) {
            startDate = now.minusDays(7);
        } else if (Objects.equals(periodEnum, ServiceStatisticPeriod.LAST_MONTH)) {
            startDate = now.minusDays(30);
        }
        long startTime = DateUtil.getLocalDateTimeMills(startDate.atStartOfDay());
        long endTime = DateUtil.getLocalDateTimeMills(now.atStartOfDay());

        Map<String, Long> statistics = Maps.newHashMap();
        buildGroupSendMsgStats(appId, now.toString(), periodEnum, statistics);
        buildMsgStats(user.getEnterpriseAccount(), appId, startTime, endTime, statistics);

        return new AjaxResult(statistics);
    }

    /**
     * 服务号工作台数据统计接口(群发消息部分)
     *
     * @param user
     * @param appId
     * @param period 周期 1 前一天 2 前一周 3 前一月
     * @return
     */
    @RequestMapping("/getStatisticsOfGroupSendMsg")
    @ResponseBody
    public AjaxResult getStatisticsOfGroupSendMsg(@ModelAttribute FsUserVO user,
                                    @RequestParam(value = "appId", required = false) String appId,
                                    @RequestParam(value = "period", required = false) Integer period) {
        checkParamNotBlank(period, "统计周期为空"); // ignoreI18n
        checkParamNotBlank(appId, "服务号id不能为空"); // ignoreI18n
        checkService(appId);
        checkAppAdmin(user, appId);

        LocalDate now = LocalDate.now();
        ServiceStatisticPeriod periodEnum = ServiceStatisticPeriod.getByCode(period);
        Map<String, Long> statistics = Maps.newHashMap();
        buildGroupSendMsgStats(appId, now.toString(), periodEnum, statistics);

        return new AjaxResult(statistics);
    }

    /**
     * 服务号工作台数据统计接口(用户消息部分)
     *
     * @param user
     * @param appId
     * @param period 周期 1 前一天 2 前一周 3 前一月
     * @return
     */
    @RequestMapping("/getStatisticsOfUserMsg")
    @ResponseBody
    public AjaxResult getStatisticsOfUserMsg(@ModelAttribute FsUserVO user,
                                                  @RequestParam(value = "appId", required = false) String appId,
                                                  @RequestParam(value = "period", required = false) Integer period) {
        checkParamNotBlank(period, "统计周期为空"); // ignoreI18n
        checkParamNotBlank(appId, "服务号id不能为空"); // ignoreI18n
        checkService(appId);
        checkAppAdmin(user, appId);

        LocalDate now = LocalDate.now();
        LocalDate startDate = now.minusDays(1);
        ServiceStatisticPeriod periodEnum = ServiceStatisticPeriod.getByCode(period);
        if (Objects.equals(periodEnum, ServiceStatisticPeriod.YESTERDAY)) {
            startDate = now.minusDays(1);
        } else if (Objects.equals(periodEnum, ServiceStatisticPeriod.LAST_WEEK)) {
            startDate = now.minusDays(7);
        } else if (Objects.equals(periodEnum, ServiceStatisticPeriod.LAST_MONTH)) {
            startDate = now.minusDays(30);
        }
        long startTime = DateUtil.getLocalDateTimeMills(startDate.atStartOfDay());
        long endTime = DateUtil.getLocalDateTimeMills(now.atStartOfDay());

        Map<String, Long> statistics = Maps.newHashMap();
        buildMsgStats(user.getEnterpriseAccount(), appId, startTime, endTime, statistics);

        return new AjaxResult(statistics);
    }

    // 生成企信消息统计
    private void buildMsgStats(String ea, String appId, long startTime, long endTime, Map<String, Long> statistics) {
        boolean isLinkService = linkServiceManager.isLinkService(appId);
        if (isLinkService) {
            MessageExhibitionResult<StatCrossAppMsgVO> statCrossAppMsgResult =
                    messageExhibitionService.statCrossAppMsg(startTime, endTime, appId, ea, false);
            if (!statCrossAppMsgResult.isSuccess()) {
                logger.error("messageExhibitionService.statCrossAppMsg error. startTime[{}], endTime[{}], appId[{}], ea[{}], result[{}]",
                        startTime, endTime, appId, endTime, statCrossAppMsgResult);
                throw new BizException(statCrossAppMsgResult);
            }
            statistics.put("upMsgCount", statCrossAppMsgResult.getData().getUpMsgCount().longValue());
            statistics.put("upMsgUserCount", statCrossAppMsgResult.getData().getUpMsgUserCount().longValue());
            statistics.put("customerReplyMsgCount", statCrossAppMsgResult.getData().getCustomerReplyMsgCount().longValue());
        } else {
            MessageExhibitionResult<StatInternalAppMsgVO> statInternalAppMsgResult =
                    messageExhibitionService.statInternalAppMsg(startTime, endTime, appId, ea, false);
            if (!statInternalAppMsgResult.isSuccess()) {
                logger.error("messageExhibitionService.statInternalAppMsg error. startTime[{}], endTime[{}], appId[{}], ea[{}], result[{}]",
                        startTime, endTime, appId, endTime, statInternalAppMsgResult);
                throw new BizException(statInternalAppMsgResult);
            }
            statistics.put("upMsgCount", statInternalAppMsgResult.getData().getUpMsgCount().longValue());
            statistics.put("upMsgUserCount", statInternalAppMsgResult.getData().getUpMsgUserCount().longValue());
            statistics.put("customerReplyMsgCount", statInternalAppMsgResult.getData().getCustomerReplyMsgCount().longValue());
        }
    }

    // 生成群发消息统计
    private void buildGroupSendMsgStats(String appId, String baseOnDate, ServiceStatisticPeriod periodEnum, Map<String, Long> statistics) {
        String statAppId = appId;
        if (ConfigCenter.SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_SWITCH_ON) {
            statAppId = ConfigCenter.SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_APP_ID;
        }
        BaseResult<ServiceGroupSendMsgStatResult> getByAppIdAndBaseOnDateResult =  serviceGroupSendMsgStatService.getByAppIdAndBaseOnDate(statAppId, baseOnDate);
        if (!getByAppIdAndBaseOnDateResult.isSuccess()) {
            logger.error("serviceGroupSendMsgStatService.getByAppIdAndBaseOnDate error. statAppId[{}], baseOnDate[{}] result[{}]",
                    statAppId, baseOnDate, getByAppIdAndBaseOnDateResult);
            throw new BizException(getByAppIdAndBaseOnDateResult);
        }

        ServiceGroupSendMsgStatResult statResult = getByAppIdAndBaseOnDateResult.getResult();
        if (statResult.getHasSyncedSuccess()) {
            Long sendCount = null;
            Long readCount = null;
            Long likeCount = null;
            Long commentCount = null;

            if (Objects.equals(periodEnum, ServiceStatisticPeriod.YESTERDAY)) {
                sendCount = statResult.getSendYesterday();
                readCount = statResult.getPvYesterday();
                likeCount = statResult.getLikeYesterday();
                commentCount = statResult.getCommentYesterday();
            } else if (Objects.equals(periodEnum, ServiceStatisticPeriod.LAST_WEEK)) {
                sendCount = statResult.getSendLastWeek();
                readCount = statResult.getPvLastWeek();
                likeCount = statResult.getLikeLastWeek();
                commentCount = statResult.getCommentLastWeek();
            } else if (Objects.equals(periodEnum, ServiceStatisticPeriod.LAST_MONTH)) {
                sendCount = statResult.getSendLastMonth();
                readCount = statResult.getPvLastMonth();
                likeCount = statResult.getLikeLastMonth();
                commentCount = statResult.getCommentLastWeek();
            }

            statistics.put("sendCount", Objects.isNull(sendCount) ? 0 : sendCount);
            statistics.put("readCount", Objects.isNull(readCount) ? 0 : readCount);
            statistics.put("likeCount", Objects.isNull(likeCount) ? 0 : likeCount);
            statistics.put("commentCount", Objects.isNull(commentCount) ? 0 : commentCount);
        }

    }

    enum ServiceStatisticPeriod {
        YESTERDAY(1),
        LAST_WEEK(2),
        LAST_MONTH(3);

        public int getCode() {
            return code;
        }

        int code;

        ServiceStatisticPeriod(int code) {
            this.code = code;
        }

        public static ServiceStatisticPeriod getByCode(int code) {
            ServiceStatisticPeriod[] values = ServiceStatisticPeriod.values();
            for (ServiceStatisticPeriod period : values) {
                if (period.getCode() == code) {
                    return period;
                }
            }
            throw new IllegalArgumentException("code invalidate");
        }
    }


    /**
     * 查询服务号工作台指引， 第一次调用返回全量指引，后面调用则只返回最新指引
     * @return
     */
    @RequestMapping("/queryDashboardGuide")
    @ResponseBody
    public AjaxResult queryDashboardGuide() {
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        //做下容错处理
        try {
            return new AjaxResult(serviceManager.queryDashBoardGuide(fsUserVO));
        } catch (Exception e) {
            logger.warn("查询服务号工作台指引失败! fsUserVO["+fsUserVO+"]" , e); // ignoreI18n
            return new AjaxResult(new ArrayList<>());
        }
    }

    /**
     * 保存评论管理开关
     * @param user
     * @param commentSwitchForm
     * @return
     */
    @RequestMapping("/saveCommentSwitch")
    @ResponseBody
    public AjaxResult saveCommentSwitch(@ModelAttribute FsUserVO user,
                                        @RequestBody ServiceCommentSwitchForm commentSwitchForm) {
        checkParamNotBlank(commentSwitchForm, "表单内容为空"); // ignoreI18n
        checkParamNotBlank(commentSwitchForm.getAppId(), "appId为空"); // ignoreI18n
        checkParamNotBlank(commentSwitchForm.getCommentSwitch(), "评论开关为空"); // ignoreI18n

        if (!isFsAdmin(user) && !isAppAdmin(user, commentSwitchForm.getAppId())) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "权限不足."); // ignoreI18n
        }

        BaseResult<Boolean> saveResult =  serviceCommentSwitchService.saveCommentSwitch(user.getEnterpriseAccount(),
                commentSwitchForm.getAppId(), ServiceCommentSwitchEnum.getByCode(commentSwitchForm.getCommentSwitch()));
        if(!saveResult.isSuccess() || !saveResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "保存评论管理开关失败"); // ignoreI18n
        }
        return new AjaxResult(null);
    }

    /**
     * 保存文章分类开关
     * @param user
     * @param categorySwitchForm
     * @return
     */
    @RequestMapping("/saveCategorySwitch")
    @ResponseBody
    public AjaxResult saveCategorySwitch(@ModelAttribute FsUserVO user,
                                         @RequestBody ArticleCategorySwitchForm categorySwitchForm) {
        checkParamNotBlank(categorySwitchForm, "表单内容为空"); // ignoreI18n
        checkParamNotBlank(categorySwitchForm.getAppId(), "appId为空"); // ignoreI18n
        checkParamNotBlank(categorySwitchForm.getCategorySwitch(), "分类开关为空"); // ignoreI18n

        if (!isFsAdmin(user) && !isAppAdmin(user, categorySwitchForm.getAppId())) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "权限不足."); // ignoreI18n
        }

        BaseResult<Boolean> saveResult =  articleCategorySwitchService.saveCategorySwitch(user.getEnterpriseAccount(),
                categorySwitchForm.getAppId(), ArticleCategorySwitchEnum.getByCode(categorySwitchForm.getCategorySwitch()));
        if(!saveResult.isSuccess() || !saveResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "保存文章分类开关失败"); // ignoreI18n
        }
        return new AjaxResult(null);
    }
    /**
     * 查询文章评论列表
     * @param lastestShowedCommentTime ：已经显示的评论中，最新一条的时间
     */
    @RequestMapping("/queryArticleCommentList")
    @ResponseBody
    public AppPagerAjaxResult queryArticleCommentList(@ModelAttribute FsUserVO user,
                                                      @RequestParam(value = "appId") String appId,
                                                      @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                      @RequestParam(value = "lastestShowedCommentTime", required = false) Long lastestShowedCommentTime,
                                                      @RequestParam(value = "lastReadTime", required = false) Long clientLastReadTime) {
        checkParamNotBlank(appId, "服务号id为空"); // ignoreI18n
        if (!isAppAdmin(user, appId)) {
            return new AppPagerAjaxResult(AjaxCode.NO_AUTHORITY, "权限不足."); // ignoreI18n
        }

        // 判断评论管理开关
        BaseResult<ServiceCommentSwitchEnum> commentSwitchEnumBaseResult =
                serviceCommentSwitchService.queryCommentSwitchByAppId(user.getEnterpriseAccount(), appId);
        if (!commentSwitchEnumBaseResult.isSuccess()) {
            logger.warn("failed to call serviceCommentSwitchService.queryCommentSwitchByAppId. appId[{}], ,result[{}]", appId, commentSwitchEnumBaseResult);
            return new AppPagerAjaxResult(AjaxCode.BIZ_EXCEPTION, "查询文章评论开关状态错误"); // ignoreI18n
        }
        if (ServiceCommentSwitchEnum.OFF.equals(commentSwitchEnumBaseResult.getResult())) {
            return new AppPagerAjaxResult(AjaxCode.BIZ_EXCEPTION, "文章评论已经关闭"); // ignoreI18n
        }

        // 打开文章评论列表第一页则清除未读评论标识
        if (currentPage == 1) {
            CommonThreadPoolUtils.getExecutor().execute(() -> {
                try {
                    serviceManager.clearUnreadCommentCount(appId, user.getEnterpriseAccount(), user.getUserId());
                } catch (Exception e) {
                    logger.warn("clearUnreadCommentCount error. appId[" + appId + "], fsEa[" + user.getEnterpriseAccount() + "]", e);
                }
            });
        }

        // 查询文章评论列表
        BaseResult<ArticleCommentListResult> queryArticleCommentsResult =
                materialMessageService.queryArticleComments(user.getEnterpriseAccount(), appId, currentPage, pageSize);
        if (!queryArticleCommentsResult.isSuccess()) {
            logger.warn("failed to call materialMessageService.queryArticleComments. appId[{}], currentPage[{}], pageSize[{}]",
                    appId, currentPage, pageSize);
            return new AppPagerAjaxResult(AjaxCode.BIZ_EXCEPTION, "查询文章评论错误"); // ignoreI18n
        }
        int recordSize = queryArticleCommentsResult.getResult().getTotal();
        List<ArticleCommentVO> articleCommentVOList = queryArticleCommentsResult.getResult().getList();

        //更新 lastestShowedCommentTime：对比 lastestShowedCommentTime 和 文章评论列表第一条的时间，取两者最新的时间为 lastestShowedCommentTime
        if (!CollectionUtils.isEmpty(articleCommentVOList)) {
            if (null == lastestShowedCommentTime || lastestShowedCommentTime < articleCommentVOList.get(0).getCreateTime()) {  // 第一次没有 lastestShowedCommentTime 或者 有lastestShowedCommentTime，但不是已经显示过当中最新的
                lastestShowedCommentTime = articleCommentVOList.get(0).getCreateTime();
            }
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("currentTime", System.currentTimeMillis());
        dataMap.put("lastestShowedCommentTime", lastestShowedCommentTime);
        if (!org.springframework.util.CollectionUtils.isEmpty(articleCommentVOList)) {
            LastReadTimeArg.LastReadTimeArgBuilder builder = new LastReadTimeArg.LastReadTimeArgBuilder(user.getEnterpriseAccount(),
                    user.getUserId(), appId);
            builder.clientLastReadTime(clientLastReadTime);
            builder.latestCommentTime(articleCommentVOList.get(0).getCreateTime());
            builder.earliestCommentTime(articleCommentVOList.get(articleCommentVOList.size() - 1).getCreateTime());
            BaseResult<LastReadTimeResult> lastReadTime = materialMessageService.queryLastReadTime(builder.build());
            if (!lastReadTime.isSuccess()) {
                logger.warn("failed to call materialMessageService.queryLastReadTime. appId[{}], ea[{}], userId[{}], " +
                                "clientLastReadTime[{}], commentCreatedTime[{}]", appId, user.getEnterpriseAccount(),
                        user.getUserId(), clientLastReadTime, articleCommentVOList.get(0).getCreateTime());
                return new AppPagerAjaxResult(AjaxCode.BIZ_EXCEPTION, "查询最近阅读评论时间错误"); // ignoreI18n
            }
            dataMap.put("lastReadTime", lastReadTime.getResult().getLastReadTime());
            dataMap.put("previousPageCommentTime", lastReadTime.getResult().getLastCommentTimeInPreviousPage());
        }

        Pager<ArticleCommentVO> result = new Pager<>();
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        result.setRecordSize(recordSize);
        result.setData(articleCommentVOList);
        AppPagerAjaxResult pagerAjaxResult = new AppPagerAjaxResult(result);
        pagerAjaxResult.setData(dataMap);
        return pagerAjaxResult;
    }

    /**
     * 查询是否有新评论
     *
     * @param user
     * @param appId
     * @param lastestShowedCommentTime ：已经显示的评论中，最新一条的时间
     * @return
     */
    @RequestMapping("/queryHasNewArticleComment")
    @ResponseBody
    public AjaxResult queryHasNewArticleComment(@ModelAttribute FsUserVO user,
                                                @RequestParam(value = "appId", required = false) String appId,
                                                @RequestParam(value = "lastestShowedCommentTime", required = false) Long lastestShowedCommentTime) {
        checkParamNotBlank(appId, "服务号id为空"); // ignoreI18n
        if (!isFsAdmin(user) && !isAppAdmin(user, appId)) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "权限不足."); // ignoreI18n
        }

        // 判断评论管理开关
        BaseResult<ServiceCommentSwitchEnum> commentSwitchEnumBaseResult =
                serviceCommentSwitchService.queryCommentSwitchByAppId(user.getEnterpriseAccount(), appId);
        if (!commentSwitchEnumBaseResult.isSuccess()) {
            logger.warn("failed to call serviceCommentSwitchService.queryCommentSwitchByAppId. appId[{}], ,result[{}]", appId, commentSwitchEnumBaseResult);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "查询文章评论开关状态错误"); // ignoreI18n
        }
        if (ServiceCommentSwitchEnum.OFF.equals(commentSwitchEnumBaseResult.getResult())) {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "文章评论已经关闭"); // ignoreI18n
        }

        // 查询文章最新一条评论
        int currentPage = 1;
        int pageSize = 1;
        BaseResult<ArticleCommentListResult> queryArticleCommentsResult =
                materialMessageService.queryArticleComments(user.getEnterpriseAccount(), appId, currentPage, pageSize);
        if (!queryArticleCommentsResult.isSuccess()) {
            logger.warn("failed to call materialMessageService.queryArticleComments. appId[{}], currentPage[{}], pageSize[{}]",
                    appId, currentPage, pageSize);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "查询文章评论错误"); // ignoreI18n
        }
        List<ArticleCommentVO> articleCommentVOList = queryArticleCommentsResult.getResult().getList();

        //判断是否有新评论
        int hasNewComent = 0;  //0：没新评论  1：有新评论
        if (!CollectionUtils.isEmpty(articleCommentVOList)) {
            if (null == lastestShowedCommentTime || lastestShowedCommentTime < articleCommentVOList.get(0).getCreateTime()) {  //第一次没有 lastestShowedCommentTime   或者  有lastestShowedCommentTime，但不是最新的
                hasNewComent = 1;
            }
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("hasNewComent", hasNewComent);
        return new AjaxResult(dataMap);
    }

    /**
     * 加载服务号工作台
     *
     * @param user
     * @return
     */
    @RequestMapping("/loadServiceDashboard")
    @ResponseBody
    public AjaxResult loadServiceDashboard(@ModelAttribute FsUserVO user,
                                           @RequestParam(value = "appId", required = false) String appId,
                                           @RequestParam(value = "appType", required = false) Integer appType) {
        checkParamNotBlank(appId, "服务号id不能为空"); // ignoreI18n
        checkService(appId);
        checkAppAdmin(user, appId);
        Map<String, Object> dashboardMap = new HashMap<>();
        OpenAppDO appDO = appManager.loadAppBrief(appId);
        // 名称
        dashboardMap.put("appName", appDO.getAppName());

        // 通知
        try {
            dashboardMap.put("notice", appManager.queryServiceDashboardNotice());
        } catch (BizException e) {
            dashboardMap.put("notice", new HashMap<>());
            logger.warn("appManager.queryServiceDashboardNotice failed", e);
        }
        // 统计数据
        dashboardMap.put("statistics", appManager.queryServiceDashboardStatistics(appDO.getAppId(),
                user.getEnterpriseAccount()));

        // 是否存在未读评论
        boolean hasUnReadComment = false;
        try {
            hasUnReadComment = serviceManager.hasUnreadComment(appId, user.getEnterpriseAccount());
        } catch (BizException e) {
            logger.warn("call serviceManager.hasUnreadComment failed", e);
        }
        dashboardMap.put("hasUnReadComment", hasUnReadComment ? 1 : 2);

        //是否有上行消息
        boolean hasUnReadMsg = false;
        try {
            hasUnReadMsg = serviceManager.hasUnreadMsg(appId, user.getEnterpriseAccount());
        } catch(BizException e) {
            logger.warn("call serviceManager.hasUnreadMsg failed", e);
        }
        dashboardMap.put("hasUnReadMsg", hasUnReadMsg ? 1 : 2);

        // 服务号特性列表
        dashboardMap.put("features", appManager.queryServiceFeatureList(user, appId, appType));//外联服务号1 无服务工单、问卷和文章分类

        Integer categoryId;
        String faqsMore;
        //外联服务号 常见问题配置
        if(Objects.equals(AppCenterEnum.AppType.LINK_SERVICE.value(), appType)){
            // 常见问题
            dashboardMap.put("faqs", JsonKit.json2object(ConfigCenter.LINK_SERVICE_FAQ, new TypeToken<List<ServiceFAQVO>>(){}.getType()));

            dashboardMap.put("faqsMore", ConfigCenter.LINK_SERVICE_FAQ_MORE_TARGET_URL);
        } else {
            // 常见问题
            dashboardMap.put("faqs", JsonKit.json2object(ConfigCenter.SERVICE_FAQ, new TypeToken<List<ServiceFAQVO>>(){}.getType()));

            dashboardMap.put("faqsMore", ConfigCenter.SERVICE_FAQ_MORE_TARGET_URL);
        }

        // 更多url
        dashboardMap.put("sysNoticeMore", ConfigCenter.SYSTEM_NOTICE_MORE_TARGET_URL);
        Integer isRecommended = null;
        if (!StringUtils.isBlank(appDO.getProperties())) {
            isRecommended = OpenAppProperties.fromJson(appDO.getProperties()).getIsRecommendedApp();
        }
        if (isRecommended != null && isRecommended == 1) { //todo 可以使用常量enum
            Boolean result = serviceManager.isShowBanner(user, appId);
            if (result != null && result) {
                dashboardMap.put("isShowBanner", true);
                dashboardMap.put("appDesc", appDO.getAppDesc());
            } else {
                dashboardMap.put("isShowBanner", false);
            }
        } else {
            dashboardMap.put("isShowBanner", false);
        }

        //添加用于控制数据统计日志范围的属性。1 前1天  2  前一周 3 前一月
        dashboardMap.put("statisticsDateFlags", ConfigCenter.SERVICE_DASHBOARD_STATISTICS_DATE_FLAG);

        return new AjaxResult(dashboardMap);
    }

    /**
     * 企业是否能够创建服务号
     *
     * @param user
     * @return
     */
    @RequestMapping("/isAbleToCreateService")
    @ResponseBody
    public AjaxResult isAbleToCreateService(@ModelAttribute FsUserVO user,
                                            @RequestParam(value = "appType", required = false) Integer appType) {
        AppCenterEnum.AppType appTypeEnum;
        if (appType != null && AppCenterEnum.AppType.LINK_SERVICE.value() == appType) {
            linkServiceManager.isUpstreamLinkAdmin(user);
            appTypeEnum = AppCenterEnum.AppType.LINK_SERVICE;
        } else {
            checkFsAdmin(user);//验证系统管理员
            appTypeEnum = AppCenterEnum.AppType.SERVICE;
        }
        AbleCreateServiceVO ableCreateServiceVO = appManager.isAbleToCreateService(user, appTypeEnum);
        return new AjaxResult(ableCreateServiceVO);
    }

    /**
     * 服务号安全设置.
     *
     * @param user                         管理员或应用管理员登录.
     * @param materialAccessPermissionForm 素材权限设置Form
     * @return AjaxResult.
     */
    @RequestMapping("/saveMaterialAccessPermission")
    @ResponseBody
    public AjaxResult saveMaterialAccessPermission(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                                   @RequestBody MaterialAccessPermissionForm materialAccessPermissionForm) {
        checkParamNotBlank(materialAccessPermissionForm, "请填写表单."); // ignoreI18n
        String appId = materialAccessPermissionForm.getAppId();
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        Integer materialAccessPermission = materialAccessPermissionForm.getMaterialAccessPermission();
        checkParamNotBlank(materialAccessPermission, "安全设置不能为空"); // ignoreI18n
        checkAppAdmin(user, appId);
        checkService(appId);
        int accessPermission = appManager.saveMaterialAccessPermission(user, appId, materialAccessPermission);
        //修改安全设置消息写MQ
        if (CommonConstant.ACCESSPERMISSION_INTERNAL == materialAccessPermissionForm.getMaterialAccessPermission()) {
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                    user.getUserId(), System.currentTimeMillis()+ "", "修改安全设置为\""+ CommonConstant.SAFETY_SET_INFO_1 +"\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        } else if (CommonConstant.ACCESSPERMISSION_OPEN == materialAccessPermissionForm.getMaterialAccessPermission()){
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                    user.getUserId(), System.currentTimeMillis()+ "", "修改安全设置为\""+ CommonConstant.SAFETY_SET_INFO_2 +"\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        }
        Map<String, Object> ajaxDataMap = new HashMap<>();
        ajaxDataMap.put("materialAccessPermission", accessPermission);
        return new AjaxResult(ajaxDataMap);
    }

    /**
     * 加载服务号信息.
     *
     * @param user  管理员或应用管理员登录.
     * @param appId 应用id.
     * @return AjaxResult.
     */
    @RequestMapping("/loadServiceByAppId")
    @ResponseBody
    public AjaxResult loadServiceByAppId(@ModelAttribute FsUserVO user,
                                         @RequestParam(value = "appId", required = false) String appId) {

        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkService(appId);
        Map<String, Object> ajaxDataMap = appManager.loadServiceByAppId(user, appId);
        return new AjaxResult(ajaxDataMap);
    }

    /**
     * 查询服务号各种功能的状态（1:已开启、2:未开启、3:隐藏）.
     *
     * @param user  管理员或应用管理员登录.
     * @param appId 应用id.
     * @return AjaxResult.
     */
    @RequestMapping("/queryFeatureStatusByAppId")
    @ResponseBody
    public AjaxResult queryFeatureStatusByAppId(@ModelAttribute FsUserVO user,
                                                @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkService(appId);
        Map<String, Object> ajaxDataMap = appManager.queryFeatureStatusByAppId(user, appId);
        return new AjaxResult(ajaxDataMap);
    }

    /**
     * 服务号启用/停用.
     *
     * @param user   操作者.
     * @param appId  应用id
     * @param status 1启用, 2停用
     * @return 处理结果
     */
    @RequestMapping("/updateServiceBindOnOff")
    @ResponseBody
    public AjaxResult updateServiceBindOnOff(@ModelAttribute FsUserVO user,
                                             @RequestParam(value = "appId", required = false) String appId,
                                             @RequestParam(value = "status", required = false) Integer status) {
        checkParamNotBlank(appId, "请选择要设置的服务号"); // ignoreI18n
        checkParamRegex("" + status, "[12]{1}", "请填写有效的状态"); // ignoreI18n

        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在."); // ignoreI18n
        }
        OpenAppDO openAppDO = appResult.getResult();

        // 校验权限
        boolean isLinkService = linkServiceManager.isLinkService(appId);
        if (isLinkService) {
            if (!linkServiceManager.isUpstreamLinkAdmin(user) && !isAppAdmin(user, appId)) {
                return new AjaxResult(AjaxCode.NO_AUTHORITY,"权限不足"); // ignoreI18n
            }
        } else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == openAppDO.getAppType()) {
            if (!webAuthManager.isLinkAdmin(user) && !isAppAdmin(user, appId)) {
                return new AjaxResult(AjaxCode.NO_AUTHORITY, "权限不足"); // ignoreI18n
            }
        } else {
            if (!isAppAdmin(user, appId) && !isFsAdmin(user)) {
                return new AjaxResult(AjaxCode.NO_AUTHORITY,"权限不足"); // ignoreI18n
            }
        }

        // 验证是否服务号
        String enterpriseAccount = user.getEnterpriseAccount();
        checkService(appId);

        //1.启动，停用操作
        StatusResult statusResult
                = openFsUserBindAppService.updateServiceBindOnOff(user, appId, enterpriseAccount, status);
        if (!statusResult.isSuccess()) {
            logger.warn("updateAppBindOnOff failed,user[{}] appId[{}],status[{}],result[{}]", user, appId, status, statusResult);
            if (statusResult.getErrCode() == AppCenterCodeEnum.APP_IS_NORMAL.getErrCode()){
                return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "服务号已经被启用,请刷新页面."); // ignoreI18n
            }
            if (statusResult.getErrCode() == AppCenterCodeEnum.APP_IS_STOP.getErrCode()){
                return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "服务号已经被停用,请刷新页面."); // ignoreI18n
            }
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "更新服务号状态失败"); // ignoreI18n
        }

        if (CommonConstant.APP_BIND_STATUS_ON == status) {
            serviceNumberManager.modifyServiceSession(user, appId, ServiceTypeEnum.ON);
        } else if (CommonConstant.APP_BIND_STATUS_OFF == status) {
            serviceNumberManager.modifyServiceSession(user, appId, ServiceTypeEnum.OFF);
        }

        // 通讯录用户重新拉取服务号列表
        if (isLinkService) {
            serviceManager.notifyUserViewAsync(user, appId);
        } else {
            serviceManager.updateSessionNameLogoDesc(user, appId, null, null, null);
        }
        return SUCCESS;
    }

    /**
     * 服务号推广页.
     * @param user 操作者.
     * @return
     */
    @RequestMapping("/queryServicePromotion")
    @ResponseBody
    public AjaxResult queryServicePromotion(@ModelAttribute FsUserVO user) {
        ServicePromotionVO servicePromotionVO = servicePromotionManager.queryServicePromotionVO(user);
        return new AjaxResult(servicePromotionVO);
    }

    /**
     * 关闭服务号推广.
     * @param user 操作者.
     * @return
     */
    @RequestMapping("/closeServicePromotion")
    @ResponseBody
    public AjaxResult closeServicePromotion(@ModelAttribute FsUserVO user) {
        servicePromotionManager.addEaClosePromotionAdminCache(user);
        return SUCCESS;
    }

    /**
     * 加载服务号首页
     *
     * @param user
     * @return
     */
    @RequestMapping("/queryServiceHomePage")
    @ResponseBody
    public AjaxResult queryServiceHomePage(@ModelAttribute FsUserVO user, @ModelAttribute String lang) {
        Integer eaServiceCount = 0;
        Integer manageServiceCount = 0;
        List<FsAdAppBannerDO> banners = new ArrayList<>();
        List<NoticeContentVO> systemAnnouncements = new ArrayList<>();

        Map<String, Object> homePageMap = new HashMap<>();
        Map<String, Object> serviceCountMap = new HashMap<>();
        Map<String, Object> systemAnnouncementsMap = new HashMap<>();

        //企业服务号创建模板
        List<Map<String, Object>> createTemplateList = appCreateTemplateManager.queryCreateTemplateList(user, OpenAppCreateTemplateTypeEnum.CUSTOM_SERVICE.getValue(), lang);
        if (CollectionUtils.isEmpty(createTemplateList)) {
            logger.error("queryCreateTemplateList failed. user[{}]", user);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取服务号模板失败"); // ignoreI18n
        }

        //企业服务号数
        com.facishare.open.app.center.api.result.BaseResult<Integer> eaServiceCountResult =
                openAppService.queryEaAllServiceCount(user.getEnterpriseAccount());
        if (!eaServiceCountResult.isSuccess() || null == eaServiceCountResult.getResult()) {
            logger.warn("queryServiceCount failed. fsEa[{}], result[{}]", user.getEnterpriseAccount(), eaServiceCountResult);
        } else {
            eaServiceCount = eaServiceCountResult.getResult();
        }

        //个人管理服务号数
        com.facishare.open.app.center.api.result.BaseResult<Integer> manageServiceCountResult=
                openAppAdminService.queryServiceCountByUserId(user.asStringUser());
        if (!manageServiceCountResult.isSuccess() || null == manageServiceCountResult.getResult()) {
            logger.warn("queryServiceCountByUserId failed. userId[{}], result[{}]", user.getUserId(), manageServiceCountResult);
        } else {
            manageServiceCount = manageServiceCountResult.getResult();
        }

        //服务号banner
        BaseResult<List<FsAdAppBannerDO>> bannerResult = appCenterBannerService.queryAppBanners(AppAdBannerTypeEnum.SERVICE);
        if (!bannerResult.isSuccess() || null == bannerResult.getResult()) {
            logger.warn("queryAppBanners failed.bannerResult[{}]", bannerResult);
        } else {
            banners = bannerResult.getResult();
        }

        //系统公告
        try {
            BaseResult<List<ArticleVO>> announcementsResult = articleService.getLatestAnnouncements(5);
            if (!announcementsResult.isSuccess() || null == announcementsResult.getResult()) {
                logger.warn("getLatestAnnouncements failed. announcementsResult[{}]", announcementsResult);
            } else {
                announcementsResult.getResult().forEach(articleVO -> {
                    NoticeContentVO noticeContentVO = new NoticeContentVO();
                    noticeContentVO.setTitle(articleVO.getTitle());
                    noticeContentVO.setUrl(BizCommonUtils.getSystemNoticeArticleTargetUrl(articleVO.getArticleId()));
                    systemAnnouncements.add(noticeContentVO);
                });
            }
        } catch (Exception e) {
            logger.error("getLatestAnnouncements failed.", e);
        }

        serviceCountMap.put("eaServiceCount", eaServiceCount);
        serviceCountMap.put("manageServiceCount", manageServiceCount);
        systemAnnouncementsMap.put("announcements", systemAnnouncements);
        systemAnnouncementsMap.put("learnMore", ConfigCenter.SYSTEM_NOTICE_MORE_TARGET_URL);

        homePageMap.put("serviceCount", serviceCountMap);
        homePageMap.put("banners", banners);
        homePageMap.put("createTemplateList", createTemplateList);
        homePageMap.put("systemAnnouncements", systemAnnouncementsMap);
        homePageMap.put("relatedDocuments", new Gson().fromJson(ConfigCenter.SERVICE_RELATED_DOCUMENTS, new TypeToken<List<RelatedDocumentVO>>(){}.getType()));
        return new AjaxResult(homePageMap);
    }

    @RequestMapping(value = "/closeBanner", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult closeBanner(@ModelAttribute FsUserVO user, @RequestBody String appId) {
        if (appId == null) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "请选择要关闭提示的服务号"); // ignoreI18n
        }
        serviceManager.closeBanner(user, appId);
        return SUCCESS;
    }

    @RequestMapping("/queryServiceListByAdmin")
    @ResponseBody
    public AjaxResult queryServiceListByAdmin(@ModelAttribute FsUserVO user) {
        com.facishare.open.app.center.api.result.BaseResult<List<String>> serviceIdListResult =
                openAppAdminService.queryServiceIdList(user.asStringUser());
        if (!serviceIdListResult.isSuccess()) {
            logger.warn("failed to call queryServiceIdList in queryServiceListByAdmin, user[{}]", user);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取服务号列表失败"); // ignoreI18n
        }
        List<String> serviceIdList = serviceIdListResult.getResult();
        if (!CollectionUtils.isEmpty(serviceIdList)) {
            List<ServiceNumberVO> list = serviceIdList.stream().map(appManager::loadAppBrief)
                    .filter(d -> {
                        boolean isOnline = d.getStatus() == AppStatus.ON_LINE.status();
                        boolean isSelfBuiltService = AppCenterEnum.AppType.SERVICE.value() == d.getAppType();
                        return isOnline && isSelfBuiltService;
                    }).map(s -> {
                        ServiceNumberVO v = new ServiceNumberVO();
                        v.setAppId(s.getAppId());
                        v.setAppName(s.getAppName());
                        return v;
                    }).collect(Collectors.toList());
            return new AjaxResult(list);
        }
        return new AjaxResult(new ArrayList<>(0));
    }
}
