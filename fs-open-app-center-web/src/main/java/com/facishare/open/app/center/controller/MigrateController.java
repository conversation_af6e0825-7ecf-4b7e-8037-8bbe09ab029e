package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.AppStatus;
import com.facishare.open.app.center.api.result.AppListResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.ServiceNumberManager;
import com.facishare.open.app.center.model.SupportStaffForm;
import com.facishare.open.app.center.model.SupportStaffVO;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用于数据迁移等操作
 * Created by liqiulin on 2017/7/10.
 */
@Controller
@RequestMapping("/open/appcenter/migrate")
public class MigrateController extends BaseController {
    @Resource
    private OpenAppAdminService openAppAdminService;

    private List<FsUserVO> getAdminsByAppId(String ea, String appId) {
        BaseResult<List<String>> appAdminIdsResult = openAppAdminService.queryAppAdminIdList(ea, appId);
        if (!appAdminIdsResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.queryAppAdminIdList, fsEa[{}], appId=[{}], result[{}]",
                    ea, appId, appAdminIdsResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取应用管理员列表失败"); // ignoreI18n
        }
        List<FsUserVO> adminIds = appAdminIdsResult.getResult().stream().map(fsUserString -> new FsUserVO(fsUserString)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adminIds)) {
            logger.info("appId[{}] 无管理员，不设置移动客服", appId); // ignoreI18n
        }
        return adminIds;
    }


    private void checkRight(FsUserVO fsUserVO) {
        if (!ConfigCenter.MIGRATE_OP_USER_ACCOUNTS.contains(fsUserVO.getUserAccount())) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
        }
    }

}
