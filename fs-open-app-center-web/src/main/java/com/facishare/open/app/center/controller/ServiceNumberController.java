package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.manager.AppMessageToMqManager;
import com.facishare.open.app.center.manager.ServiceNumberManager;
import com.facishare.open.app.center.model.*;
import com.facishare.open.app.center.mq.item.tags.AppCenterMqTagsConstant;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 类名称：ServiceNumberController
 * 类描述： 移动客服相关接口
 * 创建人：zhouq
 * 创建时间：2016年3月25日 下午4:47:17
 * 修改人：zhouq
 * 修改时间：2016年3月25日 下午4:47:17
 * 修改备注：
 */
@Controller
@RequestMapping("/open/appcenter/servicenumber")
public class ServiceNumberController extends BaseController {

    @Resource
    private ServiceNumberManager serviceNumberManager;

    @Resource
    private AppMessageToMqManager appMessageToMqManager;

    @Resource
    private OperationLogService operationLogService;

    /**
     * 移动客服启用/停用.
     *
     * @param serviceNumberForm 客服信息
     * @return 处理结果
     */
    @RequestMapping("/updateServiceNumberOnOff")
    @ResponseBody
    public AjaxResult updateServiceNumberOnOff(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody ServiceNumberForm serviceNumberForm) {
        String appId = serviceNumberForm.getAppId();
        checkParamNotBlank(appId, "请选择要设置的应用"); // ignoreI18n
        checkParamRegex("" + serviceNumberForm.getStatus(), "[0-1]{1}", "请填写有效的状态"); // ignoreI18n
        checkAppAdmin(user, appId);
        checkService(appId);
        String enterpriseAccount = user.getEnterpriseAccount();
        OpenServiceNumberDO entity = new OpenServiceNumberDO();
        entity.setAppId(appId);
        entity.setFsEa(enterpriseAccount);
        entity.setSwitchType(serviceNumberForm.getStatus());
        serviceNumberManager.updateServiceNumberOnOff(entity, user, lang, false);
        return new AjaxResult(entity);
    }

    /**
     * 获取服务号列表
     * @param user 用户
     * @return 服务号列表
     */
    @RequestMapping("/queryServiceNumbers")
    @ResponseBody
    public AjaxResult queryServiceNumbers(@ModelAttribute FsUserVO user){
        List<ServiceNumberVO> serviceNumberVOs = serviceNumberManager.queryServiceNumbers(user);
        return new AjaxResult(serviceNumberVOs);
    }

    /**
     * 设置客服人员信息
     * @param user 用户
     * @param supportStaffForm 客服人员信息
     * @return AjaxResult
     */
    @RequestMapping("/setSupportStaff")
    @ResponseBody
    public AjaxResult setSupportStaff(@ModelAttribute FsUserVO user, @RequestBody SupportStaffForm supportStaffForm){
        checkParamNotBlank(supportStaffForm, "参数错误"); // ignoreI18n
        checkParamNotBlank(supportStaffForm.getAppId(), "应用ID不能为空"); // ignoreI18n
        checkParamNotBlank(supportStaffForm.getSupportStaffName(), "客服名称不能为空"); // ignoreI18n
        checkAppAdmin(user, supportStaffForm.getAppId());
        checkService(supportStaffForm.getAppId());
        serviceNumberManager.setSupportStaff(user, supportStaffForm);
        return new AjaxResult(null);
    }

    /**
     * 查询客服人员信息
     * @param user 用户
     * @param supportStaffForm 客服人员信息
     * @return AjaxResult
     */
    @RequestMapping("/querySupportStaff")
    @ResponseBody
    public AjaxResult querySupportStaff(@ModelAttribute FsUserVO user, @RequestBody SupportStaffForm supportStaffForm){
        checkParamNotBlank(supportStaffForm, "参数错误"); // ignoreI18n
        checkParamNotBlank(supportStaffForm.getAppId(), "应用ID不能为空"); // ignoreI18n
        checkAppAdmin(user, supportStaffForm.getAppId());
        checkService(supportStaffForm.getAppId());
        SupportStaffVO supportStaffVO = serviceNumberManager.querySupportStaff(user, supportStaffForm);
        return new AjaxResult(supportStaffVO);
    }

    /**
     * 设置工单管理开关
     * @param user 用户
     * @param form 工单信息
     * @return AjaxResult
     */
    //老工单不用了
/*  @RequestMapping("/setWorkOrder")
    @ResponseBody
    public AjaxResult setWorkOrder(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody ServiceNumberForm form) {
        checkParamNotBlank(form, "参数错误"); // ignoreI18n
        checkParamNotBlank(form.getAppId(), "请选择要设置的应用"); // ignoreI18n
        checkParamRegex("" + form.getStatus(), "[0-1]{1}", "请填写有效的状态"); // ignoreI18n
        checkAppAdmin(user, form.getAppId());
        checkService(form.getAppId());
        serviceNumberManager.setWorkOrder(user, form);

        //开关工单消息写MQ
        if (CommonConstant.WORK_ORDER_ON == form.getStatus()) {
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "开启了工单", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        } else if (CommonConstant.WORK_ORDER_OFF == form.getStatus()){
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "停用了工单", OperationTypeConstant.WORKSHEET_STOP)); // ignoreI18n
        }
        return new AjaxResult(null);
    }*/

    /**
     * 设置工单管理开关(paas)
     * @param user 用户
     * @param form 工单信息
     * @return AjaxResult
     */
    @RequestMapping("/setWorkOrderPaas")
    @ResponseBody
    public AjaxResult setWorkOrderPaas(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody ServiceNumberForm form) {
        checkParamNotBlank(form, "参数错误"); // ignoreI18n
        checkParamNotBlank(form.getAppId(), "请选择要设置的应用"); // ignoreI18n
        checkParamRegex("" + form.getStatus(), "[0-1]{1}", "请填写有效的状态"); // ignoreI18n
        checkAppAdmin(user, form.getAppId());
        checkService(form.getAppId());
        serviceNumberManager.setWorkOrderPaas(user, form);

        if (CommonConstant.WORK_ORDER_ON == form.getStatus()) {
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "开启了工单", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        } else if (CommonConstant.WORK_ORDER_OFF == form.getStatus()){
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "停用了工单", OperationTypeConstant.WORKSHEET_STOP)); // ignoreI18n
        }
        return new AjaxResult(null);
    }

    /**
     * 设置问卷管理开关
     * @param user 用户
     * @param form 问卷信息
     * @return AjaxResult
     */
    @RequestMapping("/setQuestionnaire")
    @ResponseBody
    public AjaxResult setQuestionnaire(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody ServiceNumberForm form) {
        checkParamNotBlank(form, "参数错误"); // ignoreI18n
        checkParamNotBlank(form.getAppId(), "请选择要设置的应用"); // ignoreI18n
        checkParamRegex("" + form.getStatus(), "[0-1]{1}", "请填写有效的状态"); // ignoreI18n
        checkAppAdmin(user, form.getAppId());
        checkService(form.getAppId());
        serviceNumberManager.setQuestionnaire(user, form);
        //开关问卷消息写MQ
        if (CommonConstant.QUESTIONNAIRE_ON == form.getStatus()) {
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "开启了问卷", OperationTypeConstant.QUESTIONNAIRE_ENABLE)); // ignoreI18n

        } else if (CommonConstant.QUESTIONNAIRE_OFF == form.getStatus()){
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "停用了问卷", OperationTypeConstant.QUESTIONNAIRE_STOP)); // ignoreI18n

        }
        return new AjaxResult(null);
    }

    /**
     * 审批单开关
     * @param user
     * @param form
     * @return
     */
    @RequestMapping("/setApproval")
    @ResponseBody
    @Deprecated
    public AjaxResult setApproval(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                  @RequestBody ServiceNumberForm form) {
        checkParamNotBlank(form, "参数错误"); // ignoreI18n
        checkParamNotBlank(form.getAppId(), "请选择要设置的应用"); // ignoreI18n
        checkParamRegex("" + form.getStatus(), "[0-1]{1}", "请填写有效的状态"); // ignoreI18n

        checkAppAdmin(user, form.getAppId());
        checkService(form.getAppId());

        serviceNumberManager.setApproval(user, form);

        //开关审批单消息写MQ
        if (CommonConstant.APPROVAL_ON == form.getStatus()) {
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "开启了审批单", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        } else if (CommonConstant.APPROVAL_OFF == form.getStatus()){
            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), form.getAppId(),
                    user.getUserId(), System.currentTimeMillis()+ "", "停用了审批单", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        }
        return new AjaxResult(null);
    }

    /**
     * 服务号旧数据补偿操作
     * @param appId 应用ID
     * @param setType 恢复数据类型，1：当前企业  9999：所有企业
     * @param fsEa 企业ID 9999：所有企业
     * @return 处理结果
     */
    @RequestMapping("/setServiceOldDate")
    @ResponseBody
    @Deprecated
    public AjaxResult setServiceOldDate(@ModelAttribute FsUserVO user,  @RequestParam(value = "appId", required = false) String appId,
                                        @RequestParam(value = "setType", required = true) Integer setType,
                                        @RequestParam(value = "fsEa", required = true) String fsEa) {
        checkParamNotBlank(setType, "请选择要灰度的范围"); // ignoreI18n
        checkParamNotBlank(fsEa, "请选择要灰度的企业"); // ignoreI18n
        if (!fsEa.contains(";")){
            user.setEnterpriseAccount(fsEa);
            serviceNumberManager.setServiceOldDate(user, appId, setType);
            return new AjaxResult(null);
        }else {
            List<String> fsEas = Lists.newArrayList(fsEa.split(";"));
            fsEas.forEach( ea -> {
                user.setEnterpriseAccount(ea);
                serviceNumberManager.setServiceOldDate(user, appId, setType);
            });
            return new AjaxResult(null);
        }
    }
}
