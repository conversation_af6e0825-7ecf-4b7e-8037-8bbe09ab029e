package com.facishare.open.app.center.controller;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.ajax.result.AppPagerAjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.*;
import com.facishare.open.app.center.api.model.enums.*;
import com.facishare.open.app.center.api.model.vo.IconFileVO;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.AppCallBackService;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.api.utils.AppStatLogKit;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.form.ThirdpartyUrlForm;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.AppMessageToMqManager;
import com.facishare.open.app.center.manager.ServiceManager;
import com.facishare.open.app.center.manager.ServiceNumberManager;
import com.facishare.open.app.center.model.AppCreateForm;
import com.facishare.open.app.center.model.AppDevModeInfo;
import com.facishare.open.app.center.model.CustomComponentVO;
import com.facishare.open.app.center.model.UpdateComponentViewForm;
import com.facishare.open.app.center.utils.*;
import com.facishare.open.app.pay.api.enums.AppOnOffEnum;
import com.facishare.open.app.pay.api.enums.PayStatus;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.common.enums.MonitorTypeEnum;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.service.TransI18nService;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.common.utils.I18NUtils;
import com.facishare.open.oauth.model.AppServiceRefDO;
import com.facishare.open.oauth.result.CommonResult;
import com.facishare.open.oauth.result.GetServiceRefAppResult;
import com.facishare.open.oauth.service.AppServiceRefService;
import com.facishare.open.operating.center.api.model.enums.OperatingComponentTypeEnum;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.cloud.utils.JsonUtils;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.IntStream;

import static com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType.*;

@Controller
@RequestMapping("/open/appcenter/app")
public class AppController extends BaseController {

    @Resource
    private AppCallBackService appCallBackService;
    @Resource
    private AppManager appManager;
    @Resource
    private ServiceNumberManager serviceNumberManager;
    @Value("${fs.open.app.center.AppController.createCustomApp.status}")
    private String createCustomAppStatus;
    @Resource
    private AppMessageToMqManager appMessageToMqManager;
    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;
    @Resource
    private AppServiceRefService appServiceRefService;
    @Resource
    private ServiceManager serviceManager;
    @Resource
    private OperationLogService operationLogService;
    @Resource
    private PaaSAppRestService paaSAppRestService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private TempFileToFormalFile tempFileToFormalFile;

    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;
    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;

    @Resource
    private TransI18nService transI18nService;

    private static String ACCESS_EA_APP_STATUS_PRE = "acc.e.a.sta.%s.%s";//企业id,appId

    /**
     * 【新管理后台】更新应用的基本信息（名称、描述及应用管理员）
     *
     * @param user    操作者.
     * @param appForm 应用的基本信息
     * @return 应用id.
     */
    @RequestMapping(value = "/updateAppBaseInfo")
    @ResponseBody
    public AjaxResult updateAppBaseInfo(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody AppCreateForm appForm) throws Exception {
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择应用."); // ignoreI18n
        //appStatus 不为null 更新应用状态
        if (null != appForm.getAppStatus()) {
            appManager.saveApplicationIfToPaas(user, appForm.getAppId(), appForm.getAppStatus());
            return new AjaxResult(AjaxCode.OK);
        }
        if (null == appForm.getAppAdmins() || appForm.getAppAdmins().length == 0) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "请填写应用管理员"); // ignoreI18n
        }

        OpenAppDO briefApp = appManager.loadAppBrief(appForm.getAppId());
        checkRightForUpdateAppBaseInfo(user, appForm, briefApp);

        // 更新管理员
        if (appManager.hasChangedAppAdmin(user.getEa(), appForm.getAppId(), appForm.getAppAdmins())) {
            checkIsAllowUpdateAppAdmin(appForm.getAppId());
            updateAppAdmins(user, briefApp, appForm.getAppAdmins());
        }

        // 纷享应用(非自建应用)不可修改名称及描述
        if (Objects.equals(AppCenterEnum.AppType.CUSTOM_APP.value(), briefApp.getAppType())) {
            // 更新名称
            if (!Objects.equals(briefApp.getAppName(), appForm.getAppName())) {
                updateCustomAppName(user, lang, briefApp, appForm.getAppName());
            }
            // 更新描述
            if (!Objects.equals(briefApp.getAppDesc(), appForm.getAppDesc())) {
                updateCustomAppDesc(user, lang, briefApp.getAppId(), appForm.getAppDesc());
            }
        }

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("appId", appForm.getAppId());
        if (Objects.nonNull(appForm.getLogoAddressFile())) {
            resultMap.put("logoUrl", appManager.queryAppIconUrl(appForm.getAppId(), IconType.WEB));
        }
        return new AjaxResult(resultMap);
    }

    private void checkIsAllowUpdateAppAdmin(String appId) {
        if (Objects.nonNull(ConfigCenter.NOT_ALLOW_UPDATE_APP_ADMIN_APP_IDS)
                && Arrays.asList(ConfigCenter.NOT_ALLOW_UPDATE_APP_ADMIN_APP_IDS.split(",")).contains(appId)) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "当前应用不允许修改管理员"); // ignoreI18n
        }
    }

    private void checkRightForUpdateAppBaseInfo(FsUserVO user, AppCreateForm appForm, OpenAppDO briefApp) {
        boolean isAppAdmin = isAppAdmin(user, appForm.getAppId());
        boolean isCustomApp = Objects.equals(AppCenterEnum.AppType.CUSTOM_APP.value(), briefApp.getAppType());
        if (isCustomApp) {
            boolean hasAppCustomManageFunctionCode = webAuthManager.hasAppCustomManageFunctionCode(user);
            if (!isAppAdmin && !hasAppCustomManageFunctionCode) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        } else {
            boolean hasAppBaseManageFunctionCode = webAuthManager.hasAppBaseManageFunctionCode(user);
            if (!isAppAdmin && !hasAppBaseManageFunctionCode) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        }
    }

    /**
     * 【新管理后台】更新应用的基本信息（图片、名称、描述及应用管理员）
     *
     * @param user    操作者.
     * @param appForm 应用的基本信息
     * @return 应用id.
     */
    @RequestMapping("/updateAppBaseInfoWithLogo")
    @ResponseBody
    public AjaxResult updateAppBaseInfoWithLogo(@ModelAttribute FsUserVO user, @ModelAttribute String lang, AppCreateForm appForm) throws Exception {
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择应用."); // ignoreI18n
        if (StringUtils.isEmpty(appForm.getAppLogo()) && Objects.isNull(appForm.getLogoAddressFile())) {
            throw new BizException(AjaxCode.PARAM_ERROR, "请选择应用的logo"); // ignoreI18n
        }
        if (null == appForm.getAppAdmins() || appForm.getAppAdmins().length == 0) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "请填写应用管理员"); // ignoreI18n
        }

        OpenAppDO briefApp = appManager.loadAppBrief(appForm.getAppId());
        checkRightForUpdateAppBaseInfo(user, appForm, briefApp);

        // 更新管理员
        if (appManager.hasChangedAppAdmin(user.getEa(), appForm.getAppId(), appForm.getAppAdmins())) {
            checkIsAllowUpdateAppAdmin(appForm.getAppId());
            updateAppAdmins(user, briefApp, appForm.getAppAdmins());
        }

        // 纷享应用(非自建应用)不可修改图片/名称/描述
        if (Objects.equals(AppCenterEnum.AppType.CUSTOM_APP.value(), briefApp.getAppType())) {
            // 更新图片
            if (Objects.nonNull(appForm.getAppLogo())) {
                updateCustomAppLogo(user, lang, appForm.getAppId(), appForm.getLogoAddressFile(), appForm.getAppLogo());
            }
            if (Objects.nonNull(appForm.getLogoAddressFile())) {
                updateCustomAppLogo(user, lang, appForm.getAppId(), appForm.getLogoAddressFile(), null);
            }
            // 更新名称
            if (!Objects.equals(briefApp.getAppName(), appForm.getAppName())) {
                updateCustomAppName(user, lang, briefApp, appForm.getAppName());
            }
            // 更新描述
            if (!Objects.equals(briefApp.getAppDesc(), appForm.getAppDesc())) {
                updateCustomAppDesc(user, lang, briefApp.getAppId(), appForm.getAppDesc());
            }
        }

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("appId", appForm.getAppId());
        if (Objects.nonNull(appForm.getLogoAddressFile())) {
            resultMap.put("logoUrl", appManager.queryAppIconUrl(appForm.getAppId(), IconType.WEB));
        }
        return new AjaxResult(resultMap);
    }


    /**
     * 查询开放平台的应用,用于展示更多应用.
     *
     * @param user        用户.
     * @param currentPage 当前页
     * @param pageSize    页大小.默认为10.
     * @return AjaxResult.
     */
    @RequestMapping("/loadAppList")
    @ResponseBody
    @Deprecated
    public AppPagerAjaxResult loadAppList(@ModelAttribute FsUserVO user,
                                          @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        checkFsAdmin(user);//验证是否为系统管理员.
        Pager<Map<String, Object>> appDoPager = appManager.loadAppList(user, currentPage, pageSize);
        return new AppPagerAjaxResult(appDoPager);
    }

    /**
     * 管理后台加载单个应用及其组件信息.
     *
     * @param user  管理员或应用管理员登录.
     * @param appId 应用id.
     * @return AjaxResult.
     */
    @RequestMapping("/loadAppByAppId")
    @ResponseBody
    public AjaxResult loadAppByAppId(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                     @RequestParam(value = "appId", required = false) String appId) {
        try {
            RequestContextManager.initContextForIsFromManage(true);
            checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
            Map<String, Object> ajaxDataMap = appManager.loadAppByAppId(user, appId, lang);
            return new AjaxResult(ajaxDataMap);
        } finally {
            RequestContextManager.removeContext();
        }

    }

    @RequestMapping("/queryAppScope")
    @ResponseBody
    public AjaxResult queryAppScope(@ModelAttribute FsUserVO fsAdmin,
                                    @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkFsAdmin(fsAdmin);
        final Map<String, Object> ajaxData = appManager.queryAppScope(fsAdmin, appId);
        ajaxData.put("appId", appId);
        return new AjaxResult(ajaxData);
    }

    /**
     * 加载单个应用用于显示在点击组件后的中间转换页
     *
     * @param user        普通人员即可.
     * @param componentId 应用组件id.
     * @return 应用信息.
     */
    @RequestMapping("/loadAppDtlByComponentId")
    @ResponseBody
    public AjaxResult loadAppDtlByComponentId(@ModelAttribute FsUserVO user,
                                              @RequestParam(value = "componentId", required = false) String componentId) {
        checkParamNotBlank(componentId, "请选择应用组件"); // ignoreI18n
        Map<String, Object> ajaxDataMap = appManager.loadAppDtlByComponentId(user, componentId);
        return new AjaxResult(ajaxDataMap);

    }

    /**
     * 获取未授权应用的详情信息, 包括应用的基本信息和开发者.（包括开发者信息）
     *
     * @param appId 应用appId.
     * @return 应用信息.
     */
    @RequestMapping("/loadAppDetailByAppId")
    @ResponseBody
    public AjaxResult loadAppDetailByAppId(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                           @RequestParam(value = "appId", required = false) String appId,
                                           HttpServletResponse response) {
        checkParamNotBlank(appId, "请选择应用组件"); // ignoreI18n
        //加载应用的appId, name, desc, logo, 开发者, 收费类型, 按钮名称, 按钮的跳转链接
        //( 企业试用则跳到授权页面, 个人试用则跳到"提醒管理员添加"页面
        Map<String, Object> ajaxDataMap = appManager.loadAppDetailByAppId(user, appId, lang);
        //app端H5页面需要用户信息
        try {
            BaseResult<List<Employee>> employeeListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(user.getEnterpriseAccount(), Lists.newArrayList(user.getUserId()));
            if (!employeeListResult.isSuccess()) {
                logger.warn("loadAppByAppId getEmployeesNoAdminId failed, currentFsEa[{}], employeeIds[{}], employeeListResult[{}]",
                        user.getEnterpriseAccount(), user.getUserId(), employeeListResult);
            } else {
                if (!CollectionUtils.isEmpty(employeeListResult.getResult())) {
                    response.setIntHeader("userId", user.getUserId());
                    response.setHeader("userName", URLEncoder.encode(employeeListResult.getResult().get(0).getName(), "utf-8"));
                }
            }
        } catch (Exception e) {
            logger.error("loadAppByAppId getEmployeesNoAdminId error! currentFsEa[{}], employeeIds[{}]", user.getEnterpriseAccount(), user.getUserId(), e);
        }
        return new AjaxResult(ajaxDataMap);
    }

    @RequestMapping("/loadAppBrief")
    @ResponseBody
    public AjaxResult loadAppBrief(@ModelAttribute FsUserVO user,
                                   @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用组件"); // ignoreI18n
        final OpenAppDO appDO = appManager.loadAppBrief(appId);
        Map<String, Object> ajaxDataMap = new HashMap<>();
        ajaxDataMap.put("appId", appDO.getAppId());
        ajaxDataMap.put("appName", appDO.getAppName());
        ajaxDataMap.put("appDesc", appDO.getAppDesc());
        ajaxDataMap.put("isAdmin", webAuthManager.isFsAdmin(user) ? 1 : 0);
        return new AjaxResult(ajaxDataMap);
    }

    /**
     * 添加一个自定义应用.
     *
     * @param user    普通人员即可.
     * @param appForm 应用表单.
     * @return 应用id.
     */
    @RequestMapping("/createCustomApp")
    @ResponseBody
    @Deprecated
    public AjaxResult createCustomApp(@ModelAttribute FsUserVO user, AppCreateForm appForm) throws Exception {
        if (!"true".equals(createCustomAppStatus)) {
            logger.debug("AppController.createCustomAppStatus : [" + createCustomAppStatus + "]");
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "接口不可用."); // ignoreI18n
        }
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        checkParamRegex(appForm.getAppName(), ConfigCenter.APP_NAME_CHECK_REGEX, "请填写有效的应用名称"); // ignoreI18n
        checkParamRegex(appForm.getAppDesc(), "^[\\s\\S]{1,512}$", "请填写有效的功能介绍"); // ignoreI18n

        if (null == appForm.getAppAdmins() || appForm.getAppAdmins().length < 1) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "请填写管理员"); // ignoreI18n
        }
        for (Integer admin : appForm.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员名称不合法"); // ignoreI18n
            }
        }
        checkParamNotBlank(appForm.getLogoAddressFile(), "请选择应用的logo"); // ignoreI18n
        checkParamTrue(FileCheckUtils.isImageFile(appForm.getLogoAddressFile().getOriginalFilename()), "图片格式不符合"); // ignoreI18n

        if (!CollectionUtils.isEmpty(appForm.getAppComponents())) {
            for (CustomComponentVO c : appForm.getAppComponents()) {
                try {
                    c.checkParamsForCreation();
                } catch (IllegalArgumentException e) {
                    return new AjaxResult(AjaxCode.PARAM_ERROR, e.getMessage());
                }
            }
        }

        MultipartFile original = appForm.getLogoAddressFile();
        IconFileVO iconFileVO = null;
        if (original != null) {
            BufferedImage image = ImageIO.read(original.getInputStream());
            iconFileVO = new IconFileVO(original.getBytes(), original.getOriginalFilename(), image.getWidth(),
                    image.getHeight());
        }
        //需要系统管理员权限
        checkFsAdmin(user);

        // 2.保存应用。
        appManager.createCustomApp(user, appForm, iconFileVO, AppCenterEnum.AppType.CUSTOM_APP);
        return new AjaxResult(Collections.singletonMap("appId", appForm.getAppId()));
    }

    /**
     * 更新自定义应用的名称.
     *
     * @param user    操作者.
     * @param appForm 新的应用名称.
     * @return 应用id.
     */
    @RequestMapping("/updateCustomAppName")
    @ResponseBody
    public AjaxResult updateCustomAppName(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody AppCreateForm appForm) {
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择应用."); // ignoreI18n
        checkAppAdmin(user, appForm.getAppId());
        OpenAppDO briefApp = appManager.loadAppBrief(appForm.getAppId());
        updateCustomAppName(user, lang, briefApp, appForm.getAppName());
        return new AjaxResult(appForm.getAppId());
    }

    private void updateCustomAppName(FsUserVO user, String lang, OpenAppDO briefApp, String newAppName) {
        String appId = briefApp.getAppId();

        if (briefApp.getAppType() == AppCenterEnum.AppType.SERVICE.value()
                || briefApp.getAppType() == AppCenterEnum.AppType.OUT_SERVICE_APP.value()
                || briefApp.getAppType() == AppCenterEnum.AppType.LINK_SERVICE.value()) {
            checkParamRegex(newAppName, ConfigCenter.SERVICE_NAME_CHECK_REGEX, "请填写有效的服务号名称"); // ignoreI18n
        } else {
            checkParamRegex(newAppName, ConfigCenter.APP_NAME_CHECK_REGEX, "请填写有效的应用名称"); // ignoreI18n
        }

        if (!appManager.checkAppName(briefApp.getAppId(), newAppName)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "新名称已经存在"); // ignoreI18n
        }
        OpenAppDO app = new OpenAppDO();
        app.setAppId(appId);
        app.setGmtModified(new Date());
        app.setAppName(newAppName);
        app.setServiceName(newAppName);
        appManager.updateApp(user, app);
        syncTranslateValue(app, user, lang);

        if (linkServiceManager.isLinkService(appId)) {
            linkServiceManager.updateAppName(user, appId, user.getEnterpriseAccount(), newAppName);
        } else {
            serviceManager.updateSessionNameLogoDesc(user, appId, app.getServiceName(), null, null);
        }

        // 修改应用的名称
        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                user.getUserId(), System.currentTimeMillis() + "", "修改名称为\"" + newAppName + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
    }

    private void syncTranslateValue(OpenAppDO app, FsUserVO user, String lang) {
        int tenantId = eieaConverter.enterpriseAccountToId(user.getEnterpriseAccount());
        Map<String, String> keyToNewName = Maps.newHashMap();
        String customAppNameKey = com.facishare.open.common.utils.I18NUtils.getCustomAppNameKey(app.getAppId());
        keyToNewName.put(customAppNameKey, app.getAppName());
        transI18nService.syncTransValue(tenantId, keyToNewName, lang);
    }

    /**
     * 更新应用的描述.
     *
     * @param user    操作者.
     * @param appForm 新的应用明细.
     * @return 应用id.
     */
    @RequestMapping("/updateCustomAppDesc")
    @ResponseBody
    public AjaxResult updateCustomAppDesc(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody AppCreateForm appForm) {
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择应用."); // ignoreI18n
        checkAppAdmin(user, appForm.getAppId());
        updateCustomAppDesc(user, lang, appForm.getAppId(), appForm.getAppDesc());
        return new AjaxResult(appForm.getAppId());
    }

    private void updateCustomAppDesc(FsUserVO user, String lang, String appId, String newAppDesc) {
        checkParamRegex(newAppDesc, "^[\\s\\S]{1,512}$", "请填写有效的功能介绍"); // ignoreI18n
        OpenAppDO app = new OpenAppDO();
        app.setAppId(appId);
        app.setGmtModified(new Date());
        app.setAppDesc(newAppDesc);
        appManager.updateApp(user, app);

        if (linkServiceManager.isLinkService(appId)) {
            linkServiceManager.updateAppDesc(user, appId, user.getEnterpriseAccount(), newAppDesc);
        } else {
            serviceManager.updateSessionNameLogoDesc(user, appId, null, null, newAppDesc);
        }

        // 修改应用的功能介绍
        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                user.getUserId(), System.currentTimeMillis() + "", "修改功能介绍为\"" + newAppDesc + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
    }

    /**
     * 更新应用的logo.
     *
     * @param user    操作者.
     * @param appForm 新的应用 logo.
     * @return 应用id.
     */
    @RequestMapping("/updateCustomAppLogo")
    @ResponseBody
    public AjaxResult updateCustomAppLogo(@ModelAttribute FsUserVO user, @ModelAttribute String lang, AppCreateForm appForm) throws Exception {
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择应用."); // ignoreI18n
        checkParamNotBlank(appForm.getLogoAddressFile(), "请选择应用的logo"); // ignoreI18n

        checkAppAdmin(user, appForm.getAppId());

        updateCustomAppLogo(user, lang, appForm.getAppId(), appForm.getLogoAddressFile(), null);

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("appId", appForm.getAppId());
        resultMap.put("logoUrl", appManager.queryAppIconUrl(appForm.getAppId(), IconType.WEB));
        return new AjaxResult(resultMap);
    }

    private void updateCustomAppLogo(FsUserVO user, String lang, String appId, CommonsMultipartFile logoAddressFile, String appLogo) throws IOException {
        //使用新的上传logo
        if (StringUtils.isNotBlank(appLogo)) {
            appManager.updateCustomAppLogo(user, appId, appLogo);
        } else {
            MultipartFile original = logoAddressFile;
            IconFileVO iconFileVO = null;
            if (original != null) {
                BufferedImage image;
                try {
                    image = ImageIO.read(original.getInputStream());
                } catch (IOException e) {
                    logger.warn("ImageIO.read(file) is error!", e);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "无法解析您上传的图片，该图片可能采用了比较特殊的编码格式或颜色空间"); // ignoreI18n
                }
                iconFileVO = new IconFileVO(original.getBytes(), original.getOriginalFilename(), image.getWidth(),
                        image.getHeight());
            }
            appManager.updateCustomAppLogo(user, appId, iconFileVO);
        }
        String logoUrl = appManager.queryAppIconUrl(appId, IconType.SERVICE);
        if (linkServiceManager.isLinkService(appId)) {
            linkServiceManager.updateAppLogo(user, appId, user.getEnterpriseAccount(), logoUrl);
        } else {
            serviceManager.updateSessionNameLogoDesc(user, appId, null, logoUrl, null);
        }

        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                user.getUserId(), System.currentTimeMillis() + "", "修改了LOGO", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
    }

    /**
     * 创建自定义应用组件.
     *
     * @param user              操作者.
     * @param customComponentVO 组件信息.
     * @return 组件id.
     */
    @RequestMapping("createCustomComponent")
    @ResponseBody
    public AjaxResult createCustomComponent(@ModelAttribute FsUserVO user, @ModelAttribute String lang, CustomComponentVO customComponentVO) {
        checkParamNotBlank(customComponentVO, "请填写表单"); // ignoreI18n
        try {
            customComponentVO.checkParamsForCreation();
        } catch (IllegalArgumentException e) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, e.getMessage());
        }
        checkParamNotBlank(customComponentVO.getAppId(), "请选择应用"); // ignoreI18n

        checkAppAdmin(user, customComponentVO.getAppId());

        AppResult appResult = openAppService.loadOpenAppFast(customComponentVO.getAppId());
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在."); // ignoreI18n
        }
        // 应用类型判断
        if (SERVICE.value() == appResult.getResult().getAppType()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "服务号不能创建应用组件."); // ignoreI18n
        }
        final String componentId = appManager.createCustomComponent(user, customComponentVO);
        // 自建应用添加app组件或者web组件写mq
        checkParamNotBlank(customComponentVO.getComponentType(), "组件参数错误"); // ignoreI18n
        try {
            if (AppComponentTypeEnum.APP.getType() == customComponentVO.getComponentType()) {
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                        user.getUserId(), System.currentTimeMillis() + "", "添加了App应用入口", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            } else if (AppComponentTypeEnum.WEB.getType() == customComponentVO.getComponentType()) {
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                        user.getUserId(), System.currentTimeMillis() + "", "添加了Web应用入口", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            }
        } catch (Exception e) {
            logger.warn("createCustomComponent send Mq failed , user[{}], customComponentVO[{}]", user, customComponentVO, e);
        }

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("componentId", componentId);
            map.put("componentName", customComponentVO.getComponentName());
            map.put("componentType", customComponentVO.getComponentType());
            map.put("loginUrl", customComponentVO.getLoginUrl());
            map.put("view", customComponentVO.getView());
            map.put("componentDesc", customComponentVO.getComponentDesc());
            map.put("appId", customComponentVO.getAppId());
            map.put("action", BuriedPointBizEnum.CREATE_CUSTOM_APP_COMPONENT.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CREATE_CUSTOM_APP_COMPONENT, map, e);
        }
        if (GraySwitch.isAllowForEa(GraySwitch.clearAppCache, user.getEnterpriseAccount())) {
            appManager.clearCache(user);
        }
        return new AjaxResult(componentId);
    }

    /**
     * 更新自定义应用组件.
     *
     * @param user              操作者.
     * @param customComponentVO 组件信息.
     * @return 是否更新成功.
     */
    @RequestMapping("/updateCustomComponent")
    @ResponseBody
    public AjaxResult updateCustomComponent(@ModelAttribute FsUserVO user, CustomComponentVO customComponentVO) {
        checkParamNotBlank(customComponentVO, "请填写表单"); // ignoreI18n
        try {
            customComponentVO.checkParamsForUpdate();
        } catch (IllegalArgumentException e) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, e.getMessage());
        }
        checkParamNotBlank(customComponentVO.getComponentId(), "请选择组件"); // ignoreI18n

        checkAppAdmin(user, appManager.getAppIdByComponentId(user, customComponentVO.getComponentId()));

        appManager.updateCustomComponent(user, customComponentVO);
        return SUCCESS;

    }

    /**
     * 更新自定义应用组件,同时更新组件名称.
     *
     * @param user              操作者.
     * @param customComponentVO 组件信息.
     * @return 是否更新成功.
     */
    @RequestMapping("/updateCustomComponentWithView")
    @ResponseBody
    public AjaxResult updateCustomComponentWithView(@ModelAttribute FsUserVO user, @ModelAttribute String lang, CustomComponentVO customComponentVO) {
        checkParamNotBlank(customComponentVO, "请选择组件"); // ignoreI18n
        checkParamNotBlank(customComponentVO.getAppId(), "请选择组件"); // ignoreI18n

        //验证应用管理员
        String appId = customComponentVO.getAppId();
        checkAppAdmin(user, appId);
        //保存图标
        String icon;
        if (GraySwitch.isAllowForEa(GraySwitch.useNewIconPath, user.getEnterpriseAccount())) {
            icon = tempFileToFormalFile.tNPathToCPath(user.getEnterpriseAccount(), user.getUserId(), customComponentVO.getComponentLogo());
        } else {
            icon = tempFileToFormalFile.tNPathToAPath(user.getEnterpriseAccount(), user.getUserId(), customComponentVO.getComponentLogo());
        }
        customComponentVO.setComponentLogo(icon);

        //调出原始的应用信息,比对得到更新
        try {
            CustomComponentVO oldCustomComponent = appManager.queryComponentInfo(user, customComponentVO.getComponentId());
            if (null != oldCustomComponent) {
                if (StringUtils.isNotBlank(customComponentVO.getComponentLogo()) && !customComponentVO.getComponentLogo().equals(oldCustomComponent.getComponentLogo())) {
                    // 修改App应用入口图标
                    if (AppComponentTypeEnum.APP.getType() == customComponentVO.getComponentType()) {
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                user.getUserId(), System.currentTimeMillis() + "", "修改了App应用入口图标", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n

                    } else if (AppComponentTypeEnum.WEB.getType() == customComponentVO.getComponentType()) {
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                user.getUserId(), System.currentTimeMillis() + "", "修改了Web应用入口图标", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n

                    }
                }
                // 修改App应用入口名称 xxx
                if (StringUtils.isNotBlank(customComponentVO.getComponentName()) && !customComponentVO.getComponentName().equals(oldCustomComponent.getComponentName())) {
                    if (AppComponentTypeEnum.APP.getType() == customComponentVO.getComponentType()) {
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                user.getUserId(), System.currentTimeMillis() + "", "修改了App应用入口名称为\"" + customComponentVO.getComponentName() + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n

                    } else if (AppComponentTypeEnum.WEB.getType() == customComponentVO.getComponentType()) {
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                user.getUserId(), System.currentTimeMillis() + "", "修改了Web应用入口名称为\"" + customComponentVO.getComponentName() + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                    }
                }
                // 修改App应用入口地址
                if (StringUtils.isNotBlank(customComponentVO.getLoginUrl()) && !customComponentVO.getLoginUrl().equals(oldCustomComponent.getLoginUrl())) {
                    if (AppComponentTypeEnum.APP.getType() == customComponentVO.getComponentType()) {
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                user.getUserId(), System.currentTimeMillis() + "", "修改了App应用入口地址为\"" + customComponentVO.getLoginUrl() + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                    } else if (AppComponentTypeEnum.WEB.getType() == customComponentVO.getComponentType()) {
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                user.getUserId(), System.currentTimeMillis() + "", "修改了Web应用入口地址为\"" + customComponentVO.getLoginUrl() + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                    }
                }
                // 修改App应用入口可见范围
                if (StringUtils.isNotBlank(customComponentVO.getView()) && StringUtils.isNotBlank(oldCustomComponent.getView())) {
                    AppViewDO viewDo = JsonKit.json2object(customComponentVO.getView(), AppViewDO.class);
                    AppViewDO oldView = JsonKit.json2object(oldCustomComponent.getView(), AppViewDO.class);
                    // 获取department和view
                    Integer[] departmentArray = viewDo.getDepartment();
                    Integer[] memberArray = viewDo.getMember();
                    String[] roleIdArray = viewDo.getRole();
                    departmentArray = null == departmentArray ? new Integer[]{} : departmentArray;
                    memberArray = null == memberArray ? new Integer[]{} : memberArray;
                    Arrays.sort(departmentArray);
                    Arrays.sort(memberArray);
                    // 获取原来的department和view
                    Integer[] oldDepartArray = oldView.getDepartment();
                    Integer[] oldMemberArray = oldView.getMember();
                    oldDepartArray = null == oldDepartArray ? new Integer[]{} : oldDepartArray;
                    oldMemberArray = null == oldMemberArray ? new Integer[]{} : oldMemberArray;
                    Arrays.sort(oldDepartArray);
                    Arrays.sort(oldMemberArray);
                    // 判断是否匹配
                    if (!Arrays.toString(departmentArray).equals(Arrays.toString(oldDepartArray)) || !Arrays.toString(memberArray).equals(Arrays.toString(oldMemberArray))) {
                        if (AppComponentTypeEnum.APP.getType() == customComponentVO.getComponentType()) {
                            //运营中心记录日志（fs-open-app-operating）,从mq改成异步调用
                            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                    user.getUserId(), System.currentTimeMillis() + "", "修改了App应用入口可见范围", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n

                        } else if (AppComponentTypeEnum.WEB.getType() == customComponentVO.getComponentType()) {

                            //运营中心记录日志（fs-open-app-operating）,从mq改成异步调用
                            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), customComponentVO.getAppId(),
                                    user.getUserId(), System.currentTimeMillis() + "",  "修改了Web应用入口可见范围", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("updateCustomComponentWithView send MQ Message failed , user[{}], customComponentVO[{}]", user, customComponentVO, e);
        }

        AjaxResult result = updateCustomComponent(user, customComponentVO);
        syncComponentName(eieaConverter.enterpriseAccountToId(user.getEnterpriseAccount()), customComponentVO, lang);

        if (AjaxCode.OK != result.getErrCode()) {
            return result;
        }
        UpdateComponentViewForm form = new UpdateComponentViewForm();
        form.setComponentId(customComponentVO.getComponentId());
        form.setView(customComponentVO.getView());

        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在."); // ignoreI18n
        }

        if (BizCommonUtils.getCrmComponentId().equals(form.getComponentId())) {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "配置使用人员请到CRM设置角色"); // ignoreI18n
        }
        if (GraySwitch.isAllowForEa(GraySwitch.clearAppCache, user.getEnterpriseAccount())) {
            appManager.clearCache(user);
        }
        AppViewDO viewDo = JsonKit.json2object(form.getView(), AppViewDO.class);
        if (GraySwitch.isAllowForEa(GraySwitch.useNewPaasAppGray, user.getEa())) {
            openFsUserAppViewService.saveAppComponentView(user, form.getComponentId(), customComponentVO.getComponentType(), viewDo);
        } else {
            // 修改企业自建应用适用范围旧
            //验证应用启用停用状态
            appManager.checkAppOnOffStatus(appId, user.getEnterpriseAccount());
            appManager.updateComponentView(user, form.getComponentId(), viewDo);
        }

        return new AjaxResult(form.getComponentId());
    }

    private void syncComponentName(int ei, CustomComponentVO customComponentVO, String lang) {
        Map<String, String> keyToNewName = new HashMap<>();
        keyToNewName.put(I18NUtils.getCustomAppEntryNameKey(customComponentVO.getAppId(), customComponentVO.getComponentId()), customComponentVO.getComponentName());
        transI18nService.syncTransValue(ei, keyToNewName, lang);
    }


    /**
     * 删除自定义应用组件.
     *
     * @param user        操作者.
     * @param componentId 组件id.
     * @return 是否更新成功
     */
    @RequestMapping("/deleteCustomComponent")
    @ResponseBody
    public AjaxResult deleteCustomComponent(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestParam(value = "componentId", required = false) final String componentId) {
        checkParamNotBlank(componentId, "请选择组件"); // ignoreI18n
        OpenAppComponentDO openAppComponentDO = appManager.getComponentByComponentId(user, componentId);
        String appId = openAppComponentDO.getAppId();
        checkAppAdmin(user, appId);
        //删除app组件或者web组件写mq
        checkParamNotBlank(openAppComponentDO.getComponentType(), "组件参数有误"); // ignoreI18n
        try {
            if (AppComponentTypeEnum.APP.getType() == openAppComponentDO.getComponentType()) {
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                        user.getUserId(), System.currentTimeMillis() + "", "删除了App应用入口", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            } else if (AppComponentTypeEnum.WEB.getType() == openAppComponentDO.getComponentType()) {
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                        user.getUserId(), System.currentTimeMillis() + "", "删除了Web应用入口", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            }
        } catch (Exception e) {
            logger.warn("deleteCustomComponent send MQ failed , user[{}], componentId[{}]", user, componentId, e);
        }
        appManager.deleteCustomComponent(user, componentId);
        return SUCCESS;
    }

    /**
     * 更新应用的应用管理员.
     *
     * @param user    操作者.
     * @param appForm 新的应用管理员
     * @return 应用id.
     */
    @RequestMapping("/updateAppAdmins")
    @ResponseBody
    public AjaxResult updateAppAdmins(@ModelAttribute FsUserVO user, @RequestBody AppCreateForm appForm) {
        checkParamNotBlank(appForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择应用"); // ignoreI18n

        if (null == appForm.getAppAdmins() || appForm.getAppAdmins().length == 0) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "请填写应用管理员"); // ignoreI18n
        }

        OpenAppDO openAppDO = appManager.loadAppBrief(appForm.getAppId());

        // 权限校验
        if (linkServiceManager.isLinkService(appForm.getAppId())) {
            if (!linkServiceManager.isUpstreamLinkAdmin(user) && !isAppAdmin(user, appForm.getAppId())) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        } else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == openAppDO.getAppType()) {
            if (!webAuthManager.isLinkAdmin(user) && !isAppAdmin(user, appForm.getAppId())) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        } else {
            if (!isFsAdmin(user) && !isAppAdmin(user, appForm.getAppId())) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_OR_APP_ADMIN", "操作需要系统管理员或者应用管理员权限"); // ignoreI18n
            }
        }

        checkIsAllowUpdateAppAdmin(appForm.getAppId());
        updateAppAdmins(user, openAppDO, appForm.getAppAdmins());
        return new AjaxResult(appForm.getAppId());
    }

    private void updateAppAdmins(FsUserVO user, OpenAppDO openAppDO, Integer[] appAdmins) {
        String appId = openAppDO.getAppId();
        //验证应用启用停用状态
        appManager.checkAppOnOffStatus(appId, user.getEnterpriseAccount());
        appManager.updateCustomAppAdmins(user, appId, appAdmins);

        // 更新企信工作台Sesison
        List<Integer> addAdminIdList = Lists.newArrayList(appAdmins);
        if (AppCenterEnum.AppType.SERVICE.value() == openAppDO.getAppType()) {
            PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
            platformMetaSessionVO.setAdmins(addAdminIdList);
            serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, CommonConstant.IS_SERVICE);
        } else if (AppCenterEnum.AppType.LINK_SERVICE.value() == openAppDO.getAppType()) {
            linkServiceManager.updateAppAdmins(user, appId, addAdminIdList);
        }

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("appId", appId);
            map.put("appAdmins", appAdmins);
            map.put("action", BuriedPointBizEnum.MODIFY_APP_ADMINS.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.MODIFY_APP_ADMINS, map, e);
        }
    }

    /**
     * 更新服务号组件的可见范围.
     *
     * @param user 操作者.
     * @param form 表单
     * @return 组件id.
     */
    @RequestMapping("/updateComponentView")
    @ResponseBody
    public AjaxResult updateComponentView(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody UpdateComponentViewForm form) {
        checkParamNotBlank(form, "请填写表单"); // ignoreI18n
        checkParamNotBlank(form.getComponentId(), "请选择应用组件"); // ignoreI18n
        checkParamNotBlank(form.getView(), "请设置可见范围"); // ignoreI18n
        OpenAppComponentDO openAppComponentDO = appManager.getComponentByComponentId(user, form.getComponentId());
        checkParamNotBlank(openAppComponentDO, "应用组件信息有误"); // ignoreI18n
        String appId = openAppComponentDO.getAppId();

        //验证应用管理员
        checkAppAdmin(user, appId);

        checkIsAllowUpdateComponentView(appId);

        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在."); // ignoreI18n
        }

        if (BizCommonUtils.getCrmComponentId().equals(form.getComponentId())) {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "配置使用人员请到CRM设置角色"); // ignoreI18n
        }

        //验证应用启用停用状态(状态跟可见范围)
        //appManager.checkAppOnOffStatus(appId, user.getEnterpriseAccount());

        AppViewDO viewDo = JsonKit.json2object(form.getView(), AppViewDO.class);
        if (Objects.isNull(viewDo)) {
            viewDo = new AppViewDO();
        }
        appManager.updateComponentView(user, form.getComponentId(), viewDo);

        try {
            if (null != appResult.getResult()) {
                if (IntStream.of(SERVICE.value(), BASE_SERVICE_APP.value(), EXT_SERVICE_APP.value()).anyMatch(v -> v == appResult.getResult().getAppType())) {
                    // 服务号则显示修改订阅范围
                    //运营中心记录日志（fs-open-app-operating）,从mq改成异步调用
                    CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                            user.getUserId(), System.currentTimeMillis() + "", OperatingComponentTypeEnum.UPDATE_SUBSCRIPTION_RANGE.getDesc(), OperationTypeConstant.WORKSHEET_ENABLE));
                } else if (LINK_SERVICE.value() == appResult.getResult().getAppType()) {
                    // 修改互联服务号内部可见范围
                    CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                            user.getUserId(), System.currentTimeMillis() + "", OperatingComponentTypeEnum.UPDATE_INNER_SUBSCRIPTION_RANGE.getDesc(), OperationTypeConstant.WORKSHEET_ENABLE));
                } else {
                    if (AppComponentTypeEnum.APP.getType() == openAppComponentDO.getComponentType()) {
                        // 修改app可见范围
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                                user.getUserId(), System.currentTimeMillis() + "", "修改了App可见范围", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                    } else if (AppComponentTypeEnum.WEB.getType() == openAppComponentDO.getComponentType()) {
                        // 修改web可见范围
                        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                                user.getUserId(), System.currentTimeMillis() + "", "修改了Web可见范围", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("updateComponentView send MQ failed, user[{}],form[{}]", user, form, e);
        }
        return new AjaxResult(form.getComponentId());
    }

    private void checkIsAllowUpdateComponentView(String appId) {
        if (Objects.nonNull(ConfigCenter.NOT_ALLOW_UPDATE_COMPONENT_VIEW_APP_IDS)
                && Arrays.asList(ConfigCenter.NOT_ALLOW_UPDATE_COMPONENT_VIEW_APP_IDS.split(",")).contains(appId)) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "当前应用不允许组件可见范围"); // ignoreI18n
        }
    }

    /**
     * 删除自定义应用.
     *
     * @param user  操作人.
     * @param appId 应用id.
     * @return 是否成功.
     */
    @RequestMapping("/deleteCustomApp")
    @ResponseBody
    public AjaxResult deleteCustomApp(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                      @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(appResult);
        }
        OpenAppDO openAppDO = appResult.getResult();

        // 校验权限
        if (linkServiceManager.isLinkService(appId)) {
            checkUpstreamLinkAdmin(user);
        } else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == openAppDO.getAppType()) {
            if (!webAuthManager.isLinkAdmin(user)) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_LINK_ADMIN", "操作需要互联管理员权限"); // ignoreI18n
            }
        } else {
            if (!isFsAdmin(user) && !hasAppManageFunction(user, appId)) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_OR_APP_ADMIN", "操作需要平台应用管理权限"); // ignoreI18n
            }
        }
        appManager.deleteCustomApp(user, lang, appId);
        return SUCCESS;
    }

    /**
     * 获取开发模式信息.
     *
     * @param user  操作人.
     * @param appId 应用id
     * @return 开发模式信息 {@link AppDevModeInfo}
     */
    @RequestMapping("/getDevModeInfo")
    @ResponseBody
    public AjaxResult getDevModeInfo(@ModelAttribute FsUserVO user,
                                     @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n

        if (!isFsAdmin(user) && !isAppAdmin(user, appId) && !hasAppManageFunction(user, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_OR_APP_ADMIN", "操作需要应用管理权限"); // ignoreI18n
        }

        AppDevModeInfo appDevModeInfo = appManager.getAppDevModeInfo(user, appId);
        return new AjaxResult(appDevModeInfo);
    }


    /**
     * 启动开发模式或修改开发模式信息.
     *
     * @param user           操作者.
     * @param appDevModeInfo 开发模式信息
     * @param serviceId      新关联的服务号
     * @return 是否启动成功
     */
    @RequestMapping("/enableDevMode")
    @ResponseBody
    public AjaxResult enableDevMode(@ModelAttribute FsUserVO user, @ModelAttribute String lang, AppDevModeInfo appDevModeInfo, String serviceId) {
        checkParamNotBlank(appDevModeInfo, "请填写表单"); // ignoreI18n
        try {
            appDevModeInfo.checkParamsForEnable();
        } catch (IllegalArgumentException e) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, e.getMessage());
        }

        if (Objects.equals(appDevModeInfo.getIsStop(), 1)) {
            checkParamNotBlank(appDevModeInfo.getIpWhites(), "IP白名单不能为空"); // ignoreI18n
        }

        AppResult appResult = openAppService.loadOpenAppFast(appDevModeInfo.getAppId());
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在."); // ignoreI18n
        }

        if (!isFsAdmin(user) && !isAppAdmin(user, appDevModeInfo.getAppId()) && !hasAppManageFunction(user, appDevModeInfo.getAppId())) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_OR_APP_ADMIN", "操作需要系统管理员或者应用管理员权限"); // ignoreI18n
        }

        // 检查开发者模式开启状态
        try {
            if (null != appResult.getResult()) {
                // 开启开发者模式写MQ
                Integer appModeState = appResult.getResult().getAppMode();
                if (CommonConstant.DEVELOPER_MODE_ON == appModeState) {
                    // 普通模式下则为开启开发者模式
                    CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appDevModeInfo.getAppId(),
                            user.getUserId(), System.currentTimeMillis() + "", "开启了开发者模式", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                } else if (CommonConstant.DEVELOPER_MODE_UPDATE == appModeState) {
                    // 回调模式下则为修改开发者模式信息
                    AppDevModeInfo oldAppDevModeInfo = appManager.getAppDevModeInfo(user, appDevModeInfo.getAppId());
                    if (null != oldAppDevModeInfo) {
                        // 新提交的表单信息
                        String callBackMsgUrl = appDevModeInfo.getCallBackMsgUrl();
                        String callBackDomain = appDevModeInfo.getCallBackDomain();
                        String ipWhites = appDevModeInfo.getIpWhites();

                        if (null != callBackMsgUrl && !callBackMsgUrl.equals(oldAppDevModeInfo.getCallBackMsgUrl())) {
                            // 修改了消息与事件接收URL xxx
                            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appDevModeInfo.getAppId(),
                                    user.getUserId(), System.currentTimeMillis() + "", "修改了消息与事件接收URL为\"" + callBackMsgUrl + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                        }
                        if (null != callBackDomain && !callBackDomain.equals(oldAppDevModeInfo.getCallBackDomain())) {
                            // 登录授权发起页域名 xxx
                            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appDevModeInfo.getAppId(),
                                    user.getUserId(), System.currentTimeMillis() + "", "修改了登录授权发起页域名为\"" + callBackMsgUrl + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                        }
                        if (null != ipWhites && !ipWhites.equals(oldAppDevModeInfo.getIpWhites())) {
                            // 白名单IP列表
                            CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appDevModeInfo.getAppId(),
                                    user.getUserId(), System.currentTimeMillis() + "", "修改了白名单IP列表", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("enableDevMode send MQ failed, user[{}],appDevModeInfo[{}]", user, appDevModeInfo, e);
        }

        final String code = appManager.enableDevMode(user, appDevModeInfo);
        OpenAppDO appDO = appResult.getResult();
        if (appDO.getAppType() == AppCenterEnum.AppType.CUSTOM_APP.value()) {
            GetServiceRefAppResult getServiceRefAppResult = appServiceRefService
                    .getServiceRefAppList(appDevModeInfo.getAppId(), null);
            if (!getServiceRefAppResult.isSuccess()) {
                logger.warn("failed to get ref services in enableDevMode, appId[{}], serviceId[{}], user[{}]",
                        appDevModeInfo.getAppId(), serviceId, user);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, getServiceRefAppResult, "查询关联服务号失败."); // ignoreI18n
            }
            List<AppServiceRefDO> appServiceRefDOs = getServiceRefAppResult.getAppServiceRefDOs();
            if (!CollectionUtils.isEmpty(appServiceRefDOs)) {
                if (StringUtils.isBlank(serviceId)) {
                    appServiceRefDOs.stream().filter(t -> StringUtils.isNotBlank(t.getServiceId()))
                            .forEach(t -> {
                                //如果遍历过程中某个服务号的关联关系删除失败，会出现只删除一部分关联关系的情况。
                                CommonResult commonResult = appServiceRefService.deleteServiceRefApp(t.getServiceId(),
                                        appDevModeInfo.getAppId());
                                if (!commonResult.isSuccess()) {
                                    logger.warn("failed to deleteServiceRefApp, serviceId[{}], appId[{}]",
                                            t.getServiceId(), appDevModeInfo.getAppId());
                                    throw new BizException(AjaxCode.BIZ_EXCEPTION, getServiceRefAppResult,
                                            "删除关联服务号失败."); // ignoreI18n
                                }
                            });
                } else {
                    if (appServiceRefDOs.stream().noneMatch(a -> serviceId.equals(a.getServiceId()))) {
                        appServiceRefDOs.stream().filter(t -> StringUtils.isNotBlank(t.getServiceId()))
                                .forEach(t -> {
                                    CommonResult commonResult = appServiceRefService.deleteServiceRefApp(t.getServiceId()
                                            , appDevModeInfo.getAppId());
                                    if (!commonResult.isSuccess()) {
                                        logger.warn("failed to deleteServiceRefApp, serviceId[{}], appId[{}]",
                                                t.getServiceId(), appDevModeInfo.getAppId());
                                        throw new BizException(AjaxCode.BIZ_EXCEPTION, getServiceRefAppResult,
                                                "删除关联服务号失败."); // ignoreI18n
                                    }

                                });
                        AppServiceRefDO newDO = new AppServiceRefDO();
                        newDO.setAppId(appDevModeInfo.getAppId());
                        newDO.setServiceId(serviceId);
                        newDO.setGmtCreate(new Date());
                        newDO.setGmtModified(new Date());
                        CommonResult commonResult = appServiceRefService.batchSaveServiceRefApps(Arrays.asList(newDO));
                        if (!commonResult.isSuccess()) {
                            logger.warn("failed to batchSaveServiceRefApps, AppServiceRefDO[{}]", newDO);
                            throw new BizException(AjaxCode.BIZ_EXCEPTION, getServiceRefAppResult,
                                    "保存应用关联服务号失败."); // ignoreI18n
                        }
                    }
                }
            } else if (StringUtils.isNotBlank(serviceId)) {
                AppServiceRefDO newDO = new AppServiceRefDO();
                newDO.setAppId(appDevModeInfo.getAppId());
                newDO.setServiceId(serviceId);
                newDO.setGmtCreate(new Date());
                newDO.setGmtModified(new Date());
                appServiceRefService.batchSaveServiceRefApps(Arrays.asList(newDO));
            }
        }
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.putAll(JsonUtils.parseToMap(appDevModeInfo));
            map.put("appId", appDevModeInfo.getAppId());
            map.put("action", BuriedPointBizEnum.CUSTOM_APP_ENABLE_DEV_MODE.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CUSTOM_APP_ENABLE_DEV_MODE, map, e);
        }
        return new AjaxResult(code);
    }

    /**
     * 关闭开发模式.
     *
     * @param user       操作人.
     * @param createForm 应用id
     * @return 是否关闭成功
     */
    @RequestMapping("/disableDevMode")
    @ResponseBody
    public AjaxResult disableDevMode(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                     @RequestBody AppCreateForm createForm) {
        checkParamNotBlank(createForm, "请填写表单"); // ignoreI18n
        final String appId = createForm.getAppId();
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n

        if (!isFsAdmin(user) && !isAppAdmin(user, appId) && !hasAppManageFunction(user, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_OR_APP_ADMIN", "操作需要应用管理权限"); // ignoreI18n
        }

        AppResult appResult = openAppService.loadOpenAppFast(createForm.getAppId());
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在."); // ignoreI18n
        }
        appManager.disableDevMode(appId);
        // 关闭开发者模式
        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), createForm.getAppId(),
                user.getUserId(), System.currentTimeMillis() + "", "停用了开发者模式", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("appId", appId);
            map.putAll(JsonUtils.parseToMap(createForm));
            map.put("action", BuriedPointBizEnum.CUSTOM_APP_DISABLE_DEV_MODE.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CUSTOM_APP_DISABLE_DEV_MODE, map, e);
        }
        return SUCCESS;
    }

    /**
     * 获取应用的登录地址.
     *
     * @param user        操作人.
     * @param componentId 组件id.
     * @return 跳转地址.
     */
    @RequestMapping("/getComponentLoginUrl")
    @ResponseBody
    public AjaxResult getComponentLoginUrl(@ModelAttribute FsUserVO user,
                                           @RequestParam(value = "componentId", required = false) String componentId) {
        checkParamNotBlank(componentId, "请选择组件"); // ignoreI18n
        AjaxResult result;
        //todo xialf: 验证componentId是否存在, 以防前端使用appId
        CallBackBindDO args = new CallBackBindDO();
        args.setAppOrComponentId(componentId);
        args.setFsUserId(user.asStringUser());
        args.setAppType(AppViewTypeEnum.WEB);
        BaseResult bindResult = appCallBackService.getAppCallBackBindParam(args);
        if (bindResult.isSuccess()) {
            result = new AjaxResult(bindResult.getResult());
            //写统计日志
            appStatLog(user, componentId);
        } else {
            logger.warn("get app login callback address fail,user [" + user + "] appId [ " + componentId
                    + " ] result [" + bindResult + "]");
            result = new AjaxResult(AjaxCode.BIZ_EXCEPTION, bindResult.getErrMessage());
        }
        return result;
    }

    /**
     * 重定向到应用的登录地址. （用于UIPAAS应用列表跳转）
     *
     * @param user        操作人.
     * @param componentId 组件id.
     * @return 跳转地址.
     */
    @RequestMapping("/redirectComponentLoginUrl")
    public void redirectComponentLoginUrl(@ModelAttribute FsUserVO user,
                                          @RequestParam(value = "componentId", required = false) String componentId,
                                          HttpServletResponse response) throws IOException {

        checkParamNotBlank(componentId, "请选择组件"); // ignoreI18n
        AjaxResult result;
        CallBackBindDO args = new CallBackBindDO();
        args.setAppOrComponentId(componentId);
        args.setFsUserId(user.asStringUser());
        args.setAppType(AppViewTypeEnum.WEB);
        BaseResult bindResult = appCallBackService.getAppCallBackBindParam(args);
        if (bindResult.isSuccess()) {
            result = new AjaxResult(bindResult.getResult());
            //写统计日志
            appStatLog(user, componentId);
        } else {
            logger.warn("get app login callback address fail,user [" + user + "] appId [ " + componentId
                    + " ] result [" + bindResult + "]");
            throw new BizException(AjaxCode.BIZ_EXCEPTION, bindResult.getErrMessage());
        }

        String redirectUrl = (String) bindResult.getResult();
        response.sendRedirect(redirectUrl);
    }

    private void appStatLog(FsUserVO user, String componentId) {
        Map<String, Object> stringObjectMap = appManager.loadAppDtlByComponentId(user, componentId);
        String appId = (String) stringObjectMap.get("appId");
        Integer appType = (Integer) stringObjectMap.get("appType");
        AppStatLogKit.log(appId, componentId, MonitorTypeEnum.STATISTICS_WEB_USE_ACCOUNT,
                user.getEnterpriseAccount(), user.getUserId(), appType);
    }

    /**
     * 获取组件所属应用的id.
     *
     * @param user        用户信息
     * @param componentId 组件id
     * @return 所属应用的id
     */
    @RequestMapping("/getAppId")
    @ResponseBody
    public AjaxResult getAppId(@ModelAttribute FsUserVO user,
                               @RequestParam(value = "componentId", required = false) String componentId) {
        checkParamNotBlank(componentId, "请选择组件"); // ignoreI18n
        return new AjaxResult(appManager.getAppIdByComponentId(user, componentId));
    }

//    private String getAppIdByComponentId(final FsUserVO user, final String componentId) {
//        appManager.loadAppDtlByComponentId(user,componentId);
//        BaseResult<OpenAppComponentDO> componentDoResult = openAppComponentService.loadOpenAppComponentById(user,
//                componentId);
//        if (!componentDoResult.isSuccess()) {
//            logger.warn("failed to call openAppComponentService.loadOpenAppComponentById: user={}, componentId={}, result={}",
//                    user, componentId, componentDoResult);
//            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询组件失败");
//        }
//        return componentDoResult.getResult().getAppId();
//    }

    /**
     * 是否可以创建自定义应用
     *
     * @param user 用户.
     * @return AjaxResult.
     */
    @RequestMapping("/isAbleToCreateCustom")
    @ResponseBody
    @Deprecated
    public AjaxResult isAbleToCreateCustom(@ModelAttribute FsUserVO user) {
        checkFsAdmin(user);//验证系统管理员
        appManager.isAbleToCreateCustom(user);
        return SUCCESS;
    }

    /**
     * 查询开放平台的应用,用于应用中心主页平台应用的展示.add by lambo @20160120
     *
     * @param user        用户.
     * @param currentPage 当前页
     * @param pageSize    页大小.默认为10.
     * @return AjaxResult.
     */
    @RequestMapping("/queryPlatFormAppList")
    @ResponseBody
    public AppPagerAjaxResult queryPlatFormAppList(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                                   @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Pager<Map<String, Object>> appDoPager = appManager.queryPlatFormAppList(user, currentPage, pageSize, lang);
        return new AppPagerAjaxResult(appDoPager);
    }

    /**
     * 查询企业自定义应用列表,用于应用中心主页企业自建的展示.add by lambo @20160120
     *
     * @param user 用户.
     * @return AjaxResult.
     */
    @RequestMapping("/queryCustomAppList")
    @ResponseBody
    public AjaxResult queryCustomAppList(@ModelAttribute FsUserVO user, @ModelAttribute String lang) {
        List<Map<String, Object>> customAppList = appManager.queryCustomAppList(user, lang, isFsAdmin(user), isAppAdmin(user));
        Map<String, Object> data = new HashMap<>();
        data.put("customAppList", customAppList);
        data.put("isFsAdmin", isFsAdmin(user) ? CommonConstant.YES : CommonConstant.NO);
        return new AjaxResult(data);
    }

    /**
     * 应用试用
     * 根据不同的身份和应用, 试用规则来决定是否允许试用.
     *
     * @param user 用户
     * @return 试用结果和身份态
     */
    @RequestMapping("/try")
    @ResponseBody
    public AjaxResult applyTrial(@ModelAttribute FsUserVO user,
                                 @RequestBody AppCreateForm appForm) {
        //todo by xialf, 检验参数

        //todo 运用试用规则判断是否可以试用 by xialf

        //todo 返回试用结果(包含身份态)
        return null;
    }

    /**
     * 查询APP状态  1启用/2停用
     *
     * @param appId
     * @param user
     * @return
     */
    @RequestMapping("/queryOnOffStatus")
    @ResponseBody
    public AjaxResult queryOnOffStatus(@RequestParam(value = "appId") String appId, @ModelAttribute FsUserVO user) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        if (!isFsAdmin(user) && !isAppAdmin(user, appId)) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "非企业管理员或应用管理员"); // ignoreI18n
        }
        AppOnOffEnum appOnOffEnum = appManager.getAppOnOffStatus(appId, user.getEnterpriseAccount());
        if (appOnOffEnum.getValue().equals(AppOnOffEnum.OFF.getValue())) {
            AjaxResult onOffResult = new AjaxResult(AjaxCode.BIZ_EXCEPTION, ConfigCenter.APP_OFF_DIALOG_BOX);
            onOffResult.setData(appOnOffEnum.getValue());
            return onOffResult;
        }
        return new AjaxResult(appOnOffEnum.getValue());
    }

    /**
     * 查询组件可见范围人数
     *
     * @param appId
     * @param user
     * @return
     */
    @RequestMapping("/queryComponentUsersCount")
    @ResponseBody
    public AjaxResult queryComponentUsersCount(@RequestParam(value = "appId", required = false) String appId, @RequestParam
            (value = "componentId", required = false) String componentId, @ModelAttribute FsUserVO user) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkParamNotBlank(componentId, "请选择组件"); // ignoreI18n
        if (!isFsAdmin(user) && !isAppAdmin(user, appId)) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "非企业管理员或应用管理员"); // ignoreI18n
        }
        Map<String, Integer> dataMap = appManager.queryComponentUsersCount(user, componentId);
        return new AjaxResult(dataMap);
    }

    /**
     * 第三方跳转地址追加code
     *
     * @param appId
     * @param user
     * @return
     */
    @RequestMapping("/thirdpartyUrlAppendCode")
    @ResponseBody
    @Deprecated
    public AjaxResult thirdpartyUrlAppendCode(@RequestParam(value = "appId", required = false) String appId, @RequestParam
            (value = "thirdpartyUrl", required = false) String thirdpartyUrl, @ModelAttribute FsUserVO user) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkParamNotBlank(thirdpartyUrl, "第三方地址不能为空"); // ignoreI18n
        CallBackBindDO args = new CallBackBindDO();
        args.setAppOrComponentId(appId);
        args.setAppType(AppViewTypeEnum.WEB);
        args.setMessageJumpUrl(thirdpartyUrl);
        BaseResult bindResult = appCallBackService.getAppCallBackBindParam(args);
        if (!bindResult.isSuccess()) {
            logger.warn("fail to callBackBizService.getCallBackBindParam, args={}, result={}",
                    args, bindResult);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, bindResult.getErrDescription());
        }
        return new AjaxResult(bindResult.getResult());
    }


    /**
     * 第三方跳转地址追加code  推荐使用POST方式提交请求
     *
     * @param thirdpartyUrlForm
     * @param user
     * @return
     */
    @RequestMapping("/thirdpartyUrlAppendCode4Post")
    @ResponseBody
    public AjaxResult thirdpartyUrlAppendCode4Post(@ModelAttribute FsUserVO user, @RequestBody ThirdpartyUrlForm
            thirdpartyUrlForm) {
        checkParamNotBlank(thirdpartyUrlForm, "表单不能为空"); // ignoreI18n
        checkParamNotBlank(thirdpartyUrlForm.getAppId(), "应用不能为空"); // ignoreI18n
        checkParamNotBlank(thirdpartyUrlForm.getThirdpartyUrl(), "第三方地址不能为空"); // ignoreI18n
        CallBackBindDO args = new CallBackBindDO();
        args.setAppOrComponentId(thirdpartyUrlForm.getAppId());
        args.setFsUserId(user.asStringUser());
        args.setAppType(AppViewTypeEnum.WEB);
        args.setMessageJumpUrl(thirdpartyUrlForm.getThirdpartyUrl());
        BaseResult bindResult = appCallBackService.getAppCallBackBindParam(args);
        if (!bindResult.isSuccess()) {
            logger.warn("fail to callBackBizService.getCallBackBindParam, args={}, result={}",
                    args, bindResult);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, bindResult.getErrDescription());
        }
        return new AjaxResult(bindResult.getResult());
    }

    /**
     * 获取应用状态(启停, 是否到期).
     */
    @RequestMapping("/queryAppStatus")
    @ResponseBody
    public AjaxResult queryAppStatus(@RequestParam(value = "appId") String appId, @ModelAttribute FsUserVO user) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkParamNotBlank(user, "用户不能为空"); // ignoreI18n
        boolean isFsAdmin = isFsAdmin(user);
        boolean isAppAdmin = isAppAdmin(user, appId);
        if (!isFsAdmin && !isAppAdmin) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "不是企业管理员也不是应用管理员"); // ignoreI18n
        }

        Map<String, Object> resultMap = new HashMap<>();
        final PayStatus payStatus = appManager.getPayStatus(appId, user.getEnterpriseAccount());
        resultMap.put("payStatus", payStatus.getCode());
        if (payStatus == PayStatus.PURCHASE_EXPIRED || payStatus == PayStatus.TRIAL_EXPIRED) {
            final String msg = isFsAdmin ? ConfigCenter.APP_EXPIRED_ADMIN_MSG : ConfigCenter.getAppExpiredAppAdminMsg();
            AjaxResult result = new AjaxResult(AjaxCode.BIZ_EXCEPTION, msg);
            result.setData(resultMap);
            return result;
        }

        AppOnOffEnum appOnOffEnum = appManager.getAppOnOffStatus(appId, user.getEnterpriseAccount());
        resultMap.put("onOff", appOnOffEnum.getValue());
        if (appOnOffEnum.getValue().equals(AppOnOffEnum.OFF.getValue())) {
            AjaxResult onOffResult = new AjaxResult(AjaxCode.BIZ_EXCEPTION, ConfigCenter.APP_OFF_DIALOG_BOX);
            onOffResult.setData(resultMap);
            return onOffResult;
        }
        return new AjaxResult(resultMap);
    }
}
