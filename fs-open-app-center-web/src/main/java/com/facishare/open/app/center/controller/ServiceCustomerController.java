package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.enums.OpenCustomerRoleEnum;
import com.facishare.open.app.center.api.model.vo.OpenCustomerVO;
import com.facishare.open.app.center.api.service.outer.OpenCustomerService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.OuterServiceManager;
import com.facishare.open.app.center.manager.ServiceCustomerManager;
import com.facishare.open.app.center.model.OpenCustomerForm;
import com.facishare.open.app.center.model.outers.args.OpenCustomerQueryPagerArgs;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.proxy.constants.PermissionType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 开平客服：用于外联服务号的微信客服
 * Created by chenzs on 2016/11/2.
 */
@Controller
@RequestMapping("/open/appcenter/service/customer")
public class ServiceCustomerController extends BaseController {
    @Autowired
    private ServiceCustomerManager serviceCustomerManager;

    @Autowired
    private OuterServiceManager outerServiceManager;

    @Autowired
    private OpenCustomerService openCustomerService;

    /**
     * 拉取客服人员列表
     *
     * @param fsUserVO
     * @param args     其中 role=-1, 则获取所有的角色
     * @return .
     */
    @RequestMapping("/queryPager")
    @ResponseBody
    public AjaxResult queryPager(@ModelAttribute FsUserVO fsUserVO,
                                 @RequestBody OpenCustomerQueryPagerArgs args) {
        //参数
        if (args.getRole() == null) {  //没传role, args.getRole() = null
            return new AjaxResult(AjaxCode.PARAM_ERROR, "您选择的角色有误，请重新选择"); // ignoreI18n
        } else if (args.getRole() != -1
                && args.getRole() != OpenCustomerRoleEnum.COMMON.getCode()
                && args.getRole() != OpenCustomerRoleEnum.EXCLUSIVE.getCode()
                && args.getRole() != OpenCustomerRoleEnum.MANAGER.getCode()) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "您选择的角色有误，请重新选择"); // ignoreI18n
        }
        checkParamNotBlank(args.getAppId(), "appId不能为空"); // ignoreI18n
        //权限
        checkAppAdmin(fsUserVO, args.getAppId());
        checkServiceIsOn(fsUserVO, args.getAppId());
        checkPermissionType(fsUserVO, args.getAppId());

        if (args.getRole() == -1) {  //查询所有角色
            args.setRole(null);
        }
        Pager<OpenCustomerVO> openCustomerVOPager = serviceCustomerManager.queryPager(fsUserVO, args);
        return new AjaxResult(openCustomerVOPager);
    }

    /**
     * 手工添加客服人员
     *
     * @param fsUserVO
     * @param openCustomerForm
     * @return
     */
    @RequestMapping(value = "/addOpenCustomer", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult addOpenCustomer(@ModelAttribute FsUserVO fsUserVO,
                                      @RequestBody OpenCustomerForm openCustomerForm) {
        //参数
        checkParamNotBlank(openCustomerForm, "请填写表单."); // ignoreI18n
        checkParamNotBlank(openCustomerForm.getUserIds(), "请选择需要添加的客服人员"); // ignoreI18n
        checkParamNotBlank(openCustomerForm.getAppId(), "appId不能为空"); // ignoreI18n
        //权限
        checkAppAdmin(fsUserVO, openCustomerForm.getAppId());
        checkServiceIsOn(fsUserVO, openCustomerForm.getAppId());
        checkPermissionType(fsUserVO, openCustomerForm.getAppId());

        Map<String, Object> result = serviceCustomerManager.addOpenCustomer(fsUserVO, openCustomerForm);
        return new AjaxResult(result);
    }

    /**
     * 修改客服人员角色
     *
     * @param fsUserVO
     * @param openCustomerVO
     * @return
     */
    @RequestMapping(value = "/updateOpenCustomerRole", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult updateOpenCustomerRole(@ModelAttribute FsUserVO fsUserVO,
                                             @RequestBody OpenCustomerVO openCustomerVO) {
        //参数
        checkParamNotBlank(openCustomerVO, "请填写客服信息."); // ignoreI18n
        checkParamNotBlank(openCustomerVO.getCustomerId(), "请选择需要修改的客服人员"); // ignoreI18n
        checkParamNotBlank(openCustomerVO.getRole(), "请选择需要修改的角色"); // ignoreI18n
        if (OpenCustomerRoleEnum.getByCode(openCustomerVO.getRole()) == null) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "您选择的角色有误，请重新选择"); // ignoreI18n
        }
        //权限
        checkAppAdmin(fsUserVO, openCustomerVO.getAppId());
        checkServiceIsOn(fsUserVO, openCustomerVO.getAppId());
        checkPermissionType(fsUserVO, openCustomerVO.getAppId());

        serviceCustomerManager.updateOpenCustomerRole(fsUserVO, openCustomerVO);
        return SUCCESS;
    }

    /**
     * 删除客服人员
     *
     * @param fsUserVO
     * @param openCustomerVO
     * @return
     */
    @RequestMapping(value = "/deleteOpenCustomer", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult deleteOpenCustomer(@ModelAttribute FsUserVO fsUserVO,
                                         @RequestBody OpenCustomerVO openCustomerVO) {
        //参数
        checkParamNotBlank(openCustomerVO, "请填写客服信息."); // ignoreI18n
        checkParamNotBlank(openCustomerVO.getCustomerId(), "请选择需要删除的客服人员"); // ignoreI18n
        //权限
        checkAppAdmin(fsUserVO, openCustomerVO.getAppId());
        checkServiceIsOn(fsUserVO, openCustomerVO.getAppId());
        checkPermissionType(fsUserVO, openCustomerVO.getAppId());

        //是否允许删除
        serviceCustomerManager.checkAllowToDelete(fsUserVO, openCustomerVO);
        //删除客服人员
        serviceCustomerManager.deleteOpenCustomer(fsUserVO, openCustomerVO.getCustomerId());
        return SUCCESS;
    }

    /**
     * 客服人员生成二维码图片
     * 用于：手机端H5调用
     *
     * @param fsUserVO
     * @param appId
     * @return
     */
    @RequestMapping(value = "/viewQRCode", method = RequestMethod.GET)
    @ResponseBody
    public AjaxResult viewQRCode(@ModelAttribute FsUserVO fsUserVO,
                                 @RequestParam(value = "appId", required = false) String appId) {
        //参数
        checkParamNotBlank(appId, "请选择外联服务号."); // ignoreI18n
        //权限
        checkPermissionType(fsUserVO, appId);
        checkServiceIsOn(fsUserVO, appId);

        Map<String, Object> result = serviceCustomerManager.viewQRCode(fsUserVO, appId);
        return new AjaxResult(result);
    }

    /**
     * 查询公司 fsEa的某个外联服务号appId的所有有效的客服人员的userId
     * 用于：外部联系人里面，"选择服务专员"的下拉框
     *
     * @param fsUserVO
     * @param appId
     * @return
     */
    @RequestMapping(value = "/queryCustomersUserIds", method = RequestMethod.GET)
    @ResponseBody
    public AjaxResult queryCustomersUserIds(@ModelAttribute FsUserVO fsUserVO,
                                            @RequestParam(value = "appId", required = false) String appId) {
        //参数
        checkParamNotBlank(appId, "请选择外联服务号."); // ignoreI18n
        //权限
        checkAppAdmin(fsUserVO, appId);
        checkServiceIsOn(fsUserVO, appId);
        checkPermissionType(fsUserVO, appId);

        List<Integer> customersUserIds = serviceCustomerManager.queryCustomersUserIds(fsUserVO, appId);
        Map<String, Object> result = new HashMap<>();
        result.put("customersUserIds", customersUserIds);
        return new AjaxResult(result);
    }

    /**
     * 判断权限:微信客服需要4种权限
     *
     * @param fsUserVO
     * @param appId
     */
    private void checkPermissionType(FsUserVO fsUserVO, String appId) {
        List<Integer> permissionTypes = Lists.newArrayList(
                PermissionType.PERMISSION_MESSAGE_MANAGE,
                PermissionType.PERMISSION_ACCOUNT_SERVICE,
                PermissionType.PERMISSION_MUTI_KEFU,
                PermissionType.PERMISSION_QR_CODE);
       outerServiceManager.checkWechat(fsUserVO, appId, null, permissionTypes);
    }
}
