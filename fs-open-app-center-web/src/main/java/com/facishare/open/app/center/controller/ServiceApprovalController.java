package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.ServiceApprovalRoleEnum;
import com.facishare.open.app.center.api.model.vo.ServiceApprovalVO;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.ServiceApprovalManager;
import com.facishare.open.app.center.model.CustomAppCreateForm;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @describe: 服务号申请审批
 * @author: xiaoweiwei
 * @date: 2016/10/18 10:07
 */
@Controller
@RequestMapping("/open/appcenter/service/approval")
public class ServiceApprovalController extends BaseController {
    @Resource
    private ServiceApprovalManager serviceApprovalManager;

    @Resource
    private AppManager appManager;

    /**
     * 服务号创建审批
     * @param fsUserVO
     * @param form
     * @return
     */
    @RequestMapping("/createApproval")
    @ResponseBody
    public AjaxResult createApproval(@ModelAttribute FsUserVO fsUserVO, @RequestBody CustomAppCreateForm form) {
        checkParamNotBlank(form, "请填写表单."); // ignoreI18n
        checkParamRegex(form.getAppName(), ConfigCenter.SERVICE_NAME_CHECK_REGEX, "请填写有效的服务号名称."); // ignoreI18n
        checkParamRegex(form.getAppDesc(), "^[\\s\\S]{1,300}$", "请填写有效的功能介绍."); // ignoreI18n
        checkParamNotBlank(form.getAppLogo(), "服务号logo不能为空."); // ignoreI18n
        checkParamNotBlank(form.getApplyReasons(), "申请理由不能为空"); // ignoreI18n
        checkParamTrue(form.getApplyReasons().length() <= 150, "申请理由不能超过150字"); // ignoreI18n
        checkParamTrue(null != form.getAppAdmins() && form.getAppAdmins().length > 0, "请选择管理员."); // ignoreI18n
        for (Integer admin : form.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员不合法"); // ignoreI18n
            }
        }
        checkParamNotBlank(form.getTemplateId(), "请选择创建模板."); // ignoreI18n
        checkParamTrue(null != form.getCopies() && form.getCopies().length > 0, "请选择抄送范围."); // ignoreI18n
        // 验证是否有重名服务号
        if(appManager.existsAppName(form.getAppName(), fsUserVO.getEnterpriseAccount(), AppCenterEnum.AppType.SERVICE) ||
                serviceApprovalManager.existsName(fsUserVO.getEnterpriseAccount(), form.getAppName())) {
            throw new BizException(AjaxCode.PARAM_ERROR, "服务号名称已经存在"); // ignoreI18n
        }
        if (isFsAdmin(fsUserVO)){
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "企业管理员可直接创建服务号，不需要申请"); // ignoreI18n
        }
        form.setWebLoginUrl(null);
        form.setAppLoginUrl(null);
        Map<String,Object> result = serviceApprovalManager.createApprovalByForm(fsUserVO, form);
        return new AjaxResult(result);
    }


    /**
     * 查询申请
     * @param fsUserVO
     * @param approvalId
     * @return
     */
    @RequestMapping("/queryApproval")
    @ResponseBody
    public AjaxResult queryApproval(@ModelAttribute FsUserVO fsUserVO,
                                      @RequestParam(value = "approvalId", required = false) String approvalId) {
        checkParamNotBlank(approvalId, "请选择审批"); // ignoreI18n
        ServiceApprovalVO serviceApproval = serviceApprovalManager.queryApproval(fsUserVO, approvalId);
        //申请的角色
        Integer serviceApprovalRole = ServiceApprovalRoleEnum.OTHER.getCode();
        if (!CollectionUtils.isEmpty(serviceApproval.getCopies()) && serviceApproval.getCopies().contains(fsUserVO
                .getUserId())){
            serviceApprovalRole = ServiceApprovalRoleEnum.COPY.getCode();
        }
        if (isFsAdmin(fsUserVO)){
            serviceApprovalRole = ServiceApprovalRoleEnum.APPROVAL.getCode();
        }
        if(serviceApproval.getCreatorUserId().equals(fsUserVO.getUserId())){
            serviceApprovalRole = ServiceApprovalRoleEnum.CREATOR.getCode();
        }

        if (serviceApprovalRole.equals(ServiceApprovalRoleEnum.OTHER.getCode())){
            throw new BizException(AjaxCode.NO_AUTHORITY, "无权限查看"); // ignoreI18n
        }
        //判断是否已经重新提交过
        Boolean hasResubmitted = serviceApprovalManager.hasResubmitted(approvalId);
        Map<String,Object> result = new HashMap<>();
        result.put("serviceApproval", serviceApproval);
        result.put("serviceApprovalRole", serviceApprovalRole);
        result.put("hasResubmitted", hasResubmitted);
        return new AjaxResult(result);
    }

    /**
     * 审批通过
     * @param fsUserVO
     * @param approvalId
     * @return
     */
    @RequestMapping("/throughApproval")
    @ResponseBody
    public AjaxResult throughApproval(@ModelAttribute FsUserVO fsUserVO, @ModelAttribute String lang,
                                     @RequestParam(value = "approvalId", required = false) String approvalId,
                                      @RequestParam(value = "approvalMsg", required = false) String approvalMsg) {
        checkParamNotBlank(approvalId, "请选择审批"); // ignoreI18n
        checkFsAdmin(fsUserVO);
        serviceApprovalManager.throughApproval(fsUserVO, lang, approvalId, approvalMsg);
        ServiceApprovalVO serviceApproval = serviceApprovalManager.queryApproval(fsUserVO, approvalId);
        Map<String,Object> result = new HashMap<>();
        result.put("serviceApproval", serviceApproval);
        return new AjaxResult(result);
    }

    /**
     * 审批拒绝
     * @param fsUserVO
     * @param approvalId
     * @return
     */
    @RequestMapping("/unThroughApproval")
    @ResponseBody
    public AjaxResult unThroughApproval(@ModelAttribute FsUserVO fsUserVO,
                                      @RequestParam(value = "approvalId", required = false) String approvalId,
                                      @RequestParam(value = "approvalMsg", required = false) String approvalMsg) {
        checkParamNotBlank(approvalId, "请选择审批"); // ignoreI18n
        checkParamNotBlank(approvalMsg, "请填写审批消息"); // ignoreI18n
        checkFsAdmin(fsUserVO);
        serviceApprovalManager.unThroughUnApproval(fsUserVO, approvalId, approvalMsg);
        ServiceApprovalVO serviceApproval = serviceApprovalManager.queryApproval(fsUserVO, approvalId);
        Map<String,Object> result = new HashMap<>();
        result.put("serviceApproval", serviceApproval);
        return new AjaxResult(result);
    }

    /**
     * 服务号创建审批
     * @param user
     * @param form
     * @return
     */
    @RequestMapping("/resubmitApproval")
    @ResponseBody
    public AjaxResult resubmitApproval (@ModelAttribute FsUserVO user, @RequestBody CustomAppCreateForm form) {
        checkParamNotBlank(form, "请填写表单."); // ignoreI18n
        checkParamRegex(form.getAppName(), ConfigCenter.SERVICE_NAME_CHECK_REGEX, "请填写有效的服务号名称."); // ignoreI18n
        checkParamRegex(form.getAppDesc(), "^[\\s\\S]{1,300}$", "请填写有效的功能介绍."); // ignoreI18n
        checkParamNotBlank(form.getAppLogo(), "服务号logo不能为空."); // ignoreI18n
        checkParamTrue(null != form.getAppAdmins() && form.getAppAdmins().length > 0, "请选择管理员."); // ignoreI18n
        for (Integer admin : form.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员不合法"); // ignoreI18n
            }
        }
        checkParamNotBlank(form.getTemplateId(), "请选择创建模板."); // ignoreI18n
        checkParamNotBlank(form.getPreApprovalId(), "请选择原审批."); // ignoreI18n
        checkParamTrue(null != form.getCopies() && form.getCopies().length > 0, "请选择抄送范围."); // ignoreI18n
        // 验证是否有重名服务号
        if(appManager.existsAppName(form.getAppName(), user.getEnterpriseAccount(), AppCenterEnum.AppType.SERVICE) ||
                serviceApprovalManager.existsName(user.getEnterpriseAccount(), form.getAppName())) {
            throw new BizException(AjaxCode.PARAM_ERROR, "服务号名称已经存在"); // ignoreI18n
        }
        form.setWebLoginUrl(null);
        form.setAppLoginUrl(null);

        Boolean hasResubmitted = serviceApprovalManager.hasResubmitted(form.getPreApprovalId());
        if (hasResubmitted){
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "该申请已重新提交"); // ignoreI18n
        }

        Map<String,Object> result = serviceApprovalManager.createApprovalByForm(user, form);
        return new AjaxResult(result);
    }

}
