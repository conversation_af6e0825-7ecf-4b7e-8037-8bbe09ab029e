package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.utils.AppStatLogKit;
import com.facishare.open.app.center.common.AppCenterConstants;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.CustomMenuManager;
import com.facishare.open.app.center.model.AppMenuUrlForm;
import com.facishare.open.app.center.model.CustomMenuForm;
import com.facishare.open.app.center.model.MenuFormVO;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.enums.MonitorTypeEnum;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.custom.menu.api.enums.CustomMenuStatusEnum;
import com.facishare.open.custom.menu.api.enums.CustomMenuTypeEnum;
import com.facishare.open.custom.menu.api.model.Menu;
import com.facishare.open.custom.menu.api.model.vo.OpenMenuVO;
import com.facishare.open.custom.menu.api.result.BaseResult;
import com.facishare.open.custom.menu.api.service.CustomMenuService;
import com.facishare.open.intelligence.form.api.model.enums.status.DescribeStatusEnum;
import com.facishare.open.intelligence.form.api.model.enums.status.UsingStatusEnum;
import com.facishare.open.intelligence.form.api.model.enums.type.DescribeTypeEnum;
import com.facishare.open.intelligence.form.api.model.vo.FormDescribeVo;
import com.facishare.open.intelligence.form.api.model.vo.UserVO;
import com.facishare.open.intelligence.form.api.service.FormDescribeService;
import com.facishare.open.material.api.service.MaterialService;
import com.facishare.open.work.order.paas.api.model.enums.AccountTypeEnum;
import com.facishare.open.work.order.paas.api.model.enums.BizTypeEnum;
import com.facishare.open.work.order.paas.api.model.enums.SwitchStatusEnum;
import com.facishare.open.work.order.paas.api.model.enums.WorkOrderDescribeStatusEnum;
import com.facishare.open.work.order.paas.api.model.vo.UserVo;
import com.facishare.open.work.order.paas.api.model.vo.WorkOrderDescribeVo;
import com.facishare.open.work.order.paas.api.model.vo.WorkOrderSwitchVo;
import com.facishare.open.work.order.paas.api.service.WorkOrderDescribeService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * impl.
 * Created by zenglb on 2015/12/9.
 */
@Service
public class CustomMenuManagerImpl implements CustomMenuManager {
    private static final Logger logger = LoggerFactory.getLogger(CustomMenuManagerImpl.class);

    @Resource
    private CustomMenuService customMenuService;

    @Resource
    private MaterialService materialService;

    @Resource
    private AppManager appManager;

    @Resource
    private WorkOrderDescribeService workOrderDescribeService;

    @Resource
    private FormDescribeService formDescribeService;


    private static String TOPIC_WEB_URL_REF_PRE = "stream/showtopic/=/name-";
    private static String FEED_WEB_URL_REF_PRE = "stream/showfeed/=/id-";
    private static String TOPIC_TERMINAL_URL_PRE = "fs_special://topic?";
    private static String FEED_TERMINAL_URL_PRE = "fs_special://feed?";

    @Override
    public Integer queryCustomMenuStatusByAppId(FsUserVO user, String appId) {
        BaseResult<OpenMenuVO> customMenuStatusResult = customMenuService.findCustomMenuByAppId(appId);
        if (!customMenuStatusResult.isSuccess()) {
            logger.warn("query customMenu status failed! appId[{}] customMenuStatus[{}]", appId, customMenuStatusResult);
            return CommonConstant.NO;
        }
        return CustomMenuStatusEnum.NORMAL == customMenuStatusResult.getResult().getStatus() ? CommonConstant.YES : CommonConstant.NO;
    }

    @Override
    public void enableCustomMenu(FsUserVO user, String appId) {
        OpenAppDO appDO = appManager.checkAppTypeAndOwner(user, appId);
        BaseResult<Void> baseResult = customMenuService.modifyStatus(appId, user.getEnterpriseAccount(), CustomMenuStatusEnum.NORMAL);
        if (!baseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "开启自定义菜单失败"); // ignoreI18n
        }
        //添加自定义菜单开启日志
        AppStatLogKit.log(appId, "", MonitorTypeEnum.STATISTICS_CUSTOM_MENU_OPEN_ACCOUNT, user.getEnterpriseAccount(), user.getUserId(),appDO.getAppType());
    }

    @Override
    public void disableCustomMenu(FsUserVO user, String appId) {
        OpenAppDO appDO = appManager.checkAppTypeAndOwner(user, appId);
        BaseResult<Void> booleanBaseResult = customMenuService.modifyStatus(appId, user.getEnterpriseAccount(), CustomMenuStatusEnum.CLOSE);
        if (!booleanBaseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, booleanBaseResult, "关闭自定义菜单失败"); // ignoreI18n
        }
        //添加自定义菜单开启日志
        AppStatLogKit.log(appId, "", MonitorTypeEnum.STATISTICS_CUSTOM_MENU_CLOSE_ACCOUNT, user.getEnterpriseAccount(), user.getUserId(),appDO.getAppType());
    }

    @Override
    public void deleteCustomMenu(FsUserVO user, String appId) {
        BaseResult<Void> booleanBaseResult = customMenuService.modifyStatus(appId, user.getEnterpriseAccount(),  CustomMenuStatusEnum.DELETED);
        if (!booleanBaseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, booleanBaseResult, "删除自定义菜单失败"); // ignoreI18n
        }
    }

    @Override
    public List<MenuFormVO> loadCustomMenuListByAppId(FsUserVO user, String appId) {
        appManager.checkAppTypeAndOwner(user, appId);
        BaseResult<OpenMenuVO> customMenuByAppIdResult = customMenuService.findCustomMenuByAppId(appId);
        if (!customMenuByAppIdResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, customMenuByAppIdResult, "加载自定义菜单失败"); // ignoreI18n
        }
        List<MenuFormVO> result = new ArrayList<>();
        List<Menu> menus = customMenuByAppIdResult.getResult().getMenus();

        if (!CollectionUtils.isEmpty(menus)) {
            for (int i = 0; i < menus.size(); i++) {
                Menu menu = menus.get(i);
                MenuFormVO vo = menu2VO(menu, i + "");
                result.add(vo);
            }
        }
        return result;
    }

    private MenuFormVO menu2VO(Menu menu, String id) {
        MenuFormVO result = new MenuFormVO();
        result.setId(id);
        result.setName(menu.getName());
        result.setType(menu.getType());
        result.setIsOpenResponse(menu.getIsOpenResponse());
        if (CustomMenuTypeEnum.URL.getCode() == menu.getType()) {
            String actionParam = menu.getActionParam();
            String webUrl = transToWebURL(actionParam);
            if (StringUtils.isNotBlank(webUrl)) {
                result.setUrl(webUrl);
            } else {
                result.setUrl(actionParam);
            }
        } else  if (CustomMenuTypeEnum.IMAGE_TEXT.getCode() == menu.getType()) {
            result.setMaterialId(menu.getActionParam());
            if (!StringUtils.isEmpty(result.getMaterialId())){
                com.facishare.open.common.result.BaseResult<Integer> statusResult = materialService.queryStatus(result
                        .getMaterialId());
                if (statusResult.isSuccess()){
                    result.setStatus(statusResult.getResult());
                }
            }
        } else  if (CustomMenuTypeEnum.BIND_APP.getCode() == menu.getType()){
            // TODO: 2016/6/15 addby zhouq 设置绑定应用的属性
            result.setBindAppKey(AppCenterConstants.BIND_MAP.get(menu.getActionParam()));
            result.setUrl(menu.getActionParam());
        } else {
            result.setTxt(menu.getActionParam());
        }
        if (!CollectionUtils.isEmpty(menu.getChildren())) {
            result.setChildren(new ArrayList<>());
            for (int i = 0; i < menu.getChildren().size(); i++) {
                Menu m2 = menu.getChildren().get(i);
                MenuFormVO vo = menu2VO(m2, id + i);
                result.getChildren().add(vo);
            }
        }
        return result;
    }

    private String transToWebURL(String terminalJumpUrl) {
        if (StringUtils.isBlank(terminalJumpUrl)) {
            return "";
        }
        try {
            terminalJumpUrl = terminalJumpUrl.trim();
            if (terminalJumpUrl.indexOf(TOPIC_TERMINAL_URL_PRE) == 0) {
                String topicJsonStr = terminalJumpUrl.substring(TOPIC_TERMINAL_URL_PRE.length());
                Topic topic = JsonKit.json2object(topicJsonStr, Topic.class);
                String topicName = topic.getTopicName();
                return ConfigCenter.FXIAOKE_HOME_INDEX_URL + "#" + TOPIC_WEB_URL_REF_PRE + topicName;
            } else if (terminalJumpUrl.indexOf(FEED_TERMINAL_URL_PRE) == 0) {
                String feedJsonStr = terminalJumpUrl.substring(FEED_TERMINAL_URL_PRE.length());
                Feed feed = JsonKit.json2object(feedJsonStr, Feed.class);
                String feedId = feed.getFeedId();
                return ConfigCenter.FXIAOKE_HOME_INDEX_URL + "#" + FEED_WEB_URL_REF_PRE + feedId;
            } else {
                return "";
            }
        } catch (Exception e) {
            // 容错处理
            logger.warn("transToWebURL error! terminalJumpUrl["+terminalJumpUrl+"]", e);
            return  "";
        }
    }

    private class Topic {
        private String topicName;

        public String getTopicName() {
            return topicName;
        }

        public void setTopicName(String topicName) {
            this.topicName = topicName;
        }
    }

    private class Feed {
        private String feedId;

        public String getFeedId() {
            return feedId;
        }

        public void setFeedId(String feedId) {
            this.feedId = feedId;
        }
    }

    private String transToTerminalURL(String webJumpUrl){
        if (StringUtils.isBlank(webJumpUrl)) {
            return "";
        }
        try {
            String ref = new URL(webJumpUrl).getRef();
            if (StringUtils.isEmpty(ref)) {
                return "";
            }
            String refTopicURL = TOPIC_WEB_URL_REF_PRE;
            String refFeedURL = FEED_WEB_URL_REF_PRE;
            if (ref.indexOf(refTopicURL) == 0) {
                String topicName = ref.substring(refTopicURL.length());
                return "fs_special://topic?{\"topicName\":\""+topicName+"\"}";
            } else if (ref.indexOf(refFeedURL) == 0) {
                String feedId = ref.substring(refFeedURL.length());
                return "fs_special://feed?{\"feedId\":"+feedId+"} ";
            } else {
                return "";
            }
        } catch (Exception e) {
            // 容错处理
            logger.warn("transToTerminalURL error! webJumpUrl["+webJumpUrl+"]", e);
            return  "";
        }
    }

    @Override
    public void saveCustomMenuList(FsUserVO user, CustomMenuForm customMenuForm) {
        appManager.checkAppTypeAndOwner(user, customMenuForm.getAppId());
        if (CommonConstant.YES != queryCustomMenuStatusByAppId(user, customMenuForm.getAppId())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "请先开启自定义菜单"); // ignoreI18n
        }
        OpenMenuVO menuVO = new OpenMenuVO();
        menuVO.setAppId(customMenuForm.getAppId());
        List<Menu> menus = new ArrayList<>();

        for (int i = 0; i < customMenuForm.getMenus().size(); i++) {
            MenuFormVO vo = customMenuForm.getMenus().get(i);
            Menu menu = vo2menu(vo);
            menus.add(menu);
        }
        menuVO.setMenus(menus);
        try {
            menuVO.checkParams();
        } catch (IllegalArgumentException e) {
            BizException exception = new BizException(AjaxCode.PARAM_ERROR, e,e.getLocalizedMessage());
            throw exception;
        }

        BaseResult<Void> baseResult = customMenuService.savePrivateAppCustomMenu(user.getEnterpriseAccount(), menuVO);
        if (!baseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "保存自定义菜单失败"); // ignoreI18n
        }
    }

    @Override
    public List<AppMenuUrlForm> queryWorkOrderTemplateByApp(FsUserVO user, String appId) {
        List<AppMenuUrlForm> appMenuUrlForms = Lists.newArrayList();

        //1、查询工单模板列表
        UserVo opUser = new UserVo();
        opUser.setAccountType(AccountTypeEnum.FS_ACCOUNT.getType());
        opUser.setOrganizeId(user.getEnterpriseAccount());
        opUser.setUserId(user.getUserId().toString());

        WorkOrderSwitchVo workOrderSwitchVo = new WorkOrderSwitchVo();
        workOrderSwitchVo.setBizId(appId);
        workOrderSwitchVo.setBizType(BizTypeEnum.FS_SERVICE.getType());
        workOrderSwitchVo.setOrganizeId(user.getEnterpriseAccount());
        workOrderSwitchVo.setAccountType(AccountTypeEnum.FS_ACCOUNT.getType());
        workOrderSwitchVo.setStatus(SwitchStatusEnum.ON.getStatus());

        List<Integer> statusList = Lists.newArrayList(WorkOrderDescribeStatusEnum.VALID.getStatus()); //完整的才能被关联

        com.facishare.open.common.result.BaseResult<List<WorkOrderDescribeVo>> result = workOrderDescribeService.queryList(opUser, workOrderSwitchVo, statusList);
        if (!result.isSuccess()) {
            logger.warn("workOrderDescribeService.queryList failed, opUser[{}], workOrderSwitchVo[{}], result[{}]", workOrderSwitchVo, appId, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询服务工单模板列表失败"); // ignoreI18n
        }
        List<WorkOrderDescribeVo> workOrderDescribeVos = result.getResult();
        if (CollectionUtils.isEmpty(workOrderDescribeVos)) {
            return appMenuUrlForms;
        }

        //2、创建 appMenuUrlForms
        String bizId = appId;
        Integer bizType = BizTypeEnum.FS_SERVICE.getType();
        workOrderDescribeVos.forEach(t -> {
            AppMenuUrlForm appMenuUrlForm = new AppMenuUrlForm();
            appMenuUrlForm.setAppId(t.getId());
            String apiName = t.getFormDescribeApiName();
            appMenuUrlForm.setBindAppKey(apiName);
            appMenuUrlForm.setBindAppName(t.getName());
            String menuUrl = String.format(ConfigCenter.WORK_ORDER_PAAS_URL, apiName, apiName, bizId, bizType);
            appMenuUrlForm.setMenuUrl(menuUrl);
            appMenuUrlForms.add(appMenuUrlForm);
        });

        return appMenuUrlForms;
    }

    @Override
    public boolean useEserviceWorkOrder(FsUserVO user, String appId) {
        // 如果没有配置 且 在灰度中， 就使用新版服务通工单
        List<AppMenuUrlForm> appMenuUrlForms = this.queryWorkOrderTemplateByApp(user, appId);
        return CollectionUtils.isEmpty(appMenuUrlForms) && ConfigCenter.grayToEserviceWorkOrder(user.getEa());
    }

    @Override
    public List<AppMenuUrlForm> queryApprovalByApp(FsUserVO user, String appId) {
        List<AppMenuUrlForm> appMenuUrlForms = Lists.newArrayList();

        //1、查询审批单定义
        FormDescribeVo formDescribeVoArg = new FormDescribeVo();
        formDescribeVoArg.setCreatorOrgId(user.getEa());
        formDescribeVoArg.setStatus(DescribeStatusEnum.VALID.getStatus());
        formDescribeVoArg.setUsingStatus(UsingStatusEnum.ON.getStatus());
        formDescribeVoArg.setType(DescribeTypeEnum.APPROVAL.getType());
        formDescribeVoArg.setBizType(com.facishare.open.intelligence.form.api.model.enums.type.BizTypeEnum.LINK_SERVICE.getType());
        formDescribeVoArg.setBizId(appId);
        com.facishare.open.common.result.BaseResult<List<FormDescribeVo>> result = formDescribeService.queryList(UserVO.getUpUserVO(user), formDescribeVoArg);
        if (!result.isSuccess()) {
            logger.warn("formDescribeService.queryList failed, opUser[{}], formDescribeVo[{}], result[{}]", user, formDescribeVoArg, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询审批单列表失败"); // ignoreI18n
        }
        List<FormDescribeVo> formDescribeVos = result.getResult();
        if (CollectionUtils.isEmpty(formDescribeVos)) {
            return appMenuUrlForms;
        }

        //2、创建 appMenuUrlForms
        formDescribeVos.forEach(formDescribeVo -> {
            String menuUrl = String.format(ConfigCenter.APPROVAL_URL, formDescribeVo.getBizId(), formDescribeVo.getId());

            AppMenuUrlForm appMenuUrlForm = new AppMenuUrlForm();
            appMenuUrlForm.setAppId(formDescribeVo.getId());                        //不是formDescribeVo.getBizId()
            appMenuUrlForm.setBindAppKey(formDescribeVo.getObjectDescribeApiName());
            appMenuUrlForm.setBindAppName(formDescribeVo.getName());
            appMenuUrlForm.setMenuUrl(menuUrl);
            appMenuUrlForms.add(appMenuUrlForm);
        });

        return appMenuUrlForms;
    }

    private Menu vo2menu(MenuFormVO vo) {
        Menu result = new Menu();
        result.setName(vo.getName());
        result.setType(vo.getType());
        result.setIsOpenResponse(vo.getIsOpenResponse());
        if (CustomMenuTypeEnum.URL.getCode() == vo.getType() || CustomMenuTypeEnum.BIND_APP.getCode() == vo.getType()) {
            String voUrl = vo.getUrl();
            String terminalJumpUrl = transToTerminalURL(voUrl);
            if (StringUtils.isNotBlank(terminalJumpUrl)) {
                result.setActionParam(terminalJumpUrl);
            } else {
                result.setActionParam(voUrl);
            }
        } else if (CustomMenuTypeEnum.IMAGE_TEXT.getCode() == vo.getType()) {
            result.setActionParam(vo.getMaterialId());
        } else {
            result.setActionParam(vo.getTxt());
        }
        if (!CollectionUtils.isEmpty(vo.getChildren())) {
            result.setChildren(new ArrayList<>());
            for (int i = 0; i < vo.getChildren().size(); i++) {
                MenuFormVO vo2 = vo.getChildren().get(i);
                Menu m = vo2menu(vo2);
                result.getChildren().add(m);
            }
        }
        return result;

    }
}
