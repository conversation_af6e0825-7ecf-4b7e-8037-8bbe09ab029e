package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.model.CustomerEvaluateForm;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.msg.model.CustomerEvaluateDO;
import com.facishare.open.msg.model.UpdateCustomerEvaluateVO;
import com.facishare.open.msg.result.CustomerEvaluateResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import com.facishare.open.msg.service.CustomerEvaluateService;

import java.util.List;
import java.util.Objects;

import static com.facishare.open.common.model.FsUserVO.toFsUserString;

/**
 * 客服满意度评价
 *
 * Created by liangk on 2017/8/22.
 */
@Controller
@RequestMapping("open/appcenter/app/customerEvaluate")
public class CustomerEvaluateController extends BaseController {

    @Resource
    private MsgCustomerService msgCustomerService;

    @Resource
    private CustomerEvaluateService customerEvaluateService;

    protected Logger logger = LoggerFactory.getLogger(getClass());
    /**
     * 查询客服评价结果
     * @param user 用户信息
     * @param appId 应用ID
     * @param msgLinkId 评价文本ID
     * @result AjaxResult
     */
    @RequestMapping("queryCustomerEvaluate")
    @ResponseBody
    public AjaxResult queryCustomerEvaluate(@ModelAttribute FsUserVO user,
                                            @RequestParam(value = "appId", required = false) String appId,
                                            @RequestParam(value = "msgLinkId") String msgLinkId) {
        CustomerEvaluateResult<CustomerEvaluateDO> result = customerEvaluateService.queryCustomerEvaluateByLinkId(msgLinkId);
        if (!result.isSuccess()) {
            logger.error("call CustomerEvaluateService.queryCustomerEvaluateByLinkId error, msgLinkId[{}], result[{}]", msgLinkId, result);
            throw new  BizException(result);
        }
        CustomerEvaluateDO customerEvaluateDO = result.getData();
        FsUserVO evaluatorVO = new FsUserVO(customerEvaluateDO.getReplyEa(), customerEvaluateDO.getEvaluatorId());
        boolean isEvaluator = FsUserVO.toFsUserString(evaluatorVO).equals(toFsUserString(user));
        // 判断是否为评价人，否则判断是否为客服
        if (!isEvaluator) {
            if(!isCustomer(user, appId)) {
                return new AjaxResult(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        }
        return new AjaxResult(customerEvaluateDO);
    }

    /**
     * 提交客服评价结果
     * @param user 用户
     * @param customerEvaluateForm 评价包装类
     * @return AjaxResult
     */
    @RequestMapping("updateCustomerEvaluate")
    @ResponseBody
    public AjaxResult updateCustomerEvaluate(@ModelAttribute FsUserVO user,
                                             @RequestBody CustomerEvaluateForm customerEvaluateForm) {
        UpdateCustomerEvaluateVO updateCustomerEvaluateVO = new UpdateCustomerEvaluateVO();
        updateCustomerEvaluateVO.setScore(customerEvaluateForm.getScore());
        updateCustomerEvaluateVO.setMsgLinkId(customerEvaluateForm.getMsgLinkId());
        updateCustomerEvaluateVO.setEvaluatorId(toFsUserString(user));
        updateCustomerEvaluateVO.setRemarks(customerEvaluateForm.getRemarks());
        updateCustomerEvaluateVO.setEvaluateResult(customerEvaluateForm.getEvaluateResult());

        CustomerEvaluateResult<Boolean> result = customerEvaluateService.updateCustomerEvaluate(updateCustomerEvaluateVO);
        if (!result.isSuccess()){
            logger.error("call CustomerEvaluateService.updateCustomerEvaluate error," +
                    " updateCustomerEvaluateVO[{}], result[{}]", result, updateCustomerEvaluateVO);
            throw new  BizException(result);
        }

        return new AjaxResult(result.getData());
    }

    private boolean isCustomer(FsUserVO fsUserVO, String appId) {
        CustomerSessionResult<CustomerSessionInfoVO> result = msgCustomerService.getCustomerSessionInfo(appId,
                fsUserVO.getEnterpriseAccount());
        if (!result.isSuccess()) {
            logger.error("call getCustomerSessionInfo error,appId[{}], " +
                    "fsEa[{}], result={}", appId, fsUserVO.getEnterpriseAccount(), result);
            throw new BizException(result);
        }
        List<String> customerList = result.getData().getCustomerList();
        return customerList.stream().anyMatch(customer -> Objects.equals(customer, fsUserVO.getUserId().toString()));
    }
}



