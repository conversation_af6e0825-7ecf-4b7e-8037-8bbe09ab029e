package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.manager.ArticleCategoryManager;
import com.facishare.open.app.center.manager.ServiceManager;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.material.api.model.vo.ArticleCategoryVO;
import com.facishare.open.material.api.model.vo.ArticleVO;
import com.facishare.open.material.api.result.MessageArticleCategoryResult;
import com.facishare.open.material.api.result.MongoPager;
import com.facishare.open.material.api.service.ArticleCategoryDetailService;
import com.facishare.open.material.api.service.ArticleCategoryService;
import com.facishare.open.operating.center.api.service.FirstTimeEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by liqiulin on 2016/11/14.
 */
@Service
public class ArticleCategoryManagerImpl implements ArticleCategoryManager {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    ArticleCategoryService articleCategoryService;

    @Resource
    ArticleCategoryDetailService articleCategoryDetailService;

    @Resource
    ServiceManager serviceManager;

    @Resource
    FirstTimeEventService firstTimeEventService;

    @Override
    public boolean needGuide(FsUserVO fsUserVO, boolean onlyQuery) {
        boolean needAllGuide = serviceManager.needDashBoardAllGuide(fsUserVO, true);
        if (!needAllGuide) {
            return needArticleCategoryGuide(fsUserVO, onlyQuery);
        } else {
            return false;
        }
    }

    private boolean needArticleCategoryGuide(FsUserVO fsUserVO, boolean onlyQuery) {
        String eventFlag = CenterConstants.FIRST_TIME_EVENT_FLAG_ARTICLE_CATEGORY_GUIDE;
        String eventExecutor = FsUserVO.toFsUserString(fsUserVO);
        BaseResult<Boolean> isFirstTimeResult =
                firstTimeEventService.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        if (!isFirstTimeResult.isSuccess()) {
            logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                            "eventFlag[{}], eventExecutor[{}], isFirstTimeResult[{}]",
                    eventFlag, eventExecutor, isFirstTimeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isFirstTimeResult, "判断第一次事件失败"); // ignoreI18n
        }
        return isFirstTimeResult.getResult();
    }

    @Override
    public List<ArticleCategoryVO> queryCategories(String fsEa, String appId) {
        BaseResult<List<ArticleCategoryVO>> queryCategoriesResult =
                articleCategoryService.queryCategories(fsEa, appId);
        if (!queryCategoriesResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.queryByFsEaAndAppId, fsEa[{}], appId[{}], queryCategoriesResult[{}]",
                    fsEa, appId, queryCategoriesResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, queryCategoriesResult, "查询文章分类失败"); // ignoreI18n
        }
        return queryCategoriesResult.getResult();
    }

    @Override
    public List<ArticleCategoryVO> queryCategoriesWithArticlesCount(String fsEa, String appId) {
        BaseResult<List<ArticleCategoryVO>> queryCategoriesWithArticlesCountResult =
                articleCategoryService.queryCategoriesWithArticleCount(fsEa, appId);
        if (!queryCategoriesWithArticlesCountResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.queryCategoriesWithArticleCount, fsEa[{}], appId[{}], queryCategoriesWithArticlesCountResult[{}]",
                    fsEa, appId, queryCategoriesWithArticlesCountResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, queryCategoriesWithArticlesCountResult, "查询文章分类失败"); // ignoreI18n
        }
        return queryCategoriesWithArticlesCountResult.getResult();
    }

    @Override
    public MongoPager<MessageArticleCategoryResult> queryCategoryArticles(String fsEa, String appId, String categoryId,
                                                               String title, int pageSize, long lastTime) {
        BaseResult<MongoPager<MessageArticleCategoryResult>> queryCategoryArticlesForPageResult =
                articleCategoryDetailService.queryCategoryArticlesForPage(fsEa, appId, categoryId, title, pageSize, lastTime);
        if (!queryCategoryArticlesForPageResult.isSuccess()) {
            logger.error("failed to call articleCategoryDetailService.queryCategoryArticlesForPage. " +
                    "fsEa[{}], appId[{}], categoryId[{}], title[{}], pageSize[{}], lastTime[{}], queryCategoryArticlesForPageResult[{}]",
                    fsEa, appId, categoryId, title, pageSize, lastTime, queryCategoryArticlesForPageResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, queryCategoryArticlesForPageResult, "查询分类的文章列表失败"); // ignoreI18n
        }
        return queryCategoryArticlesForPageResult.getResult();
    }

    @Override
    public MongoPager<ArticleVO> queryArticlesForAdd(String fsEa, String appId, String title, int pageSize, long lastTime) {
        BaseResult<MongoPager<ArticleVO>> queryArticlesForAddResult =
                articleCategoryService.queryArticlesForAdd(fsEa, appId, title, pageSize, lastTime);
        if (!queryArticlesForAddResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.queryArticlesForAdd. " +
                            "fsEa[{}], appId[{}], title[{}], pageSize[{}], lastTime[{}], queryArticlesForAddResult[{}]",
                    fsEa, appId, title, pageSize, lastTime, queryArticlesForAddResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, queryArticlesForAddResult, "查询文章列表失败"); // ignoreI18n
        }

        return queryArticlesForAddResult.getResult();
    }

    @Override
    public String createCategory(FsUserVO fsUserVO, String appId, String title) {
        String fsEa = fsUserVO.getEnterpriseAccount();
        String fsUserStr = FsUserVO.toFsUserString(fsUserVO);

        checkCategoryTitle(fsEa, appId, title);

        BaseResult<List<ArticleCategoryVO>> queryCategoriesResult =
                articleCategoryService.queryCategories(fsEa, appId);
        if (!queryCategoriesResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.queryCategories, " +
                            "fsEa[{}], appId[{}], queryCategoriesResult[{}]",
                    fsEa, appId, queryCategoriesResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, queryCategoriesResult, "查询分类列表失败"); // ignoreI18n
        }
        int maxCategorySize = ConfigCenter.SERVICE_ARTICLE_CATEGORY_MAX_SIZE;
        if (queryCategoriesResult.getResult().size() >= maxCategorySize) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "当前分类数已经超过最大限制个数" + maxCategorySize); // ignoreI18n
        }

        BaseResult<String> createCategoryResult = articleCategoryService.createCategory(fsEa,
                appId, fsUserStr, title);
        if(!createCategoryResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.queryByFsEaAndAppId, " +
                    "fsEa[{}], appId[{}], creator[{}], title[{}], queryByFsEaAndAppIdResult[{}]",
                    fsEa, appId, fsUserStr, title, createCategoryResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, createCategoryResult, "创建分类失败"); // ignoreI18n
        }
        return createCategoryResult.getResult();
    }

    @Override
    public void renameCategoryTile(String fsEa, String appId, String categoryId, String newTitle) {
        BaseResult<ArticleCategoryVO> queryCategoryByIdResult = articleCategoryService.queryCategoryById(categoryId);
        if (!queryCategoryByIdResult.isSuccess() || queryCategoryByIdResult.getResult() == null) {
            logger.error("failed to call articleCategoryService.queryCategoryById, " +
                            "categoryId[{}], queryCategoryByIdResult[{}]",
                    categoryId, queryCategoryByIdResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, queryCategoryByIdResult, "查询文章分类失败"); // ignoreI18n
        }
        ArticleCategoryVO articleCategoryVO = queryCategoryByIdResult.getResult();
        if (newTitle.equals(articleCategoryVO.getTitle())) {
            return;
        } else {
            checkCategoryTitle(fsEa, appId, newTitle);
        }

        BaseResult<Void> renameCategoryTitleResult  = articleCategoryService.renameCategoryTitle(categoryId, newTitle);
        if (!renameCategoryTitleResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.renameCategoryTitle, " +
                            "categoryId[{}], newTitle[{}], renameCategoryTitleResult[{}]",
                    categoryId, newTitle, renameCategoryTitleResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, renameCategoryTitleResult, "分类标题重命名失败"); // ignoreI18n
        }
    }

    private void checkCategoryTitle(String fsEa, String appId, String title) {
        BaseResult<Boolean> existsCategoryTitleResult = articleCategoryService.existsCategoryTitle(fsEa, appId, title);
        if (!existsCategoryTitleResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.existsCategoryTitle, " +
                            "fsEa[{}], appId[{}], title[{}], existsCategoryTitleResult[{}]",
                    fsEa, appId, title, existsCategoryTitleResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, existsCategoryTitleResult, "已经是否存在同名分类失败"); // ignoreI18n
        }
        if (existsCategoryTitleResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "已经存在同名分类"); // 注意：这里的errDescription不要改，前端有特殊判断 // ignoreI18n
        }
    }

    @Override
    public void deleteCategory(String faEa, String appId, String categoryId) {
        articleCategoryService.deleteCategory(faEa, appId, categoryId);
    }

    @Override
    public void deleteArticles(String fsEa, String appId, String categoryId, List<String> msgImageTextParamIds) {
        if (CollectionUtils.isEmpty(msgImageTextParamIds)) {
            return;
        }
        articleCategoryService.deleteArticles(fsEa, appId, categoryId, msgImageTextParamIds);
    }

    @Override
    public void saveCategoriesOrder(String fsEa, String appId, List<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return;
        }
        BaseResult<Void> saveCategoriesOrderResult = articleCategoryService.saveCategoriesOrder(fsEa, appId, categoryIds);
        if (!saveCategoriesOrderResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.saveCategoriesOrder, " +
                            "fsEa[{}], appId[{}], categoryIds[{}], saveCategoriesOrderResult[{}]",
                    fsEa, appId, categoryIds, saveCategoriesOrderResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, saveCategoriesOrderResult, "保存分类排序失败"); // ignoreI18n
        }
    }

    @Override
    public void addArticles(String fsEa, String appId, String categoryId, List<String> msgImageTextParamIds) {
        if (CollectionUtils.isEmpty(msgImageTextParamIds)) {
            return;
        }
        BaseResult<Void> addArticlesResult =
                articleCategoryService.addArticles(fsEa, appId, categoryId, msgImageTextParamIds);
        if (!addArticlesResult.isSuccess()) {
            logger.error("failed to call articleCategoryService.addArticles. " +
                            "fsEa[{}], appId[{}], categoryId[{}], msgImageTextParamIds[{}], addArticlesResult[{}]",
                    fsEa, appId, categoryId, msgImageTextParamIds, addArticlesResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, addArticlesResult, "保存分类排序失败"); // ignoreI18n
        }
    }
}
