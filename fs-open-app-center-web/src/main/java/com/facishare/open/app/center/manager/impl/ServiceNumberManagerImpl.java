package com.facishare.open.app.center.manager.impl;

//import com.facishare.assistant.it.open.request.WorkOrderAppBindArg;
//import com.facishare.assistant.it.open.response.GenericResult;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.ad.api.enums.ModuleKeyEnum;
import com.facishare.open.app.ad.api.service.CheckAppUpdatedService;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.BaseDO;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.app.center.api.model.property.OpenAppProperties;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.AppListResult;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.AppIconService;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.cons.ServiceFeatureTypeEnum;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.center.model.*;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoVO;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.result.GetCustomServiceSwitchResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.intelligence.form.api.model.vo.SwitchVo;
import com.facishare.open.intelligence.form.api.service.SwitchService;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.operating.center.api.service.OperatingAppMessageService;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.facishare.open.operating.center.utils.I18NUtils;
import com.facishare.open.work.order.paas.api.model.args.WorkOrderSwitchArg;
import com.facishare.open.work.order.paas.api.model.enums.AccountTypeEnum;
import com.facishare.open.work.order.paas.api.model.enums.BizTypeEnum;
import com.facishare.open.work.order.paas.api.model.vo.UserVo;
import com.facishare.open.work.order.paas.api.model.vo.WorkOrderSwitchVo;
import com.facishare.open.work.order.paas.api.service.WorkOrderSwitchService;
import com.fscishare.open.surey.model.arg.AppBindArg;
import com.fscishare.open.surey.service.AppBindService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ServiceNumberManagerImpl implements ServiceNumberManager {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private Logger oldServieDateLogger = LoggerFactory.getLogger("CENTER_OLD_SERVICE_DATA_FILE");

    @Resource
    private MsgAutoReplyService msgAutoReplyService;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private CheckAppUpdatedService checkAppUpdatedService;

    @Resource
    private OpenAppAdminService openAppAdminService;

    @Resource
    private AppMessageToMqManager appMessageToMqManager;

    @Resource
    private MsgCustomerService msgCustomerService;

    @Resource
    private SwitchService switchService;

    @Resource
    private AppIconService appIconService;
    @Resource
    private AppBindService appBindService;
    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Resource
    private WorkOrderSwitchService workOrderSwitchService;
    @Resource
    private AppManager appManager;
    @Resource
    private CustomMenuManager customMenuManager;
    @Resource
    private EserviceManager eserviceManager;

    @Value("${fs.open.app.center.image.url}")
    private String LOGO_FORMAT_URL;

    @Override
    public boolean updateServiceNumberOnOff(OpenServiceNumberDO entity, FsUserVO user, String lang, boolean isInit) {
        AppResult appResult = openAppService.loadOpenAppFast(entity.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }

        int customerSessionType = CommonConstant.IS_SERVICE;
        if (appResult.getResult().getAppType() == AppCenterEnum.AppType.LINK_SERVICE.value()) {
            customerSessionType = CommonConstant.IS_LINK_SERVICE;
        }
        MsgBaseResult msgBaseResult = msgAutoReplyService.setCustomServiceReplySwitchWithType(entity.getFsEa(), entity.getAppId(), entity.getSwitchType(), customerSessionType);
        if (!msgBaseResult.isSuccess()) {
            logger.warn("failed to call msgAutoReplyService.setCustomServiceReplySwitch, enterpriseAccount={}, appId={}, result={}",
                    entity.getFsEa(), entity.getAppId(), msgBaseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置多客服开关失败"); // ignoreI18n
        }

        BaseResult<List<String>> appAdminIdsResult = openAppAdminService.queryAppAdminIdList(
                entity.getFsEa(), entity.getAppId());
        if (!appAdminIdsResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.queryAppAdminIdList, fsEa={}, appId={}, result={}",
                    entity.getFsEa(), entity.getAppId(), appAdminIdsResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取应用管理员列表失败"); // ignoreI18n
        }
        List<String> appAdminIds = appAdminIdsResult.getResult();

        if (!isInit) {
            List<Integer> customers = new ArrayList<>();
            CustomerSessionResult<CustomerSessionInfoVO> result = msgCustomerService.getCustomerSessionInfo(entity.getAppId(), user.getEnterpriseAccount());
            if (!result.isSuccess()) {
                logger.error("getCustomerSessionInfo failed,appId[{}], user[{}], result={}", entity.getAppId(), user, result);
            }
            if (null != result.getData() && !CollectionUtils.isEmpty(result.getData().getCustomerList())) { //客服人员列表
                customers = result.getData().getCustomerList().stream().map(Integer::parseInt).collect(Collectors.toList());
            }
            appMessageToMqManager.serviceNumberOnOffToMq(user, lang, appAdminIds, appResult.getResult().getAppName(), entity.getSwitchType(), entity.getAppId(), customers);
        }
        return true;
    }


    @Override
    public void resetTagByServiceNumberModuleKey(FsUserVO user, String appId) {
        CommonThreadPoolUtils.getExecutor().execute(() -> {
            try {
                checkAppUpdatedService.resetTagByUserAndModuleKey(user, ModuleKeyEnum.SERVICE_NUMBER);
            } catch (Exception e) {
                logger.warn("failed to call checkAppUpdatedService.resetTagByServiceNumberModuleKey, user[{}], moduleKeyEnum={}", user, ModuleKeyEnum.SERVICE_NUMBER, e);
            }
        });
    }

    @Override
    public void updateCustomServiceRepresentiveListAndSessionName(FsUserVO user, String appId, String serviceName) {
        String fsEa = user.getEnterpriseAccount();
        GetCustomServiceSwitchResult customServiceSwitchResult = msgAutoReplyService.queryCustomServiceReplySwitch(fsEa, appId);
        if (!customServiceSwitchResult.isSuccess()) {
            logger.warn("failed to call queryCustomServiceReplySwitch, enterpriseAccount[{}], appId[{}], result[{}]",
                    fsEa, appId, customServiceSwitchResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询多客服开关失败"); // ignoreI18n
        }
        if (CommonConstant.SERVICE_NUMBER_ON == customServiceSwitchResult.getReplySwitch()) {
            BaseResult<List<String>> appAdminIdsResult = openAppAdminService.queryAppAdminIdList(fsEa, appId);
            if (!appAdminIdsResult.isSuccess()) {
                logger.warn("failed to call openAppAdminService.queryAppAdminIdList, fsEa[{}], appId=[{}], result[{}]",
                        fsEa, appId, appAdminIdsResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取应用管理员列表失败"); // ignoreI18n
            }
            if (!CollectionUtils.isEmpty(appAdminIdsResult.getResult())) {
                String sessionName = serviceName + "客服消息"; // ignoreI18n
                MsgBaseResult createCustomServiceResult = msgAutoReplyService.updateCustomServiceRepresentiveListAndSessionName(fsEa, appId, appAdminIdsResult.getResult(), sessionName);
                if (!createCustomServiceResult.isSuccess()) {
                    logger.warn("failed to call updateCustomServiceRepresentiveListAndSessionName, fsEa[{}], appId[{}], appAdminIds[{}], result[{}]",
                            fsEa, appId, appAdminIdsResult.getResult(), createCustomServiceResult);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "更新客服会话session名称和客服人员列表失败"); // ignoreI18n
                }
            }
        }
    }

    @Override
    public List<ServiceNumberVO> queryServiceNumbers(FsUserVO user) {
        List<ServiceNumberVO> serviceNumberVOList = new ArrayList<>();
        List<String> appViewIds = Lists.newArrayList();

        // 获取可见范围服务号组件
        List<UserCanViewListVO> serviceViewList = queryComponentsByUserAndType(user, AppAccessTypeEnum.SERVICE);
        if (!CollectionUtils.isEmpty(serviceViewList)) {
            appViewIds.addAll(serviceViewList.stream().map(UserCanViewListVO::getAppId).collect(Collectors.toList()));
        }

        // 获取可见范围互联服务号组件
        List<UserCanViewListVO> linkServiceViewList = queryComponentsByUserAndType(user, AppAccessTypeEnum.LINK_SERVICE);
        if (!CollectionUtils.isEmpty(linkServiceViewList)) {
            appViewIds.addAll(linkServiceViewList.stream().map(UserCanViewListVO::getAppId).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(appViewIds)) {
            AppListResult loadOpenAppByIdsFastResult = openAppService.loadOpenAppByIdsFast(appViewIds);
            if (!loadOpenAppByIdsFastResult.isSuccess()) {
                logger.error("openAppService.loadOpenAppByIdsFast failed,arg={} result={}", appViewIds, loadOpenAppByIdsFastResult);
                throw new BizException(loadOpenAppByIdsFastResult);
            }
            List<OpenAppDO> openAppDOs = loadOpenAppByIdsFastResult.getResult();
            // 按创建时间排序
            openAppDOs = openAppDOs.stream().sorted(Comparator.comparing(BaseDO::getGmtCreate).reversed()).collect(Collectors.toList());
            List<String> appIds = openAppDOs.stream().map(OpenAppDO::getAppId).collect(Collectors.toList());
            Map<String, String> appIdIconUrlMap = appManager.batchQueryAppIconUrl(appIds, IconType.SERVICE);

            for (OpenAppDO openAppDO : openAppDOs) {
                ServiceNumberVO snVo = new ServiceNumberVO();
                snVo.setAppId(openAppDO.getAppId());
                snVo.setAppName(openAppDO.getServiceName());
                snVo.setDesc(openAppDO.getAppDesc());

                String iconUrl = appIdIconUrlMap.get(snVo.getAppId());
                if (StringUtils.isNotBlank(iconUrl)) {
                    snVo.setImageUrl(iconUrl);
                }

                snVo.setAppType(openAppDO.getAppType());
                if (Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())) {
                    snVo.setUpstreamEa(openAppDO.getAppCreater());
                }
                serviceNumberVOList.add(snVo);
            }
        }
        return serviceNumberVOList;
    }

    private List<UserCanViewListVO> queryComponentsByUserAndType(FsUserVO user, AppAccessTypeEnum appAccessTypeEnum) {
        BaseResult<List<UserCanViewListVO>> baseResult = openFsUserAppViewService.queryComponentsByFsUser(user, appAccessTypeEnum);
        if (!baseResult.isSuccess()) {
            logger.error("openFsUserAppViewService.queryComponentsByFsUser failed. user=[{}], AppAccessTypeEnum=[{}], result=[{}]", user, appAccessTypeEnum, baseResult);
            throw new BizException(baseResult);
        }
        return baseResult.getResult();
    }

    public boolean setSupportStaff(FsUserVO user, SupportStaffForm supportStaffForm) {
        CustomerSessionInfoVO customerSessionInfoVO = new CustomerSessionInfoVO();
        customerSessionInfoVO.setAppId(supportStaffForm.getAppId());
        customerSessionInfoVO.setUserId(user.getUserId());
        customerSessionInfoVO.setCustomerName(supportStaffForm.getSupportStaffName());
        customerSessionInfoVO.setCustomerIcon(supportStaffForm.getSupportStaffLogo());
        if (StringUtils.isBlank(supportStaffForm.getSupportStaffLogo())) {
            customerSessionInfoVO.setCustomerIcon(LOGO_FORMAT_URL + ConfigCenter.CUSTOMER_DEFAULT_ICON);
        }
        customerSessionInfoVO.setEnterpriseAccount(user.getEnterpriseAccount());
        if (ArrayUtils.isNotEmpty(supportStaffForm.getSupportStaffs())) {
            customerSessionInfoVO.setCustomerList(Lists.newArrayList(supportStaffForm.getSupportStaffs()));
        }
        OpenAppDO openAppDO = appManager.loadAppBrief(supportStaffForm.getAppId());
        if (openAppDO.getAppType() == AppCenterEnum.AppType.LINK_SERVICE.value()) {
            customerSessionInfoVO.setCustomerSessionType(CommonConstant.IS_LINK_SERVICE);
        }
        CustomerSessionResult<Boolean> result = msgCustomerService.setCustomerSessionInfo(customerSessionInfoVO);
        if (!result.isSuccess()) {
            logger.error("setCustomerSessionInfo failed,arg={} result={}", customerSessionInfoVO, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置客服人员失败"); // ignoreI18n
        }
        return result.getData();
    }

    @Override
    public SupportStaffVO querySupportStaff(FsUserVO user, SupportStaffForm supportStaffForm) {
        CustomerSessionResult<CustomerSessionInfoVO> result = msgCustomerService.getCustomerSessionInfo(supportStaffForm.getAppId(), user.getEnterpriseAccount());
        if (!result.isSuccess()) {
            logger.error("getCustomerSessionInfo failed,appId[{}], user[{}], result={}", supportStaffForm.getAppId(), user, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取客服人员失败"); // ignoreI18n
        }
        SupportStaffVO supportStaffVO = new SupportStaffVO();
        CustomerSessionInfoVO customerSessionInfoVO = result.getData();
        if (null != customerSessionInfoVO) {
            supportStaffVO.setAppId(customerSessionInfoVO.getAppId());
            supportStaffVO.setSupportStaffLogo(customerSessionInfoVO.getCustomerIcon());
            supportStaffVO.setSupportStaffName(customerSessionInfoVO.getCustomerName());
            supportStaffVO.setSupportStaffs(customerSessionInfoVO.getCustomerList());
        }
        BaseResult<List<String>> appAdminIdsResult = openAppAdminService.queryAppAdminIdList(user.getEnterpriseAccount(), supportStaffForm.getAppId());
        if (!appAdminIdsResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.queryAppAdminIdList, fsEa[{}], appId=[{}], result[{}]",
                    user.getEnterpriseAccount(), supportStaffForm.getAppId(), appAdminIdsResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取应用管理员列表失败"); // ignoreI18n
        }
        List<String> adminIds = appAdminIdsResult.getResult().stream().map(fsUserString -> new FsUserVO(fsUserString).getUserId().toString()).collect(Collectors.toList());
        supportStaffVO.setAppAdminIds(adminIds);
        return supportStaffVO;
    }

    @Override
    public boolean modifyServiceWorkBenchSession(FsUserVO user, String appId, PlatformMetaSessionVO platformMetaSessionVO, ServiceTypeEnum serviceTypeEnum, Integer serviceType) {
        CustomerSessionResult<Boolean> customerSessionResult = msgCustomerService.updatePlatformMetaSessionNew(appId, user.getEnterpriseAccount(),
                serviceTypeEnum.getCode(), platformMetaSessionVO, serviceType);
        if (!customerSessionResult.isSuccess()) {
            logger.warn("failed to call updatePlatformMetaSessionNew, fsEa[{}], appId[{}], platformMetaSessionVO[{}], serviceType[{}], result={}",
                    user.getEnterpriseAccount(), appId, platformMetaSessionVO, serviceType, customerSessionResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "更新服务号工作台会话失败"); // ignoreI18n
        }
        return true;
    }

    @Override
    public boolean modifyServiceSession(FsUserVO user, String appId, ServiceTypeEnum serviceTypeEnum) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        int type = 0;
        if (AppCenterEnum.AppType.SERVICE.value() == appResult.getResult().getAppType()) {
            type = CommonConstant.IS_SERVICE;
        } else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType()) {
            type = CommonConstant.IS_OUTER_SERVICE;
        } else if (AppCenterEnum.AppType.LINK_SERVICE.value() == appResult.getResult().getAppType()) {
            type = CommonConstant.IS_LINK_SERVICE;
        }
        CustomerSessionResult<Void> customerSessionResult = msgCustomerService.setAppServiceStatus(user.getEnterpriseAccount(), appId, serviceTypeEnum == ServiceTypeEnum.ON, type);
        if (!customerSessionResult.isSuccess()) {
            logger.warn("failed to call setAppServiceStatus, user={}, appId={}, type={}, result={}", user, appId, type, customerSessionResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置服务号状态失败"); // ignoreI18n
        }

        //外联服务号启用后如果有（已停用）的话,去掉.
        if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType()) {
            String workBeanchName = appResult.getResult().getServiceName();

            if (serviceTypeEnum == ServiceTypeEnum.ON &&
                    workBeanchName.contains(ConfigCenter.SERIVCE_OFF_NOTIC_PER)) {
                workBeanchName = workBeanchName.replace(ConfigCenter.SERIVCE_OFF_NOTIC_PER, "");
                PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
                platformMetaSessionVO.setCustomerSessionName(workBeanchName);
                modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, CommonConstant.IS_OUTER_SERVICE);

            }

        }

        return true;
    }

    //老工单不用了
/*  @Override
    public boolean setWorkOrder(FsUserVO user, ServiceNumberForm form) {
        AppResult appResult = openAppService.loadOpenAppFast(form.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        WorkOrderAppBindArg workOrderAppBindArg = new WorkOrderAppBindArg();
        workOrderAppBindArg.setMid(CenterConstants.DEFAULT_CREATE_TEMPLATE_ID);
        if (StringUtils.isNotBlank(appResult.getResult().getProperties())) {
            OpenAppProperties openAppProperties = JsonKit.json2object(appResult.getResult().getProperties(), OpenAppProperties.class);
            if (null != openAppProperties && !Strings.isNullOrEmpty(openAppProperties.getCreateTemplateId())) {
                workOrderAppBindArg.setMid(openAppProperties.getCreateTemplateId());
            }
        }
        workOrderAppBindArg.setAppId(form.getAppId());
        workOrderAppBindArg.setIsEnable(form.getStatus());
        workOrderAppBindArg.setEnterpriseAccount(user.getEnterpriseAccount());
        workOrderAppBindArg.setSeesionName(CenterConstants.WORK_ORDER_SESSION_NAME);
        workOrderAppBindArg.setSessionIcon(LOGO_FORMAT_URL + ConfigCenter.WORK_ORDER_DEFAULT_ICON);
        GenericResult<Integer> result = workOrderAppService.bindApp(workOrderAppBindArg);
        if (!result.isSuccess()) {
            logger.warn("failed to call bindApp, appBindArg[{}], result={}", workOrderAppBindArg, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置工单管理开关失败"); // ignoreI18n
        }
        return false;
    }*/

    @Override
    public void setWorkOrderPaas(FsUserVO user, ServiceNumberForm form) {
        AppResult appResult = openAppService.loadOpenAppFast(form.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }

        UserVo opUser = new UserVo();
        opUser.setAccountType(AccountTypeEnum.FS_ACCOUNT.getType());
        opUser.setOrganizeId(user.getEnterpriseAccount());
        opUser.setUserId(user.getUserId().toString());

        if (StringUtils.isNotBlank(form.getType())
                && form.getType().equals(ServiceFeatureTypeEnum.ESERVICE_WORK_ORDER.getType())) {

            boolean result = eserviceManager.updateEserviceWorkOrderOpenStatus(
                    user.getEa(), user.getUserId(), form.getAppId(), form.getStatus());
            if (!result) {
                logger.warn("updateEserviceWorkOrderOpenStatus failed, opUser[{}], form[{}], result[{}]",
                        opUser, form, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置工单管理开关失败"); // ignoreI18n
            }

        } else {
            WorkOrderSwitchArg workOrderSwitchArg = new WorkOrderSwitchArg();
            workOrderSwitchArg.setMid(CenterConstants.DEFAULT_CREATE_TEMPLATE_ID);
            if (StringUtils.isNotBlank(appResult.getResult().getProperties())) {
                OpenAppProperties openAppProperties = JsonKit.json2object(appResult.getResult().getProperties(), OpenAppProperties.class);
                if (null != openAppProperties && !Strings.isNullOrEmpty(openAppProperties.getCreateTemplateId())) {
                    workOrderSwitchArg.setMid(openAppProperties.getCreateTemplateId());
                }
            }
            workOrderSwitchArg.setBizId(form.getAppId());
            workOrderSwitchArg.setBizType(BizTypeEnum.FS_SERVICE.getType());
            workOrderSwitchArg.setOrganizeId(user.getEnterpriseAccount());
            workOrderSwitchArg.setAccountType(AccountTypeEnum.FS_ACCOUNT.getType());
            workOrderSwitchArg.setStatus(form.getStatus());

            com.facishare.open.common.result.BaseResult<Void> result = workOrderSwitchService.saveOrUpdateSwitch(opUser, workOrderSwitchArg);
            if (!result.isSuccess()) {
                logger.warn("saveOrUpdateSwitch failed, opUser[{}], workOrderSwitchArg[{}], result[{}]", opUser, workOrderSwitchArg, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置工单管理开关失败"); // ignoreI18n
            }
        }
    }

    @Override
    public Integer queryWorkOrderPaasStatus(FsUserVO user, ServiceNumberForm form) {
        UserVo opUser = new UserVo();
        opUser.setAccountType(AccountTypeEnum.FS_ACCOUNT.getType());
        opUser.setOrganizeId(user.getEnterpriseAccount());
        opUser.setUserId(user.getUserId().toString());

        com.facishare.open.common.result.BaseResult<WorkOrderSwitchVo> result = workOrderSwitchService.query(opUser, form.getAppId(), BizTypeEnum.FS_SERVICE.getType(), user.getEnterpriseAccount(), AccountTypeEnum.FS_ACCOUNT.getType());
        if (!result.isSuccess()) {
            logger.warn("workOrderSwitchService.query failed, opUser[{}], bizId[{}], result[{}]", opUser, form.getAppId(), result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询服务工单管理开关失败"); // ignoreI18n
        }
        //查询不到记录，则默认关闭
        if (null == result.getResult()){
            return 2;
        }
        return result.getResult().getStatus();
    }

/*  老工单下线
    @Override
    public Integer queryWorkOrderStatus(FsUserVO user, ServiceNumberForm form) {
        AppBindArg appBindArg = new AppBindArg();
        appBindArg.setAppId(form.getAppId());
        appBindArg.setEnterpriseAccount(user.getEnterpriseAccount());
        GenericResult<Integer> result = workOrderAppService.checkAppIsEnable(user.getEnterpriseAccount(), form.getAppId());
        if (!result.isSuccess()) {
            logger.warn("failed to call bindApp, appBindArg[{}], result={}", appBindArg, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询工单管理开关失败");
        }
        return result.getData();
    }*/

    @Override
    public boolean setQuestionnaire(FsUserVO user, ServiceNumberForm form) {
        AppResult appResult = openAppService.loadOpenAppFast(form.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        AppBindArg appBindArg = new AppBindArg();
        if (StringUtils.isNotBlank(appResult.getResult().getProperties())) {
            OpenAppProperties openAppProperties = JsonKit.json2object(appResult.getResult().getProperties(), OpenAppProperties.class);
            if (null != openAppProperties && !Strings.isNullOrEmpty(openAppProperties.getCreateTemplateId())) {
                appBindArg.setMid(openAppProperties.getCreateTemplateId());
            }
        }
        appBindArg.setAppId(form.getAppId());
        appBindArg.setIsEnable(form.getStatus());
        appBindArg.setEnterpriseAccount(user.getEnterpriseAccount());
        appBindArg.setSeesionName(CenterConstants.QUESTIONNAIRE_SESSION_NAME);
        appBindArg.setSessionIcon(LOGO_FORMAT_URL + ConfigCenter.QUESTIONNAIRE_DEFAULT_ICON);
        com.fscishare.open.surey.model.result.BaseResult<String> result = appBindService.bindApp(appBindArg);
        if (!result.isSuccess()) {
            logger.warn("failed to call bindApp, appBindArg[{}], result={}", appBindArg, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置问卷管理开关失败"); // ignoreI18n
        }
        return false;
    }

    @Override
    public Integer queryQuestionnaireStatus(FsUserVO user, ServiceNumberForm form) {
        try {
            AppBindArg appBindArg = new AppBindArg();
            appBindArg.setAppId(form.getAppId());
            appBindArg.setEnterpriseAccount(user.getEnterpriseAccount());
            com.fscishare.open.surey.model.result.BaseResult<Integer> result = appBindService.getBindStatus(appBindArg);
            if (!result.isSuccess()) {
                logger.warn("failed to call bindApp, appBindArg[{}], result={}", appBindArg, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询问卷管理开关失败"); // ignoreI18n
            }
            return result.getResult();
        } catch(Exception e) {
            return 0;
        }

    }

    @Override
    public Integer queryApprovalStatus(FsUserVO user, ServiceNumberForm form) {
        SwitchVo switchVoArg = new SwitchVo();
        switchVoArg.setOrgId(user.getEa());
        switchVoArg.setBizType(com.facishare.open.intelligence.form.api.model.enums.type.BizTypeEnum.LINK_SERVICE.getType());
        switchVoArg.setBizId(form.getAppId());

        com.facishare.open.common.result.BaseResult<SwitchVo> switchVoResult = switchService.query(user, switchVoArg);
        if (!switchVoResult.isSuccess()) {
            logger.warn("switchService.query failed, opUser[{}], switchVoArg[{}], result[{}]", user, switchVoArg, switchVoResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询审批单开关失败"); // ignoreI18n
        }
        //查询不到记录，则默认关闭
        if (null == switchVoResult.getResult()){
            return 2;
        }
        return switchVoResult.getResult().getStatus();
    }

    @Override
    public void setApproval(FsUserVO user, ServiceNumberForm form) {
        AppResult appResult = openAppService.loadOpenAppFast(form.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }

        SwitchVo switchVo = new SwitchVo();
        switchVo.setOrgId(user.getEa());
        switchVo.setBizType(com.facishare.open.intelligence.form.api.model.enums.type.BizTypeEnum.LINK_SERVICE.getType());
        switchVo.setBizId(form.getAppId());
        switchVo.setStatus(form.getStatus());
        com.facishare.open.common.result.BaseResult<Void> result = switchService.saveOrUpdateSwitch(user, switchVo);
        if (!result.isSuccess()) {
            logger.warn("saveOrUpdateSwitch failed, opUser[{}], switchVo[{}], result[{}]", user, switchVo, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "设置审批单开关失败"); // ignoreI18n
        }
    }

    @Override
    public void setServiceOldDate(FsUserVO user, String appId, Integer setType) {
        if (StringUtils.isNotBlank(appId)) {
            AppResult appResult = openAppService.loadOpenAppFast(appId);
            if (!appResult.isSuccess()) {
                oldServieDateLogger.warn("updateServiceOldDate failed openAppService.loadOpenAppByIdsFast failed,user={} result={}", user, appResult);
                throw new BizException(AjaxCode.PARAM_ERROR, "找不到该服务号"); // ignoreI18n
            }
            updateServiceOldDate(user, appResult.getResult());
            return;
        }

        //全网灰度企业
        boolean isAllFsEa = false;
        if (9999 == setType && user.getEnterpriseAccount().equals("all")) {
            user.setEnterpriseAccount(null);
            isAllFsEa = true;
        }

        OpenAppDO openApp = new OpenAppDO();
        openApp.setAppType(AppCenterEnum.AppType.SERVICE.value());
//        openApp.setCreaterType(5);
        AppListResult openAppDOList = openAppService.queryCustomApps(user, openApp);
        if (!openAppDOList.isSuccess()) {
            oldServieDateLogger.warn("updateServiceOldDate failed openAppService.loadOpenAppByIdsFast failed,user={} result={}", user, openAppDOList);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取服务号列表失败"); // ignoreI18n
        }

        List<OpenAppDO> openAppDOs = openAppDOList.getResult();
        if (!CollectionUtils.isEmpty(openAppDOs)) {
            for (OpenAppDO app : openAppDOs) {
                //全网恢复数据，需要过滤之前已经恢复过的数据
                if (isAllFsEa && StringUtils.isNotBlank(ConfigCenter.IS_NOT_SET_OLD_SERVICE_FSEA)) {
                    List<String> fsEas = Lists.newArrayList(ConfigCenter.IS_NOT_SET_OLD_SERVICE_FSEA.split(";"));
                    if (fsEas.contains(app.getAppCreater())) {
                        oldServieDateLogger.warn("this fsEa is not need update seviceOldData fsEa={}", app.getAppCreater());
                        continue;
                    }
                }
                updateServiceOldDate(user, app);
            }
        }

    }

    private boolean updateServiceOldDate(FsUserVO user, OpenAppDO app) {
        oldServieDateLogger.warn("start updateServiceOldDate openApp={}", app);
        user.setEnterpriseAccount(app.getAppCreater());
        String fsEa = app.getAppCreater();
        String appId = app.getAppId();
        logger.warn("updateServiceOldDate fsEa={}, appId={}", fsEa, appId);
        GetCustomServiceSwitchResult customServiceSwitchResult = msgAutoReplyService.queryCustomServiceReplySwitch(fsEa, appId);
        if (!customServiceSwitchResult.isSuccess()) {
            oldServieDateLogger.warn("updateServiceOldDate failed to call queryCustomServiceReplySwitch, enterpriseAccount[{}], appId[{}], result[{}]",
                    fsEa, appId, customServiceSwitchResult);
            return false;
        }
        ServiceTypeEnum typeEnum = ServiceTypeEnum.UPDATE;
        if (2 == customServiceSwitchResult.getReplySwitch()) {
            typeEnum = ServiceTypeEnum.CREATE;
        }
        BaseResult<List<String>> appAdminIdsResult = openAppAdminService.queryAppAdminIdList(fsEa, appId);
        if (!appAdminIdsResult.isSuccess()) {
            oldServieDateLogger.warn("updateServiceOldDate failed to call openAppAdminService.queryAppAdminIdList, fsEa={}, appId={}, result={}",
                    fsEa, appId, appAdminIdsResult);
            return false;
        }
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        List<Integer> appAdminIds = appAdminIdsResult.getResult().stream().map(fsUserString -> new FsUserVO(fsUserString).getUserId()).collect(Collectors.toList());
        platformMetaSessionVO.setAdmins(appAdminIds);
        //工作台session名称
        platformMetaSessionVO.setCustomerSessionName(app.getAppName() + CenterConstants.SERVICE_NUMBER_MSG);
        //工作台副标题
        platformMetaSessionVO.setCustomerSessionSubName(CenterConstants.CUSTOMER_SESSION_SUB_NAME);
        //工作台session 头像
        platformMetaSessionVO.setCustomerSessionPortrait(queryAppIconUrl(appId, IconType.WEB));
        //管理员工作台描述
        platformMetaSessionVO.setAdminDescription(CenterConstants.ADMIN_DESCRIPTION);
        //客服工作台描述
        platformMetaSessionVO.setCustomerDescription(CenterConstants.CUSTOMER_DESCRIPTION);

        try {
            modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, typeEnum, CommonConstant.IS_SERVICE);
        } catch (Exception e) {
            oldServieDateLogger.warn("updateServiceOldDate failed user={}, appId={}, platformMetaSessionVO={}, typeEnum={}", user, appId, platformMetaSessionVO, typeEnum);
            return false;
        }
        oldServieDateLogger.warn("updateServiceOldDate success user={}, appId={}, platformMetaSessionVO={}, typeEnum={}", user, appId, platformMetaSessionVO, typeEnum);
        return true;
    }

    /**
     * 查询iconUrl
     *
     * @param appIdOrComponentId 应用或者组件ID
     * @param type               图片类型
     * @return iconUrl
     */
    private String queryAppIconUrl(String appIdOrComponentId, IconType type) {
        com.facishare.open.common.result.BaseResult<String> iconUrlResult = appIconService.queryAppIconUrl(appIdOrComponentId, type);
        if (!iconUrlResult.isSuccess()) {
            logger.warn("queryAppIconUrl failed, appIdOrComponentId[{}], type[{}], result[{}] : " + appIdOrComponentId, type, iconUrlResult);
        }
        return iconUrlResult.getResult();
    }
}
