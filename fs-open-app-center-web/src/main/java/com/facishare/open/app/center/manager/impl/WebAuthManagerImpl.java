package com.facishare.open.app.center.manager.impl;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.CrmViewService;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.EiEaManager;
import com.facishare.open.app.center.manager.WebAuthManager;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum;
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments;
import com.facishare.organization.adapter.api.permission.model.GetFunctionCodesByEmployeeAndAppId;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.uc.api.model.employee.arg.CheckPwdByEmployeeIdArg;
import com.facishare.uc.api.model.employee.result.CheckPwdByEmployeeResult;
import com.facishare.uc.api.service.EmployeeEditionService;
import com.google.common.hash.Hashing;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING;


/**
 * <AUTHOR>
 * @date 2015年9月1日
 */
@Service
public class WebAuthManagerImpl implements WebAuthManager {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 协同平台权限应用ID
     */
    private static final String COLLABORATION_PLATFORM_FUNCTION_APP_ID = "facishare-system";

    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;
    @Resource
    private OpenAppAdminService openAppAdminService;
    @Resource
    public EmployeeEditionService employeeEditionService;
    @Resource
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Resource
    private CrmViewService crmViewService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private EiEaManager eiEaManager;
    @Resource
    private AppManager appManager;

    @Override
    public boolean hasAppManageFunction(FsUserVO userVO, String appId) {
        OpenAppDO briefAppDO = appManager.loadAppBrief(appId);
        return hasAppManageFunction(userVO, briefAppDO.getAppType());
    }

    @Override
    public boolean hasAppManageFunction(FsUserVO userVO, OpenAppDO openAppDO) {
        return hasAppManageFunction(userVO, openAppDO.getAppType());
    }

    @Override
    public boolean hasAppManageFunction(FsUserVO userVO, Integer appType) {
        boolean isCustomApp = Objects.equals(AppCenterEnum.AppType.CUSTOM_APP.value(), appType);
        if (isCustomApp) {
            return hasAppCustomManageFunctionCode(userVO);
        } else {
            return hasAppBaseManageFunctionCode(userVO);
        }
    }

    @Override
    public boolean hasAppBaseManageFunctionCode(FsUserVO fsUserVO) {
        List<GetFunctionCodesByEmployeeAndAppId.FunctionCode> functionCodes = getSystemFunctionCodes(fsUserVO);
        return hasSystemFunctionCode(functionCodes, SystemFunctionCodeEnum.APP_BASE_MANAGE);
    }

    @Override
    public boolean hasAppCustomManageFunctionCode(FsUserVO fsUserVO) {
        List<GetFunctionCodesByEmployeeAndAppId.FunctionCode> functionCodes = getSystemFunctionCodes(fsUserVO);
        return hasSystemFunctionCode(functionCodes, SystemFunctionCodeEnum.APP_CUSTOM_MANAGE);
    }

    public boolean hasSystemFunctionCode(List<GetFunctionCodesByEmployeeAndAppId.FunctionCode> functionCodes, SystemFunctionCodeEnum systemFunctionCodeEnum) {
        if (CollectionUtils.isEmpty(functionCodes)) {
            return false;
        } else {
            return functionCodes.stream().filter(GetFunctionCodesByEmployeeAndAppId.FunctionCode::getIsEnable).anyMatch(functionCode -> Objects.equals(systemFunctionCodeEnum.getFunctionCode(), functionCode.getFunctionCode()));
        }
    }

    @Override
    public List<String> getSystemFunctionCodes(FsUserVO fsUserVO, Boolean isEnable) {
        List<GetFunctionCodesByEmployeeAndAppId.FunctionCode> functionCodes = getSystemFunctionCodes(fsUserVO);
        if (CollectionUtils.isEmpty(functionCodes)) {
            return new ArrayList<>();
        }

        if (isEnable != null) {
            if (isEnable) {
                functionCodes =  functionCodes.stream().filter(code -> code.getIsEnable().equals(true)).collect(Collectors.toList());
            } else {
                functionCodes =  functionCodes.stream().filter(code -> code.getIsEnable().equals(false)).collect(Collectors.toList());
            }
        }

        return functionCodes.stream().map(GetFunctionCodesByEmployeeAndAppId.FunctionCode::getFunctionCode).collect(Collectors.toList());
    }

    private List<GetFunctionCodesByEmployeeAndAppId.FunctionCode> getSystemFunctionCodes(FsUserVO fsUserVO) {
        GetFunctionCodesByEmployeeAndAppId.Argument argument = new GetFunctionCodesByEmployeeAndAppId.Argument();
        argument.setEnterpriseId(eiEaManager.getEi(fsUserVO.getEa()));
        argument.setEmployeeId(fsUserVO.getUserId());
        argument.setAppId(COLLABORATION_PLATFORM_FUNCTION_APP_ID);
        argument.setCurrentEmployeeId(fsUserVO.getUserId());
        return permissionService.getFunctionCodesByEmployeeAndAppId(argument).getFunctionCodes();
    }


    @Override
    public boolean isLinkAdmin(FsUserVO fsUser) {
        CheckFunctionCodeAndGetManageDepartments.Argument argument = new CheckFunctionCodeAndGetManageDepartments.Argument();
        argument.setEmployeeId(fsUser.getUserId());
        argument.setFunctionCode(ENTERPRISE_INTERCONNECT_SETTING.getFunctionCode());
        argument.setAppId(ENTERPRISE_INTERCONNECT_SETTING.getAppId());
        argument.setCurrentEmployeeId(fsUser.getUserId());
        argument.setEnterpriseId(eiEaManager.getEi(fsUser.getEa()));
        CheckFunctionCodeAndGetManageDepartments.Result result = permissionService.checkFunctionCodeAndGetManageDepartments(argument);
        return result.getHasAbility();
    }

    @Override
    public boolean isAppAdmin(FsUserVO fsUser) {
        BaseResult<Boolean> isAppAdminResult =
                openAppAdminService.isAppAdmin(FsUserVO.toFsUserString(fsUser));
        if (!isAppAdminResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.isAppAdmin, fsUser=[{}], result=[{}]",
                    fsUser, isAppAdminResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isAppAdminResult, "判断是否应用管理员错误"); // ignoreI18n
        }
        return isAppAdminResult.getResult();
    }

    @Override
    public boolean isCrmAdmin(FsUserVO fsUserVO) {
        com.facishare.open.common.result.BaseResult<Boolean> isCrmAdminResult =
                crmViewService.isCrmAdmin(FsUserVO.toFsUserString(fsUserVO));
        if (!isCrmAdminResult.isSuccess()) {
            logger.warn("fail to call crmViewService.isCrmAdmin: fsUserVO[{}], result[{}]",
                    fsUserVO, isCrmAdminResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isCrmAdminResult, "判断是否CRM管理员错误"); // ignoreI18n
        }
        return isCrmAdminResult.getResult();
    }

    @Override
    public boolean isServiceAdmin(FsUserVO fsUser) {
        BaseResult<Boolean> isServiceAdminResult =
                openAppAdminService.isServiceAdmin(FsUserVO.toFsUserString(fsUser));
        if (!isServiceAdminResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.isServiceAdmin, fsUser=[{}], result=[{}]",
                    fsUser, isServiceAdminResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isServiceAdminResult, "判断是否服务号管理员错误"); // ignoreI18n
        }
        return isServiceAdminResult.getResult();
    }

    /**
     * 使用token获取对应的纷享账号
     *
     * @param fsAuthXCookie
     * @return 纷享账号, 如果验证失败则抛出异常 @code{BizException(code=AjaxCode.NO_AUTHORITY)}
     */
    @Override
    public FsUserVO loadWebFsUser(String fsAuthXCookie) {
        CookieToAuth.Argument argument = new CookieToAuth.Argument();
        argument.setCookie(fsAuthXCookie);
        argument.setFsToken(null);
        argument.setIp(null);
        CookieToAuth.Result<AuthXC> cookieToAuthResult = activeSessionAuthorizeService.cookieToAuthXC(argument);
        if (!cookieToAuthResult.isSucceed()
                || !ValidateStatus.NORMAL.equals(cookieToAuthResult.getValidateStatus())) {
            logger.warn("activeSessionAuthorizeService.cookieToAuthXC failed, argument={}, resultUser=[{}]", argument, cookieToAuthResult);
            throw new BizException(AjaxCode.USER_NOT_LOGIN, "验证用户登录信息失败"); // ignoreI18n
        }
        AuthXC authXC = cookieToAuthResult.getBody();
        return new FsUserVO(authXC.getEnterpriseAccount(), authXC.getEmployeeId(), authXC.getAccount());
    }

    @Override
    public boolean isFsAdmin(final FsUserVO fsUser) {
        BaseResult<Boolean> result = openAppAddressBookEmployeeService.isAdmin(fsUser.getEnterpriseAccount(), fsUser.getUserId());
        if (!result.isSuccess()) {
            logger.info("isAdmin error ,ea[{}], userId[{}], result [{}]", fsUser.getEnterpriseAccount(), fsUser.getUserId(), result);
            throw new BizException(AjaxCode.NO_AUTHORITY, result, "管理员身份验证失败"); // ignoreI18n
        }
        return result.getResult();
    }

    @Override
    public boolean checkAdminPassword(FsUserVO adminUser, String password) {
        return checkAdminPasswordNew(adminUser, password);
    }

    @Override
    public boolean isAppAdmin(FsUserVO user, String appId) {
        if(StringUtils.isBlank(appId)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "appId["+appId+"] is blank");
        }
        BaseResult<Boolean> isAppAdminResult = openAppAdminService.isAppAdmin(FsUserVO.toFsUserString(user), appId);
        if (!isAppAdminResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.isAppAdmin, user=[{}], appId=[{}], result=[{}]",
                    user, appId, isAppAdminResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isAppAdminResult, "判断是否应用管理员错误"); // ignoreI18n
        }
        return isAppAdminResult.getResult();
    }

    @Override
    public List<Integer> queryAdminIds(String fsEa) {
        final BaseResult<List<Integer>> adminIdsResult =  openAppAddressBookEmployeeService.getAdminIds(fsEa);
        if (!adminIdsResult.isSuccess()) {
            logger.warn("fail to call employeeService.getAdminIds, fsEa={}, result={}",
                    fsEa, adminIdsResult);
            throw new BizException(adminIdsResult);
        }
        return adminIdsResult.getResult();
    }

    @Override
    public FsUserVO queryOneAdmin(String fsEa) {
        //获取管理员账号
        final List<Integer> adminIds = this.queryAdminIds(fsEa);
        if (CollectionUtils.isEmpty(adminIds)) {
            throw new BizException(AppCenterCodeEnum.NO_FS_ADMIN);
        }
        return new FsUserVO(fsEa, adminIds.get(0));
    }



    private boolean checkAdminPasswordNew(FsUserVO user, String password) {
        CheckPwdByEmployeeIdArg checkPwdByEmployeeIdArg = new CheckPwdByEmployeeIdArg();
        checkPwdByEmployeeIdArg.setEnterpriseAccount(user.getEnterpriseAccount());
        checkPwdByEmployeeIdArg.setEmployeeId(user.getUserId());

        checkPwdByEmployeeIdArg.setPassword(Hashing.md5().newHasher().putBytes(password.getBytes()).hash().toString());
        CheckPwdByEmployeeResult checkPwdByEmployeeResult = employeeEditionService.checkPwdByEmployeeId(checkPwdByEmployeeIdArg);
        boolean passwordRight =  ((checkPwdByEmployeeResult.getResultCode() == CheckPwdByEmployeeResult.RESULT_CODE_SUCCESS)
                && (checkPwdByEmployeeResult.getCheckEmployeePasswordData().getErrorNumber() == 0));
        return passwordRight;
    }
}
