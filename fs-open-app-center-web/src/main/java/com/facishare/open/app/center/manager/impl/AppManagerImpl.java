package com.facishare.open.app.center.manager.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.*;
import com.facishare.open.app.center.api.model.enums.*;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType;
import com.facishare.open.app.center.api.model.property.OpenAppProperties;
import com.facishare.open.app.center.api.model.property.OpenDevProperties;
import com.facishare.open.app.center.api.model.vo.IconFileVO;
import com.facishare.open.app.center.api.result.AppListResult;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.IntegerResult;
import com.facishare.open.app.center.api.result.*;
import com.facishare.open.app.center.api.service.*;
import com.facishare.open.app.center.api.utils.ScopeCenter;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.cons.ServiceFeatureTypeEnum;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.center.model.AppDevModeInfo;
import com.facishare.open.app.center.model.*;
import com.facishare.open.app.center.mq.item.tags.AppCenterMqTagsConstant;
import com.facishare.open.app.center.threadlocal.UserContextHolder;
import com.facishare.open.app.center.utils.*;
import com.facishare.open.app.pay.api.enums.AppOnOffEnum;
import com.facishare.open.app.pay.api.enums.AppPayEnum;
import com.facishare.open.app.pay.api.enums.PayStatus;
import com.facishare.open.app.pay.api.model.QuotaVo;
import com.facishare.open.app.pay.api.service.AppOnOffService;
import com.facishare.open.app.pay.api.service.EnterprisePayService;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.open.appaccesscontrol.result.AppIpWhiteListResult;
import com.facishare.open.appaccesscontrol.service.AppIpWhiteListService;
import com.facishare.open.autoreplymsg.result.GetCustomServiceSwitchResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.service.TransI18nService;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.common.utils.EncodingAesKeyUtil;
import com.facishare.open.material.api.enums.ArticleCategorySwitchEnum;
import com.facishare.open.material.api.enums.PermissionEnum;
import com.facishare.open.material.api.enums.ServiceCommentSwitchEnum;
import com.facishare.open.material.api.model.vo.MaterialAccessPermissionVO;
import com.facishare.open.material.api.service.ArticleCategorySwitchService;
import com.facishare.open.material.api.service.MaterialAccessPermissionService;
import com.facishare.open.material.api.service.ServiceCommentSwitchService;
import com.facishare.open.msg.model.DeleteSessionSingleEaVO;
import com.facishare.open.msg.service.MsgSessionService;
import com.facishare.open.oauth.model.AppDO;
import com.facishare.open.oauth.model.AppServiceRefDO;
import com.facishare.open.oauth.model.arg.AppArg;
import com.facishare.open.oauth.model.enums.AccessTypeEnum;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.oauth.model.enums.CustomAppDevStatus;
import com.facishare.open.oauth.result.*;
import com.facishare.open.oauth.service.*;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.facishare.open.support.model.ArticleVO;
import com.facishare.open.support.service.ArticleService;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.GetPaaSAppInfoArg;
import com.facishare.webpage.customer.api.model.arg.SavePaasAppArg;
import com.facishare.webpage.customer.api.model.result.GetPaaSAppInfoResult;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.fxiaoke.api.model.ClearCacheArg;
import com.fxiaoke.api.service.ClientDataRestService;
import com.fxiaoke.enterpriserelation.arg.BatchRemoveCustomerServiceAuthArg;
import com.fxiaoke.enterpriserelation.common.HeaderObj;
import com.fxiaoke.enterpriserelation.service.CustomerServiceService;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.awt.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2015年9月14日
 */
@Service
public class AppManagerImpl implements AppManager {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    //CRM角色设置URL
    private static final String CRM_ROLE_SETTING_URL = "#crm/setting/rolemanage";

    //是否显示老工单的灰度
    private static final String CAN_SHOW_OLD_WORK_ORDER_MODEL_KEY = "canShowOldWorkOrder";

    //app自定义缓存前缀
    public static final String paasAppViewsForEaVoPreKey = "PaasAppViewsForEaVo";
    @Value("${fs.open.app.center.app.app.target.url}")
    private String appTargetUrl;
    @Value("${fs.open.app.center.app.authorize.url}")
    private String authorizeUrl;
    @Value("${fs.open.app.center.app.trial.url}")
    private String trialUrl;
    @Value("${fs.open.app.center.app.notifyAdmin.url}")
    private String notifyAdminUrl;
    @Autowired
    private AppService appService;
    @Autowired
    private CustomAppService customAppService;
    @Autowired
    private CorpService corpService;
    @Autowired
    private OpenAppService openAppService;
    @Autowired
    private OpenFsUserBindAppService openFsUserBindAppService;
    @Autowired
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Autowired
    private AppIconService appIconService;
    @Autowired
    private OpenAppComponentService openAppComponentService;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private OpenAppScopeOrderService openAppScopeOrderService;
    @Autowired
    private QuotaService quotaService;
    @Autowired
    private TryStatusService tryStatusService;
    @Autowired
    private WebAuthManager webAuthManager;
    @Autowired
    private DictManager dictManager;
    @Autowired
    private EaAuthService eaAuthService;
    @Autowired
    private CustomMenuManager customMenuManager;
    @Autowired
    private ServiceCommentSwitchService serviceCommentSwitchService;
    @Autowired
    private ArticleCategorySwitchService articleCategorySwitchService;
    @Autowired
    private MsgAutoReplyService msgAutoReplyService;
    @Autowired
    private EnterprisePayService enterprisePayService;
    @Autowired
    private AppOnOffService appOnOffService;
    @Resource
    private AppBindManager appBindManager;
    @Resource
    private AppIpWhiteListService appIpWhiteListService;
    @Resource
    private LinkServiceManager linkServiceManager;
    @Value("${fs.open.app.center.app.fs.account}")
    private Long fsOpenDevId;
    @Value("${fs.open.app.center.image.url}")
    private String imageUrl;
    @Resource
    private TempFileToFormalFile tempFileToFormalFile;
    @Resource
    private AppCreateTemplateManager appCreateTemplateManager;
    @Resource
    private OpenComponentLoginUrlService openComponentLoginUrlService;
    @Resource
    private ServiceNumberManager serviceNumberManager;
    @Resource
    private MsgSessionService msgSessionService;
    @Resource
    private ArticleService articleService;
    @Resource
    private MaterialAccessPermissionService materialAccessPermissionService;
    @Resource
    private ServiceDashboardStatisticsService serviceDashboardStatisticsService;
    @Resource
    private ServicePromotionManager servicePromotionManager;
    @Resource
    private AppServiceRefService appServiceRefService;
    @Resource
    private AppMessageToMqManager appMessageToMqManager;
    @Resource
    private ServiceManager serviceManager;
    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;
    @Autowired
    private CustomerServiceService customerServiceService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private OperationLogService operationLogService;
    @Resource
    private EserviceManager eserviceManager;
    @Resource
    private PaaSAppRestService paaSAppRestService;
    @Resource
    private ClientDataRestService clientDataRestService;

    @Resource
    private TransI18nService transI18nService;

    /**
     * 已添加的状态.
     */
    private final ImmutableSet<TryStatusEnum> APP_ADDED_STATUS = ImmutableSet.of(
            TryStatusEnum.ADMIN_ADDED, TryStatusEnum.ADMIN_TRYING, TryStatusEnum.BOUGHT, TryStatusEnum.EMPLOYEE_TRYING);

    /**
     * 已到期的状态.
     */
    private final ImmutableSet<TryStatusEnum> EXPIRED_STATUS_ENUMS = ImmutableSet.of(
            TryStatusEnum.PURCHASE_EXPIRED, TryStatusEnum.ENTERPRISE_TRIAL_EXPIRED, TryStatusEnum.EMPLOYEE_TRIAL_EXPIRED,
            TryStatusEnum.EMPLOYEE_DENIED_BY_ENTERPRISE_EXPIRED
    );

    @Override
    public boolean checkAppName(String appId, String newAppName) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        // 只有自定义类型可以保存数据
        if (!(AppType.CUSTOM_APP.value() == appResult.getResult().getAppType()
                || AppType.SERVICE.value() == appResult.getResult().getAppType()
                || AppType.LINK_SERVICE.value() == appResult.getResult().getAppType()
                || AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用类型不正确"); // ignoreI18n
        }

        // 名称重名验证
        OpenAppDO currentEntity = appResult.getResult();
        if (!currentEntity.getAppName().equals(newAppName)) {
            AppType appType = AppType.CUSTOM_APP;
            if (AppType.SERVICE.value() == currentEntity.getAppType()) {
                appType = AppType.SERVICE;
            } else if (AppType.OUT_SERVICE_APP.value() == currentEntity.getAppType()) {
                appType = AppType.OUT_SERVICE_APP;
            }
            return !this.existsAppName(newAppName, currentEntity.getAppCreater(), appType);
        }

        return true;

    }

    @Override
    public boolean existsAppName(String appName, String fsEa, AppType appType) {
        BaseResult<Boolean> result = openAppService.existsAppName(appName, fsEa, appType);
        if (!result.isSuccess()) {
            logger.warn("call openAppService.checkAppName fail appName [{}], fsEa[{}], appType[{}], appResult[{}]",
                    appName, fsEa, appType, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "验证是否存在同名应用失败"); // ignoreI18n
        }
        return result.getResult();
    }

    @Override
    public void createCustomApp(FsUserVO fsAdminUser, AppCreateForm form, IconFileVO iconFileVO, AppType appType) {
        //是否可以创建服务号
        if (appType.equals(AppType.SERVICE)) {
            AbleCreateServiceVO ableCreateServiceVO = isAbleToCreateService(fsAdminUser, AppType.SERVICE);
            if (!ableCreateServiceVO.getAbleCreateService()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, ableCreateServiceVO.getUnableMsg());
            }
        } else if (appType.equals(AppType.LINK_SERVICE)) {
            AbleCreateServiceVO ableCreateServiceVO = isAbleToCreateService(fsAdminUser, AppType.LINK_SERVICE);
            if (!ableCreateServiceVO.getAbleCreateService()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, ableCreateServiceVO.getUnableMsg());
            }
        }

        // 创建应用(oauth)
        AppArg appArg = new AppArg();
        appArg.setAppType(AppTypeEnum.CUSTOM_APP);
        if (AppType.SERVICE == appType) {
            appArg.setAppType(AppTypeEnum.SERVICE_APP);
        } else if (AppType.LINK_SERVICE == appType) {
            appArg.setAppType(AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE);
        }
        appArg.setEncodingAesKey(EncodingAesKeyUtil.generateEncodingAesKey());
        CustomAppDevStatus devStatus = CustomAppDevStatus.DISABLED;
        CreateAppResult appResult = customAppService.createCustomApp(appArg, devStatus);
        if (!appResult.isSuccess()) {
            logger.warn("call oauth createApp fail appArg [{}], devStatus[{}], appResult[{}]", appArg, devStatus, appResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "添加应用失败"); // ignoreI18n
        }
        String appId = appResult.getAppId();
        form.setAppId(appId);
        // 保存应用图片
        if (StringUtils.isNotBlank(appId) && null != iconFileVO) {
            processIcon(appId, iconFileVO);
        } else if (form.getAppLogo().startsWith("TN_")) {
            processIconByLogo(appId, form.getAppLogo(), fsAdminUser.getEnterpriseAccount(), fsAdminUser.getUserId(), form);
        } else if (StringUtils.isNotBlank(form.getAppLogo())) {
            processIconByLogo(appId, form.getAppLogo());
        }
        OpenAppDO app = new OpenAppDO();
        app.setAppId(appId);
//        app.setAppName(StringEscapeUtils.escapeHtml4(form.getAppName()));
//        app.setAppDesc(StringEscapeUtils.escapeHtml4(form.getAppDesc()));
        // 防xss在前端显示时处理.java不处理.add by lambo@********.
        app.setAppName(form.getAppName());
        app.setAppDesc(form.getAppDesc());
        // 设置企业服务号
        app.setServiceName(app.getAppName());
        app.setCreaterType(CommonConstant.APP_CREATER_FS_ACCOUNT);
        app.setAppCreater(fsAdminUser.getEnterpriseAccount());
        app.setStatus(AppStatus.ON_LINE.status());
        app.setAppType(appType.value());
        app.setAppMode(CommonConstant.APP_MODE_COMMON);
        app.setAppClass(CommonConstant.APP_CLASS_CUSTOM);
        app.setPayType(PayTypeEnum.FREE.getPayType());
        app.setGmtCreate(new Date());
        app.setGmtModified(new Date());
        if (StringUtils.isNotBlank(form.getTemplateId())) {
            OpenAppProperties properties = new OpenAppProperties();
            properties.setCreateTemplateId(form.getTemplateId());
            app.setProperties(JsonKit.object2json(properties));
        }

        // 创建应用(center) && 创建企信的session头像
        AppResult createResult = openAppService.createOpenApp(app);
        if (!createResult.isSuccess()) {
            logger.warn("create custom app by appcenter fail, result:{}", createResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, createResult, "创建自定义应用失败"); // ignoreI18n
        }

        // 保存应用管理员
        List<String> newAppAdminIds = new ArrayList<>();
        for (Integer userId : form.getAppAdmins()) {
            newAppAdminIds.add(new FsUserVO(fsAdminUser.getEnterpriseAccount(), userId).asStringUser());
        }

        BaseResult<Void> adminResult = openAppAdminService.updateAppAdminIds(fsAdminUser, appId, newAppAdminIds);
        if (!adminResult.isSuccess()) {
            if (adminResult.getErrCode() == AppCenterCodeEnum.QUOTA_INSUFFICIENT.getErrCode()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, adminResult, "可见范围的人数超过了企业购买的数量，请重新选择！"); // ignoreI18n
            } else {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, adminResult, "保存应用管理员失败"); // ignoreI18n
            }
        }

        // 添加组件 && 为组件的可见范围默认设置为应用管理员
        if (!CollectionUtils.isEmpty(form.getAppComponents())) {
            for (CustomComponentVO customComponentVO : form.getAppComponents()) {
                customComponentVO.setAppId(appId);
                createCustomComponent(fsAdminUser, customComponentVO);
            }
        }

        // 添加自定义应用的权限
        List<OpenAppScopeOrderDO> scopeOrderDOList = new ArrayList<>();

        List<String> customAppScopes = Arrays.asList(ConfigCenter.CUSTOM_APP_SCOPES.split(","));

        List<String> customServiceScopes = Arrays.asList(ConfigCenter.CUSTOM_SERVICE_SCOPES.split(","));
        //自建应用和自建服务号：使用不同的权限
        if (appType.equals(AppType.CUSTOM_APP)) {
            List<OpenAppScopeOrderDO> openAppScopeOrderDOs = customAppScopes.stream()
                    .map(customAppScope -> new OpenAppScopeOrderDO(appId, customAppScope, "自定义应用权限")).collect(Collectors.toList()); // ignoreI18n
            scopeOrderDOList.addAll(openAppScopeOrderDOs);
        } else if (appType.equals(AppType.SERVICE) || appType.equals(AppType.LINK_SERVICE)) {
            List<OpenAppScopeOrderDO> openServiceScopeOrderDOs = customServiceScopes.stream()
                    .map(customServiceScope -> new OpenAppScopeOrderDO(appId, customServiceScope, "自定义服务号权限")).collect(Collectors.toList()); // ignoreI18n
            scopeOrderDOList.addAll(openServiceScopeOrderDOs);
        }
        StatusResult result = openAppScopeOrderService.saveAppScopes(appId, scopeOrderDOList);
        if (!result.isSuccess()) {
            logger.error("save app scope error , appId [{}] scopes[{}]", appId, ToStringBuilder.reflectionToString(scopeOrderDOList));
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "保存权限失败"); // ignoreI18n
        }

        // 企业对应用授权（管理员可见应用 && 应用可访问纷享企业的scope组）
        List<String> scopeGroup = new ArrayList<>();
        //自建应用和自建服务号：使用不同的权限
        if (appType.equals(AppType.CUSTOM_APP)) {
            scopeGroup.addAll(customAppScopes);
        } else if (appType.equals(AppType.SERVICE) || appType.equals(AppType.LINK_SERVICE)) {
            scopeGroup.addAll(customServiceScopes);
        }
        CommonResult saveResult = eaAuthService.saveEaAuth(fsAdminUser.asStringUser(), null,
                fsAdminUser.getEnterpriseAccount(), appId, scopeGroup);

        if (!saveResult.isSuccess()) {
            logger.error("eaAuthService.saveEaAuth fail , result:{}", saveResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, saveResult, saveResult.getErrDescription());
        }
        if (GraySwitch.isAllowForEa(GraySwitch.clearAppCache, fsAdminUser.getEnterpriseAccount())) {
            clearCache(fsAdminUser);
        }
        serviceManager.updateSessionNameLogoDesc(fsAdminUser, appId, null, null, null);
    }

    @Override
    public void isAbleToCreateCustom(FsUserVO fsAdminUser) {
        com.facishare.open.oauth.result.IntegerResult customAppCountResult = customAppService.getCustomAppCountByFsEa(
                fsAdminUser.getEnterpriseAccount());

        if (!customAppCountResult.isSuccess()) {
            logger.error("customAppService.getCustomAppCountByFsEa ea={}, IntegerResult={}", fsAdminUser
                    .getEnterpriseAccount(), customAppCountResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, customAppCountResult, "添加应用失败,系统错误请稍后重试"); // ignoreI18n
        }

        com.facishare.open.common.result.BaseResult<AppPayEnum> payResult = enterprisePayService.canCreateCustomApp
                (fsAdminUser.getEnterpriseAccount());
        if (!payResult.isSuccess()) {
            logger.error("enterprisePayService.canCreateCustomApp, ea={},BaseResult={}",
                    fsAdminUser.getEnterpriseAccount(), payResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, customAppCountResult, "添加应用失败,系统错误请稍后重试"); // ignoreI18n
        }
        //超过默认个数且未付费购买
        if (customAppCountResult.getIntegerValue() >= ConfigCenter.CUSTOM_APP_LIMIT && payResult.getResult().getValue().equals(AppPayEnum
                .UNPURCHASED.getValue())) {
            logger.warn("customAppService.getCustomAppCountByFsEa, custom app more than {}. ea={}, IntegerResult={}",
                    ConfigCenter.CUSTOM_APP_LIMIT, fsAdminUser.getEnterpriseAccount(), customAppCountResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, ConfigCenter.CUSTOM_APP_NOTIFY_UNPURCHASED);
        }
        //超过默认个数且购买已过期
        if (customAppCountResult.getIntegerValue() >= ConfigCenter.CUSTOM_APP_LIMIT && payResult.getResult().getValue().equals(AppPayEnum
                .EXPIRED.getValue())) {
            logger.warn("customAppService.getCustomAppCountByFsEa, custom app more than {}. ea={}, IntegerResult={}",
                    ConfigCenter.CUSTOM_APP_LIMIT, fsAdminUser.getEnterpriseAccount(), customAppCountResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, ConfigCenter.CUSTOM_APP_NOTIFY_EXPIRED);
        }
        //超过最大个数
        if (customAppCountResult.getIntegerValue() >= ConfigCenter.CUSTOM_APP_MAX) {
            logger.warn("customAppService.getCustomAppCountByFsEa, custom app more than max {} ea={}, IntegerResult={}",
                    ConfigCenter.CUSTOM_APP_MAX, fsAdminUser.getEnterpriseAccount(), customAppCountResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, String.format("自定义应用不能超过最大上限%s个", // ignoreI18n
                    ConfigCenter.CUSTOM_APP_MAX));
        }
    }

    @Override
    public AbleCreateServiceVO isAbleToCreateService(FsUserVO fsAdminUser, AppType appType) {
        AbleCreateServiceVO ableCreateServiceVO = new AbleCreateServiceVO();
        ableCreateServiceVO.setAbleCreateService(Boolean.TRUE);
        long currentServiceCount;

        if (AppType.LINK_SERVICE.equals(appType)) {
            Pager<OpenAppDO> appDoPager = appBindManager.queryServiceListByAdmin(fsAdminUser, 1, 10,
                    true, true, AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE.getValue());
            currentServiceCount = appDoPager.getRecordSize();
            if (currentServiceCount >= ConfigCenter.LINK_SERVICE_MAX_NUMBER) {
                ableCreateServiceVO.setAbleCreateService(Boolean.FALSE);
                ableCreateServiceVO.setUnableMsg(String.format("互联服务号不能超过最大上限%s个", // ignoreI18n
                        ConfigCenter.LINK_SERVICE_MAX_NUMBER));
            }
        } else {

            // 从Oauth查询企业当前服务号数
            com.facishare.open.oauth.result.AppListResult appListResult =
                    appService.getAppInfoBatch(fsAdminUser.getEnterpriseAccount(), AppTypeEnum.SERVICE_APP, null);
            if (!appListResult.isSuccess()) {
                logger.error("failed to call appService.getAppInfoBatch fsAdminUser[{}], appListResult=[{}]",
                        fsAdminUser, appListResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, appListResult, "从Oauth查询企业当前服务号数失败"); // ignoreI18n
            }
            List<AppDO> appDOList = appListResult.getAppList();
            currentServiceCount = (appDOList == null) ? 0 : appDOList.size();
            logger.info("isAbleToCreateService, ea[{}], currentServiceCount[{}]", fsAdminUser.getEnterpriseAccount(), currentServiceCount);


            // 复用汇聚那边已经自建应用的购买
            com.facishare.open.common.result.BaseResult<AppPayEnum> payResult = enterprisePayService.canCreateCustomApp
                    (fsAdminUser.getEnterpriseAccount());
            if (!payResult.isSuccess()) {
                logger.error("enterprisePayService.canCreateCustomApp, ea={},BaseResult={}",
                        fsAdminUser.getEnterpriseAccount(), payResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, payResult, "获取企业应用购买状态失败"); // ignoreI18n
            }
            //超过默认个数且未付费购买
            if (currentServiceCount >= ConfigCenter.CUSTOM_APP_LIMIT && payResult.getResult().getValue().equals(AppPayEnum
                    .UNPURCHASED.getValue())) {
                ableCreateServiceVO.setAbleCreateService(Boolean.FALSE);
                ableCreateServiceVO.setUnableMsg(ConfigCenter.CUSTOM_APP_NOTIFY_UNPURCHASED);
            }
            //超过默认个数且购买已过期
            if (currentServiceCount >= ConfigCenter.CUSTOM_APP_LIMIT && payResult.getResult().getValue().equals(AppPayEnum
                    .EXPIRED.getValue())) {
                ableCreateServiceVO.setAbleCreateService(Boolean.FALSE);
                ableCreateServiceVO.setUnableMsg(ConfigCenter.CUSTOM_APP_NOTIFY_EXPIRED);
            }
            //超过最大个数
            if (currentServiceCount >= ConfigCenter.CUSTOM_APP_MAX) {
                ableCreateServiceVO.setAbleCreateService(Boolean.FALSE);
                ableCreateServiceVO.setUnableMsg(String.format("服务号不能超过最大上限%s个", // ignoreI18n
                        ConfigCenter.CUSTOM_APP_MAX));
            }
        }
        return ableCreateServiceVO;
    }

    @Override
    public void updateApp(FsUserVO fsAdminUser, OpenAppDO entity) {
        AppResult appResult = openAppService.loadOpenAppFast(entity.getAppId());
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        // 只有自定义类型可以保存数据
        if (!(AppType.CUSTOM_APP.value() == appResult.getResult().getAppType()
                || AppType.SERVICE.value() == appResult.getResult().getAppType()
                || AppType.LINK_SERVICE.value() == appResult.getResult().getAppType()
                || AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用类型不正确"); // ignoreI18n
        }
        // TODO: 2016/7/12 后台删除掉，应用类型还是不需要修改的。add by lambo .to lambo。
        entity.setAppType(appResult.getResult().getAppType());
        entity.setCreaterType(CommonConstant.APP_CREATER_FS_ACCOUNT);
        entity.setAppCreater(fsAdminUser.getEnterpriseAccount());
        AppResult createResult = openAppService.updateOpenApp(entity);
        if (!createResult.isSuccess()) {
            logger.warn("create custom app error , result:{}", createResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, createResult, createResult.getErrDescription());
        }

    }

    @Override
    public void updateCustomAppLogo(FsUserVO fsAdminUser, String appId, IconFileVO iconFileVO) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        // 只有自定义类型可以保存数据
        if (!(AppType.CUSTOM_APP.value() == appResult.getResult().getAppType()
                || AppType.SERVICE.value() == appResult.getResult().getAppType()
                || AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType()
                || AppType.LINK_SERVICE.value() == appResult.getResult().getAppType())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用类型不正确"); // ignoreI18n
        }
        processIcon(appId, iconFileVO);
        //创建服务号，默认会创建一个组件，对应修改服务号Logo需要同步更新组件的logo
        if (AppType.SERVICE.value() == appResult.getResult().getAppType() || AppType.LINK_SERVICE.value() == appResult.getResult().getAppType()) {
            com.facishare.open.app.center.api.result.BaseResult<List<OpenAppComponentDO>> result = openAppComponentService.queryAppComponentListByAppId(fsAdminUser, appId);
            if (!result.isSuccess()) {
                logger.warn("queryAppComponentListByAppId failed,appId[{}], result[{}]", appId, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "获取组件失败"); // ignoreI18n
            }
            if (!CollectionUtils.isEmpty(result.getResult())) {
                result.getResult().forEach(componentDo -> {
                    processIcon(componentDo.getComponentId(), iconFileVO);
                });
            }
        }
        //采用新的通知机制，下面代码可以去掉
//        OpenAppDO appDO = new OpenAppDO();
//        appDO.setAppId(appResult.getResult().getAppId());
//        appDO.setServiceName(appResult.getResult().getServiceName());
//        AppResult updateAppResult = openAppService.updateOpenApp(appDO);
//        if (!updateAppResult.isSuccess()) {
//            throw new BizException(AjaxCode.BIZ_EXCEPTION, updateAppResult, "通知终端更新应用头像");
//        }
    }


    @Override
    public void updateCustomAppLogo(FsUserVO fsAdminUser, String appId, String appLogo) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess() || null == appResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        // 只有自定义类型可以保存数据
        if (!(AppType.CUSTOM_APP.value() == appResult.getResult().getAppType()
                || AppType.SERVICE.value() == appResult.getResult().getAppType()
                || AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType()
                || AppType.LINK_SERVICE.value() == appResult.getResult().getAppType())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用类型不正确"); // ignoreI18n
        }
        processIconByLogo(appId, appLogo, fsAdminUser.getEa(), fsAdminUser.getUserId());
        //创建服务号，默认会创建一个组件，对应修改服务号Logo需要同步更新组件的logo
        if (AppType.SERVICE.value() == appResult.getResult().getAppType() || AppType.LINK_SERVICE.value() == appResult.getResult().getAppType()) {
            com.facishare.open.app.center.api.result.BaseResult<List<OpenAppComponentDO>> result = openAppComponentService.queryAppComponentListByAppId(fsAdminUser, appId);
            if (!result.isSuccess()) {
                logger.warn("queryAppComponentListByAppId failed,appId[{}], result[{}]", appId, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "获取组件失败"); // ignoreI18n
            }
            if (!CollectionUtils.isEmpty(result.getResult())) {
                result.getResult().forEach(componentDo -> {
                    processIconByLogo(componentDo.getComponentId(), appLogo, fsAdminUser.getEa(), fsAdminUser.getUserId());
                });
            }
        }

    }

    @Override
    public void updateComponentView(FsUserVO opUser, String componentId, AppViewDO viewDo) {
        //加载组件信息
        BaseResult<OpenAppComponentDO> componentResult = openAppComponentService.loadOpenAppComponentById(opUser,
                componentId);
        if (!componentResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, componentResult, "组件不存在"); // ignoreI18n
        }
        OpenAppComponentDO componentDO = componentResult.getResult();
        BaseResult<List<String>> adminList = openAppAdminService.queryAppAdminIdList(opUser.getEnterpriseAccount(),
                componentDO.getAppId());
        //用户不在应用管理员列表中
        if (!adminList.isSuccess() || !adminList.getResult().contains(opUser.asStringUser())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "用户不是该应用的应用管理员"); // ignoreI18n
        }
        //添加到member中
        HashSet<Integer> userIds = new HashSet<>();

        if (null != viewDo.getMember()) {
            userIds.addAll(Arrays.asList(viewDo.getMember()));
        }
        viewDo.setMember(userIds.toArray(new Integer[userIds.size()]));

        AppComponentTypeEnum componentType = AppComponentTypeEnum.getByCode(componentDO.getComponentType());

        BaseResult<Void> saveResult = openFsUserAppViewService.saveFsUserAppViewList(opUser, componentId,
                componentType, viewDo);
        if (!saveResult.isSuccess()) {
            if (saveResult.getErrCode() == AppCenterCodeEnum.QUOTA_INSUFFICIENT.getErrCode()) {
                throw new BizException(AppCenterCodeEnum.QUOTA_INSUFFICIENT.getErrCode(), saveResult, "配额不足"); // ignoreI18n
            } else {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, saveResult, "保存应用可见失败"); // ignoreI18n
            }
        }
    }

    @Override
    public void updateCustomAppAdmins(FsUserVO fsAdminUser, String appId, Integer[] appAdmins) {
        //转成这样的形式："E." + fsUserVO.getEnterpriseAccount() + "." + fsUserVO.getUserId();
        List<String> newAppAdminIds = new ArrayList<String>();
        for (Integer userId : appAdmins) {
            newAppAdminIds.add(new FsUserVO(fsAdminUser.getEnterpriseAccount(), userId).asStringUser());
        }

        //需要为新/删除的应用管理员增加/删除可见范围。实现逻辑如下：
        //1. 获取当前应用管理员，是否有新增/删除。否，则返回
        //2. 获取当前应用所有的组件的可见范围，应用上述新增/删除，再保存回新的可见范围

        //与当前应用管理对比，获取需要新增/删除的管理员
        List<String> appAdminIds = this.queryAppAdminIdList(fsAdminUser.getEa(), appId);
        if (!hasChangedAppAdmin(fsAdminUser.getEa(), appId, appAdmins)) {
            return;
        }

        //计算需要删除的应用管理员列表 = 数据库中应用管理员-页面上面当前的应用管理员
        HashSet<Integer> needDeleteSet = new HashSet<>();
        appAdminIds.forEach(userString -> {
            needDeleteSet.add(new FsUserVO(userString).getUserId());
        });
        needDeleteSet.removeAll(Arrays.asList(appAdmins));

        //2.计算需要增加的应用管理员
        HashSet<Integer> needAddSet = new HashSet<>();
        //页面上面当前的应用管理员
        needAddSet.addAll(Arrays.asList(appAdmins));
        //页面上面当前的应用管理员 - 数据库中应用管理员
        HashSet<Integer> dbAppAdminsSet = appAdminIds.stream().map(s -> new FsUserVO(s).getUserId()).collect(Collectors.toCollection(HashSet::new));
        needAddSet.removeAll(dbAppAdminsSet);//得到当前需要增加的应用管理员

        //应用管理员和可见范围不再有联系，删除以下代码。modified by albert 2016-2-24

        //根据上述得到的新增/删除的员工id，修改员工级可见范围。
        // 查出应用的组件
//        BaseResult<List<OpenAppComponentDO>> componentDOsResult = openAppComponentService.queryAppComponentListByAppId(
//                fsAdminUser, appId);
//        if (!componentDOsResult.isSuccess()) {
//            logger.error("failed to call openAppComponentService.queryAppComponentListByAppId: user={}, appId={}, result={}",
//                    fsAdminUser, appId, componentDOsResult);
//            throw new BizException(AjaxCode.BIZ_EXCEPTION, componentDOsResult, "获取应用的组件列表错误");
//        }
//        componentDOsResult.getResult().forEach(componentDO -> {
//
//            final AppComponentTypeEnum appComponentType = AppComponentTypeEnum.getByCode(componentDO.getComponentType());
//            // 修改组件的可见范围
//            BaseResult<Void> saveViewResult = openFsUserAppViewService
//                    .saveOnlyUserComponentViewList(fsAdminUser, componentDO.getComponentId(), appComponentType,
//                            new ArrayList<>(needAddSet), new ArrayList<>(needDeleteSet));
//
//            if (!saveViewResult.isSuccess()) {
//                throw new BizException(AjaxCode.BIZ_EXCEPTION, saveViewResult, "添加管理员到组件可见范围失败");
//            }
//        });

        //更新企业中指定应用的应用管理员(多删少加)
        BaseResult<Void> result = openAppAdminService.updateAppAdminIds(fsAdminUser, appId, newAppAdminIds);
        if (!result.isSuccess()) {
            if (result.getErrCode() == AppCenterCodeEnum.QUOTA_INSUFFICIENT.getErrCode()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "可见范围的人数超过了企业购买的数量，请重新选择！"); // ignoreI18n
            } else {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "保存应用管理员失败"); // ignoreI18n
            }
        }
    }

    @Override
    public boolean hasChangedAppAdmin(String ea, String appId, Integer[] newAppAdminIds) {
        List<String> newAppAdminIdStrList = new ArrayList<>();
        for (Integer userId : newAppAdminIds) {
            newAppAdminIdStrList.add(new FsUserVO(ea, userId).asStringUser());
        }

        List<String> appAdminIds = queryAppAdminIdList(ea, appId);
        return hasChangedAppAdmin(appAdminIds, newAppAdminIdStrList);
    }

    private boolean hasChangedAppAdmin(List<String> currentAppAdminIds, List<String> newAppAdminIds) {
        return !(newAppAdminIds.containsAll(currentAppAdminIds) && currentAppAdminIds.containsAll(newAppAdminIds));
    }

    private List<String> queryAppAdminIdList(String ea, String appId) {
        BaseResult<List<String>> appAdminIdsResult = openAppAdminService.queryAppAdminIdList(
                ea, appId);
        if (!appAdminIdsResult.isSuccess()) {
            logger.warn("failed to call openAppAdminService.queryAppAdminIdList: fsEa={}, appId={}", ea, appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appAdminIdsResult, "获取应用的应用管理员列表错误"); // ignoreI18n
        }
        return appAdminIdsResult.getResult();
    }

    @Override
    public Pager<Map<String, Object>> loadAppList(FsUserVO user, int currentPage, int pageSize) {
        Pager<OpenAppDO> page = new Pager<OpenAppDO>();

        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.addParam("fsEnterpriseAccount", user.getEnterpriseAccount());
        page.addParam("fsAdminUser", user.asStringUser());
        // 1.获取所有应用（排除已授权的应用）
        AppPagerResult pageResult = openAppService.queryUnBindOpenAppByType(page);
        if (!pageResult.isSuccess()) {
            logger.warn("failed to queryUnBindOpenAppByType, user={}, page={}, result={}", user, page, pageResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取更多应用错误"); // ignoreI18n
        }
        Pager<OpenAppDO> pp = pageResult.getResult();
        Pager<Map<String, Object>> mapResult = new Pager<Map<String, Object>>();
        mapResult.setCurrentPage(currentPage);
        mapResult.setPageSize(pageSize);
        mapResult.setRecordSize(pp.getRecordSize());
        List<Map<String, Object>> lst = new ArrayList<Map<String, Object>>();
        if (null != pp.getData()) {
            lst = pp.getData().stream()
                    .map(this::toMap)
                    .collect(Collectors.toList());
        }
        mapResult.setData(lst);
        return mapResult;
    }

    @Override
    public Map<String, Object> loadServiceByAppId(FsUserVO user, String appId) {
        //获取应用信息以及组件id
        AppResult appResult = openAppService.loadOpenApp(appId);
        if (!appResult.isSuccess()) {
            logger.warn("failed to call loadOpenApp, user=[{}], appId={}, result={}",
                    user, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载单个应用详情错误"); // ignoreI18n
        }
        OpenAppDO openAppDO = appResult.getResult();

        //判断是否是系统管理员
        final boolean isFsAdmin = webAuthManager.isFsAdmin(user);
        //是否应用管理员
        final boolean isAppAdmin = webAuthManager.isAppAdmin(user, appId);
        boolean isLinkService = Objects.equals(openAppDO.getAppType(), AppType.LINK_SERVICE.value());
        boolean isWxService = Objects.equals(openAppDO.getAppType(), AppType.OUT_SERVICE_APP.value());
        if (isLinkService) {
            if (!isAppAdmin && !linkServiceManager.isUpstreamLinkAdmin(user)) {
                logger.warn("user is neither admin nor UpstreamLinkAdmin, user=[{}], appId={}", user, appId);
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        } else if (isWxService) {
            if (!isAppAdmin && !webAuthManager.isLinkAdmin(user)) {
                logger.warn("user is neither admin nor linkadmin, user=[{}], appId={}", user, appId);
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        } else {
            if (!isFsAdmin && !isAppAdmin) {
                logger.warn("user is neither admin nor app admin, user=[{}], appId={}", user, appId);
                throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
            }
        }

        //获取启用停用状态
        IntegerResult integerResult = openFsUserBindAppService.queryAppBindStatus(user, appId);
        if (integerResult.isSuccess()) {
            openAppDO.setBindStatus(integerResult.getResult());
        } else {
            logger.error("load app by appId error , user[{}], appId [{}], result [{}]", user, appId, integerResult);
        }

        Map<String, Object> ajaxDataMap = toMap(openAppDO);

        //企业管理员:如果是自定义开发模式则获取开发模式相关信息
        if (isFsAdmin && openAppDO.getAppMode() == AppModeEnum.DEV.getCode()) {
            AppDevModeInfo appDevModeInfo = getAppDevModeInfo(user, appId);
            ajaxDataMap.put("devModeInfo", appDevModeInfo);
        }

        //返回应用管理员id列表
        ajaxDataMap.put("appAdminIds", getAppAdminIds(user, appId));

        //获取应用所有组件
        BaseResult<List<OpenAppComponentDO>> openComponentsResult =
                openAppComponentService.queryAppComponentListByAppId(user, appId);
        if (!openComponentsResult.isSuccess()) {
            logger.warn("failed to call queryAppComponentListByAppId, user=[{}], appId={}, result={}",
                    user, appId, openComponentsResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载应用的组件列表错误"); // ignoreI18n
        }

        Map<String, Object> serviceComponent = new HashMap<>();
        //如果是应用管理员,还需要获取所有组件的可见范围
        //这里使用逐个轮询的方式,如果影响效率则要求提供一个批量接口
        //warn :这里的效率很低,不但因为这里轮询每个组件,而且每个组件还会去分别去获取企业级,部门级,员工级可见范围
        for (OpenAppComponentDO openComponentDO : openComponentsResult.getResult()) {
            final Integer typeCode = openComponentDO.getComponentType();
            final AppComponentTypeEnum componentType = AppComponentTypeEnum.getByCode(typeCode);
            // 服务号组件需提放到外层
            if (AppComponentTypeEnum.SERVICE.equals(componentType) || AppComponentTypeEnum.LINK_SERVICE.equals(componentType)) {
                final BaseResult<AppViewDO> appViewResult =
                        openFsUserAppViewService.loadAppViewByType(user, openComponentDO.getComponentId(),
                                componentType);
                if (!appViewResult.isSuccess()) {
                    logger.warn("failed to call loadAppViewByType, user=[{}], componentId={}, componentType={}",
                            user, openComponentDO.getComponentId(), componentType);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询组件可见范围失败"); // ignoreI18n
                }
                Map<String, Object> componentMap = toMap(openComponentDO);
                componentMap.put("view", appViewResult.getResult());
                serviceComponent = componentMap;
            }
        }

        ajaxDataMap.put("serviceComponent", serviceComponent);
        ajaxDataMap.put("isFsAdmin", isFsAdmin ? 1 : 0);
        if (openAppDO.getAppType() == AppType.LINK_SERVICE.value()) {
            boolean isUpstreamLinkAdmin = linkServiceManager.isUpstreamLinkAdmin(user);
            ajaxDataMap.put("isUpstreamLinkAdmin", isUpstreamLinkAdmin ? 1 : 0);
        }
        ajaxDataMap.put("isThisAppAdmin", isAppAdmin ? 1 : 0);
        OpenAppProperties openAppProperties = OpenAppProperties.fromJson(openAppDO.getProperties());
        ajaxDataMap.put("isOfficialApp", openAppProperties != null ? openAppProperties.getIsOfficialApp() : null);

        // 添加安全设置状态
        ajaxDataMap.put("materialAccessPermission", queryMaterialAccessPermission(user.getEnterpriseAccount(), appId));

        //添加应用自定义菜单状态 addByLambo.
        ajaxDataMap.put("customMenuStatus", customMenuManager.queryCustomMenuStatusByAppId(user, appId));

        //获取自动回复开关是否打开的状态
        String enterpriseAccount = user.getEnterpriseAccount();
        int replySwitchStatus = 0;
        try {
            replySwitchStatus = msgAutoReplyService.queryAutoReplySwitch(enterpriseAccount, appId);
            //获取多客服开关是否打开的状态
            GetCustomServiceSwitchResult customServiceSwitchResult = msgAutoReplyService.queryCustomServiceReplySwitch(enterpriseAccount, appId);
            if (!customServiceSwitchResult.isSuccess()) {
                logger.warn("failed to call queryCustomServiceReplySwitch, enterpriseAccount={}, appId={}, result={}",
                        enterpriseAccount, appId, customServiceSwitchResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询多客服开关失败"); // ignoreI18n
            }
            ajaxDataMap.put("serviceNumberStatus", customServiceSwitchResult.getReplySwitch() == 1 ? 1 : 0);
            ajaxDataMap.put("serviceNumberOFF", CommonConstant.SERVICE_NUMBER_ON);
        } catch (Exception e) {
            logger.warn("failed to call queryCustomServiceReplySwitch", e);
        }
        ajaxDataMap.put("replySwitchStatus", replySwitchStatus);

        // 增加应用授权信息(albert建议说查看详情中是不需要授权信息, 只需要在授权时提供, 确定后再定)
        ajaxDataMap.put("scope", getAppScope(appId));

        //老工单不用了
//        Integer workOrderStatus = serviceNumberManager.queryWorkOrderStatus(user, new ServiceNumberForm(appId));
//        ajaxDataMap.put("workOrderStatus", workOrderStatus); //工单开关

        Integer questionnaireStatus = serviceNumberManager.queryQuestionnaireStatus(user, new ServiceNumberForm(appId));
        ajaxDataMap.put("questionnaireStatus", questionnaireStatus); //问卷开关

        ajaxDataMap.put("serviceNumberSetStatus", 1); //移动客服设置开关

        return ajaxDataMap;
    }

    @Override
    public Map<String, Object> queryFeatureStatusByAppId(FsUserVO user, String appId) {
        //判断是否是系统管理员
        final boolean isFsAdmin = webAuthManager.isFsAdmin(user);
        //是否应用管理员
        final boolean isAppAdmin = webAuthManager.isAppAdmin(user, appId);
        if (!isFsAdmin && !isAppAdmin) {
            logger.warn("user is neither admin nor app admin, user=[{}], appId=[{}]", user, appId);
            throw new BizException(AjaxCode.NO_AUTHORITY, "用户不是管理员也不是该应用的应用管理员"); // ignoreI18n
        }

        Map<String, Object> ajaxDataMap = new HashMap<>();
        ajaxDataMap.put("isFsAdmin", isFsAdmin ? 1 : 0);
        ajaxDataMap.put("isThisAppAdmin", isAppAdmin ? 1 : 0);

        //获取App
        final AppResult appResult = openAppService.loadOpenApp(appId);
        if (!appResult.isSuccess()) {
            logger.warn("fail to load app, appId[{}], result[{}]", appId, appResult);
            throw new BizException(appResult);
        }
        ajaxDataMap.put("appName", appResult.getResult().getAppName());
        ajaxDataMap.put("appType", appResult.getResult().getAppType());

        //服务的状态信息
        List<ServiceFeatureVO> serviceFeatureVOs = new ArrayList<>();

        //添加应用自定义菜单状态.1.开启，2.关闭.
        int customMenuStatus = customMenuManager.queryCustomMenuStatusByAppId(user, appId);
        serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.CUSTOM_MENU.getType(), customMenuStatus));

        //获取自动回复开关是否打开的状态
        String enterpriseAccount = user.getEnterpriseAccount();
        int replySwitchStatus = 0;
        replySwitchStatus = msgAutoReplyService.queryAutoReplySwitch(enterpriseAccount, appId);
        replySwitchStatus = replySwitchStatus == 1 ? 1 : 2;
        serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.AUTO_REPLY.getType(), replySwitchStatus));

        //获取移动客服是否打开的状态（多客服就是移动客服）
        GetCustomServiceSwitchResult customServiceSwitchResult = msgAutoReplyService.queryCustomServiceReplySwitch(enterpriseAccount, appId);
        if (!customServiceSwitchResult.isSuccess()) {
            logger.warn("failed to call queryCustomServiceReplySwitch, enterpriseAccount[{}], appId[{}], result[{}]",
                    enterpriseAccount, appId, customServiceSwitchResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询多客服开关失败"); // ignoreI18n
        }
        int serviceNumberStatus = customServiceSwitchResult.getReplySwitch() == 1 ? 1 : 2;
        serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.CUSTOMER_SERVICE.getType(), serviceNumberStatus));

        //互联服务号1.0 无服务工单、问卷和文章分类
        if (!Objects.equals(AppType.LINK_SERVICE.value(), appResult.getResult().getAppType())) {

            if (ConfigCenter.grayToEserviceWorkOrder(user.getEa())) {

                boolean workOrderPaasStatus = eserviceManager.isOpenEserviceWorkOrder(user.getEa(), user.getUserId(), appId);
                serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.ESERVICE_WORK_ORDER.getType(), workOrderPaasStatus ? 1 : 2));

            }
            // 服务工单出现条件：  开启了服务工单, 关闭后不在出现
            Integer workOrderPaasStatus = serviceNumberManager.queryWorkOrderPaasStatus(user, new ServiceNumberForm(appId));
            if (workOrderPaasStatus == 1 || !ConfigCenter.grayToEserviceWorkOrder(user.getEa())) {
                serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.WORK_ORDER_PAAS.getType(), workOrderPaasStatus));
            }

            Integer questionnaireStatus = serviceNumberManager.queryQuestionnaireStatus(user, new ServiceNumberForm(appId));
            questionnaireStatus = questionnaireStatus == 1 ? 1 : 2;
            serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.QUESTIONNAIRE.getType(), questionnaireStatus));
        }

        //互联服务号才有：审批单    审批单已下线
//        if(Objects.equals(AppType.LINK_SERVICE.value(), appResult.getResult().getAppType())) {
//            Integer approvalStatus = serviceNumberManager.queryApprovalStatus(user, new ServiceNumberForm(appId));
//            approvalStatus = approvalStatus == 1 ? 1 : 2;
//            serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.APPROVAL.getType(), approvalStatus));
//        }

        //写评论的状态:1.开启，2.关闭.
        com.facishare.open.common.result.BaseResult<ServiceCommentSwitchEnum> commentSwitchVOBaseResult =
                serviceCommentSwitchService.queryCommentSwitchByAppId(user.getEnterpriseAccount(), appId);
        if (!commentSwitchVOBaseResult.isSuccess()) {
            logger.warn("failed call serviceCommentSwitchService.queryCommentSwitchByAppId, appId[{}], result[{}]", appId, commentSwitchVOBaseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取写评论开关状态失败"); // ignoreI18n
        }
        int writeCommentStatus = commentSwitchVOBaseResult.getResult().getCode();
        serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.ARTICLE_COMMENT.getType(), writeCommentStatus));

        //外联服务号1.0 无服务工单、问卷和文章分类
        if (!Objects.equals(AppType.LINK_SERVICE.value(), appResult.getResult().getAppType())) {
            //文章分类开关:1.开启，2.关闭.
            com.facishare.open.common.result.BaseResult<ArticleCategorySwitchEnum> categorySwitchVOBaseResult =
                    articleCategorySwitchService.queryCategorySwitchByFsEaAndAppId(user.getEnterpriseAccount(), appId);
            if (!categorySwitchVOBaseResult.isSuccess()) {
                logger.warn("failed call serviceCommentSwitchService.queryCategorySwitchByFsEaAndAppId,fsEa[{}], appId[{}], " +
                        "result[{}]", user.getEnterpriseAccount(), appId, categorySwitchVOBaseResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取文章分类开关失败"); // ignoreI18n
            }
            int categorySwitch = categorySwitchVOBaseResult.getResult().getCode();
            serviceFeatureVOs.add(new ServiceFeatureVO(ServiceFeatureTypeEnum.ARTICLE_CATEGORY.getType(), categorySwitch));
        }

        ajaxDataMap.put("serviceFeatures", serviceFeatureVOs);
        return ajaxDataMap;
    }

    private Integer queryMaterialAccessPermission(String fsEa, String appId) {
        try {
            com.facishare.open.common.result.BaseResult<Integer> result = materialAccessPermissionService.queryPermission(fsEa, appId);
            if (!result.isSuccess()) {
                logger.warn("failed call materialAccessPermissionService.queryPermission, fsEa[{}], appId[{}], result[{}]", fsEa, appId, result);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取素材安全设置状态失败"); // ignoreI18n
            }
            return result.getResult();
        } catch (Exception e) {
            return PermissionEnum.INNER.getCode();
        }
    }

    @Override
    public Map<String, Object> loadAppByAppId(FsUserVO user, String appId, String lang) {
        boolean isFsAdmin = webAuthManager.isFsAdmin(user);
        boolean isAppAdmin = webAuthManager.isAppAdmin(user, appId);
        boolean hasAppManageFunction = webAuthManager.hasAppManageFunction(user, appId);
        int ei = eieaConverter.enterpriseAccountToId(user.getEa());
        if (!isFsAdmin && !isAppAdmin && !hasAppManageFunction) {
            logger.warn("user is neither admin nor app admin, user=[{}], appId={}", user, appId);
            throw new BizException(AjaxCode.NO_AUTHORITY, "用户不是管理员也不是该应用的应用管理员"); // ignoreI18n
        }

        //获取应用信息以及组件id
        AppResult appResult = openAppService.loadOpenApp(appId);
        if (!appResult.isSuccess()) {
            logger.warn("failed to call loadOpenApp, user=[{}], appId={}, result={}",
                    user, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载单个应用详情错误"); // ignoreI18n
        }
        assert appResult.getResult().getAppId().equals(appId);

        //替换多语言
        OpenAppDO openAppDO = appResult.getResult();
        if (openAppDO != null) {
            openAppDO = I18NUtils.modifyOpenAppDOByLang(ei, Arrays.asList(openAppDO), lang).get(0);
            if (openAppDO.getOpenDevDO() != null) {
                openAppDO.setOpenDevDO(I18NUtils.modifyDevByLang(ei, Arrays.asList(openAppDO.getOpenDevDO()), lang).get(0));
            }
        }


        //获取应用所有组件
        BaseResult<List<OpenAppComponentDO>> openComponentsResult =
                openAppComponentService.queryAppComponentListByAppId(user, appId);
        if (!openComponentsResult.isSuccess()) {
            logger.warn("failed to call queryAppComponentListByAppId, user=[{}], appId={}, result={}",
                    user, appId, openComponentsResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载应用的组件列表错误"); // ignoreI18n
        }

        //替换多语言
        transEntryName(openComponentsResult, ei, lang);

        IntegerResult integerResult = openFsUserBindAppService.queryAppBindStatus(user, appId);
        if (integerResult.isSuccess()) {
            openAppDO.setBindStatus(integerResult.getResult());
        } else {
            logger.error("load app by appId error , user[" + user + "],appId [ " + appId + " ] result ["
                    + integerResult + "]");
        }

        Map<String, Object> ajaxDataMap = toMapYhSpecial(openAppDO);

        transAppName(ajaxDataMap, openAppDO, ei, lang);

        //企业管理员:如果是自定义开发模式则获取开发模式相关信息
        if (isFsAdmin && openAppDO.getAppMode() == AppModeEnum.DEV.getCode()) {
            AppDevModeInfo appDevModeInfo = getAppDevModeInfo(user, appId);
            ajaxDataMap.put("devModeInfo", appDevModeInfo);
        }

        //返回应用管理员id列表
        ajaxDataMap.put("appAdminIds", getAppAdminIds(user, appId));
        //add by zhouq @20160727 因为在getAppAdminIds(user, appId)方法中，会针对基础应用去设置系统管理员为基础应用的管理员,
        //所以需要再次判断下，解决第一次进入应用设置页面，组件无法显示修改按钮的Bug
        isAppAdmin = webAuthManager.isAppAdmin(user, appId);

        //获取组件跳转地址
        Map<String, String> componentLoginUrls = new HashMap<>();
        for (final OpenAppComponentDO componentDO : openComponentsResult.getResult()) {
            final com.facishare.open.oauth.result.AppResult componentInfo =
                    appService.getComponentInfo(null, null, componentDO.getComponentId());
            if (!componentInfo.isSuccess()) {
                logger.warn("failed to appService.getComponentInfo: componentId={}", componentDO.getComponentId());
            }
            if (componentDO.getComponentType() == AppComponentTypeEnum.WEB.getType()) {

                componentLoginUrls.put(componentDO.getComponentId(), componentInfo.getCallBackWebloginUrl());
            } else {
                componentLoginUrls.put(componentDO.getComponentId(), componentInfo.getCallBackloginUrl());
            }
        }

        List<Map<String, Object>> componentMapList = new ArrayList<>();
        //如果是应用管理员,还需要获取所有组件的可见范围
        //这里使用逐个轮询的方式,如果影响效率则要求提供一个批量接口
        //warn :这里的效率很低,不但因为这里轮询每个组件,而且每个组件还会去分别去获取企业级,部门级,员工级可见范围
        for (OpenAppComponentDO openComponentDO : openComponentsResult.getResult()) {
            final Integer typeCode = openComponentDO.getComponentType();
            final AppComponentTypeEnum componentType = AppComponentTypeEnum.getByCode(typeCode);
            Map<String, Object> componentMap = toMapYhSpecial(openComponentDO);
            //获取指定组件的适应范围
            GetPaaSAppInfoResult getPaaSAppInfoResult = new GetPaaSAppInfoResult();
            if (ConfigCenter.grayNewScopeApp(openComponentDO.getComponentId()) || GraySwitch.isAllowForEa(GraySwitch.useNewPaasAppGray, user.getEa())) {
                Map<String, String> header = new HashMap<>();
                header.put("x-fs-ei", Integer.toString(ei));
                GetPaaSAppInfoArg arg = new GetPaaSAppInfoArg();
                arg.setAppId(openComponentDO.getComponentId());
                arg.setTenantId(ei);
                getPaaSAppInfoResult = paaSAppRestService.getPaaSAppInfo(header, arg);
            }
            final BaseResult<AppViewDO> appViewResult =
                    openFsUserAppViewService.loadAppViewByType(user, openComponentDO.getComponentId(),
                            componentType);
            if (!appViewResult.isSuccess()) {
                logger.warn("failed to call loadAppViewByType, user=[{}], componentId={}, componentType={}",
                        user, openComponentDO.getComponentId(), componentType);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询组件可见范围失败"); // ignoreI18n
            }
            if (Objects.nonNull(getPaaSAppInfoResult.getPaaSAppVO())) {
                List<Scope> scopeList = getPaaSAppInfoResult.getPaaSAppVO().getScopeList();

                AppViewDO appViewDO = ScopeCenter.convertScopeToView(scopeList);
                componentMap.put("view", appViewDO);
            } else {
                componentMap.put("view", appViewResult.getResult());
            }
            componentMap.put("loginUrl", componentLoginUrls.get(openComponentDO.getComponentId()));

            // CRM应用增加已用配额
            if (Objects.equals(appId, ConfigCenter.CRM_APP_ID)) {
                int hasUsedQuote = 0;
                if (openComponentDO.getComponentType() == AppComponentTypeEnum.APP.getType()) {
                    if (Objects.nonNull(appViewResult.getResult()) && Objects.nonNull(appViewResult.getResult().getMember())) {
                        hasUsedQuote = appViewResult.getResult().getMember().length;
                    }
                }
                ajaxDataMap.put("hasUsedQuote", hasUsedQuote);
            }

            if (BizCommonUtils.getCrmAppId().equals(appId)) {
                componentMap.put("isViewReadonly", 1); //可见范围只读
                componentMap.put("viewSettingUrl", CRM_ROLE_SETTING_URL);
                componentMap.put("viewSettingDesc", ConfigCenter.CRM_VIEW_SETTING_DESC);
            } else {
                componentMap.put("isViewReadonly", 2); //可见范围不是只读
            }
            componentMapList.add(componentMap);
        }

        //新增排序，app组件在前面，web在后面
        componentMapList.sort((comp1, comp2) -> String.valueOf(comp1.get("componentType")).compareTo(String.valueOf(comp2.get("componentType"))));

        ajaxDataMap.put("components", componentMapList);
        ajaxDataMap.put("isFsAdmin", isFsAdmin ? 1 : 0);  // todo 接入管理后台后这个参数需去掉,前端也需要调整成使用hasAppManageFunction属性
        ajaxDataMap.put("hasAppManageFunction", hasAppManageFunction ? 1 : 0);
        ajaxDataMap.put("isThisAppAdmin", isAppAdmin ? 1 : 0);

        // 增加应用授权信息(albert建议说查看详情中是不需要授权信息, 只需要在授权时提供, 确定后再定)
        ajaxDataMap.put("scope", getAppScope(appId));

        int payType = Objects.isNull(openAppDO.getPayType()) ? PayTypeEnum.CHARGE.getPayType() : openAppDO.getPayType();
        ajaxDataMap.put("payType", payType);

        // 付费应用增加付费状态及配额信息
        if (Objects.equals(payType, PayTypeEnum.CHARGE.getPayType())) {
            //获取应用的付费信息, 比如"已购买, 可用20个用户, 2017.02.16到期"
            final com.facishare.open.common.result.BaseResult<QuotaVo> quotaVoResult =
                    quotaService.queryQuotaInfo(user.getEnterpriseAccount(), appId);

            if (!quotaVoResult.isSuccess()) {
                logger.warn("failed to call queryQuotaInfo, user=[{}], appId={}, result={}",
                        user, appId, quotaVoResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询应用配额信息失败"); // ignoreI18n
            }
            final Date realExpire = quotaVoResult.getResult().getExpireDateWithoutExtension();
            PayStatus payStatusForShow = quotaVoResult.getResult().getPayStatus();
            if (realExpire != null
                    && realExpire.getTime() < System.currentTimeMillis()) {
                if (payStatusForShow == PayStatus.ON_TRIAL) {
                    payStatusForShow = PayStatus.TRIAL_EXPIRED;
                } else if (payStatusForShow == PayStatus.PURCHASED) {
                    payStatusForShow = PayStatus.PURCHASE_EXPIRED;
                }
            }
            ajaxDataMap.put("payStatus", payStatusForShow.getCode());

            if (quotaVoResult.getResult().getPayStatus() != PayStatus.NEVER_USED) {
                ajaxDataMap.put("quota", quotaVoResult.getResult().getQuota());
                ajaxDataMap.put("quotaBeginTime", quotaVoResult.getResult().getBeginDate());
                ajaxDataMap.put("quotaEndTime", realExpire);
                ajaxDataMap.put("quotaEndTimeWithExt", quotaVoResult.getResult().getExpireDate());
            }
        }

        ajaxDataMap.put("appIntro", getAppIntro(openAppDO));

        //自建应用，查CRM接口授权
        if (Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.CUSTOM_APP.value())) {
            ajaxDataMap.put("isCrmAuth", isCrmAuth(user.getEa(), appId));
        }
        return ajaxDataMap;
    }

    private void transEntryName(BaseResult<List<OpenAppComponentDO>> openComponentsResult, int ei, String lang) {
        List<OpenAppComponentDO> openAppComponentDOS = I18NUtils.modifyComponentByLang(ei, openComponentsResult.getResult(), lang);
        List<String> keyList = openAppComponentDOS.stream().map(x -> com.facishare.open.common.utils.I18NUtils.getCustomAppEntryNameKey(x.getAppId(), x.getComponentId())).collect(Collectors.toList());
        Map<String, String> transValueMap = transI18nService.getTransValue(ei, keyList, lang);
        if (CollectionUtils.isEmpty(transValueMap)) {
            return;
        }
        for (OpenAppComponentDO openAppComponentDO : openAppComponentDOS) {
            String name = transValueMap.get(com.facishare.open.common.utils.I18NUtils.getCustomAppEntryNameKey(openAppComponentDO.getAppId(), openAppComponentDO.getComponentId()));
            if (StringUtils.isNotEmpty(name)) {
                openAppComponentDO.setComponentName(name);
            }
        }
        openComponentsResult.setResult(openAppComponentDOS);


    }

    private void transAppName(Map<String, Object> ajaxDataMap, OpenAppDO openAppDO, int ei, String lang) {
        String customAppNameKey = com.facishare.open.common.utils.I18NUtils.getCustomAppNameKey(openAppDO.getAppId());
        ajaxDataMap.put("appName", transI18nService.getCurrentI18nValue(ei, customAppNameKey, openAppDO.getAppName(), RequestContextManager.isFromManager(), lang, true));
    }

    /**
     * 查CRM接口授权
     */
    private boolean isCrmAuth(String ea, String appId) {
        String scope = "app-crm-mgr-group";
        com.facishare.open.oauth.result.AppListResult appListResult = appService.getAppIDListByScopeGroupAndEa(ea, scope);
        logger.info("appService.getAppIDListByScopeGroupAndEa, ea[{}], scope[{}], appListResult[{}]", ea, scope, appListResult);
        if (!appListResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询CRM接口授权失败"); // ignoreI18n
        }

        for (AppDO appDO : appListResult.getAppList()) {
            if (appDO.getAppId().equals(appId)) {
                return true;
            }
        }

        return false;
    }

    private String getAppIntro(OpenAppDO openAppDO) {
        String appIntro = null;
        OpenAppProperties openAppProperties = OpenAppProperties.fromJson(openAppDO.getProperties());
        if (Objects.nonNull(openAppProperties)) {
            appIntro = openAppProperties.getAppIntro();
        }
        return appIntro;
    }

    /**
     * 获取应用管理员ID列表
     *
     * @param user
     * @param appId
     * @return
     */
    private List<String> getAppAdminIds(FsUserVO user, String appId) {
        BaseResult<List<String>> openAppAdminIdsResult =
                openAppAdminService.queryAppAdminIdList(user.getEnterpriseAccount(), appId);
        if (!openAppAdminIdsResult.isSuccess()) {
            logger.warn("failed to call queryAppAdminIdList, user[{}], appId[{}], result[{}]",
                    user, appId, openAppAdminIdsResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "请求应用的管理员列表错误"); // ignoreI18n
        }
        return openAppAdminIdsResult.getResult().stream()
                .map(adminId -> String.valueOf(new FsUserVO(adminId).getUserId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取应用授权信息
     *
     * @param appId
     */
    private List<Map<String, Object>> getAppScope(String appId) {
        List<Map<String, Object>> scopeList = new ArrayList<>();
        OpenAppScopeOrderListResult appScopeOrderListResult = openAppScopeOrderService
                .getAppScopeByAppId(appId);
        if (!appScopeOrderListResult.isSuccess()) {
            logger.warn("fail to finish call openAppScopeOrderService.getAppScopeByAppId, appId[{}], result[{}]",
                    appId, appScopeOrderListResult);
            throw new BizException(appScopeOrderListResult);

        }
        List<OpenAppScopeOrderDO> scopeOrders = appScopeOrderListResult.getResult();
        if (!CollectionUtils.isEmpty(scopeOrders)) {
            scopeOrders.forEach(scopeOrder -> {
                Map<String, Object> scopeMap = new HashMap<>();
                scopeMap.put("scope", scopeOrder.getScope());
                scopeMap.put("scopeName", scopeOrder.getScopeName());
                scopeList.add(scopeMap);
            });
        }

        return scopeList;
    }

    @Override
    public Map<String, Object> queryAppScope(FsUserVO fsAdmin, String appId) {
        Map<String, Object> ajaxDataMap = new HashMap<>();

        List<Map<String, Object>> scopeList = getAppScope(appId);

        //获取应用信息以及组件id
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            logger.warn("failed to call loadOpenApp, user=[{}], appId={}, result={}",
                    fsAdmin, appId, appResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载单个应用详情错误"); // ignoreI18n
        }
        assert appResult.getResult().getAppId().equals(appId);
        ajaxDataMap.put("scope", scopeList);

        final OpenAppDO openAppDO = appResult.getResult();
        ajaxDataMap.put("appLogoUrl", queryAppIconUrl(appId, IconType.WEB));
        ajaxDataMap.put("appName", openAppDO.getAppName());

        return ajaxDataMap;
    }

    @Override
    public AppDevModeInfo getAppDevModeInfo(FsUserVO user, String appId) {
        final com.facishare.open.oauth.result.AppResult oauthAppResult =
                appService.getAppInfo(null, null, appId);
        if (!oauthAppResult.isSuccess()) {
            logger.warn("failed to call appService.getAppInfo: appId={}", appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取应用权限信息错误"); // ignoreI18n
        }
        AppDevModeInfo appDevModeInfo = new AppDevModeInfo();
        appDevModeInfo.setAppId(appId);
        appDevModeInfo.setAppSecret(oauthAppResult.getAppSecret());
        appDevModeInfo.setCallBackDomain(oauthAppResult.getCallBackDomain());
        appDevModeInfo.setCallBackMsgUrl(oauthAppResult.getCallBackMsgUrl());
        appDevModeInfo.setEncodingAesKey(oauthAppResult.getEncodingAesKey());
        appDevModeInfo.setToken(oauthAppResult.getToken());

        //如果token和aesKey为空,则自动生成一个
        if (StringUtils.isBlank(appDevModeInfo.getEncodingAesKey())) {
            appDevModeInfo.setEncodingAesKey(EncodingAesKeyUtil.generateEncodingAesKey());
        }
        if (StringUtils.isBlank(appDevModeInfo.getToken())) {
            appDevModeInfo.setToken(UUID.randomUUID().toString().replace("-", ""));
        }

        AuthorizationCodeResult authorizationCodeResult =
                corpService.createAuthorizationCode(user.getEnterpriseAccount(), appId);
        if (!authorizationCodeResult.isSuccess()) {
            logger.warn("failed to call corpService.createAuthorizationCode: user={}, appId={}",
                    user, appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取永久授权码错误"); // ignoreI18n
        }
        appDevModeInfo.setCode(authorizationCodeResult.getAuthorizationCode());
        appDevModeInfo.setIpWhites("");
        AppIpWhiteListResult appIpWhiteListResult = appIpWhiteListService.getAppIpWhiteList(appId);
        if (appIpWhiteListResult.isSuccess()) {
            String[] ipList = appIpWhiteListResult.getIpList();
            if (null != ipList && ipList.length > 0) {
                appDevModeInfo.setIpWhites(String.join(";", ipList));
            }
            appDevModeInfo.setIsStop(appIpWhiteListResult.getIsStop());
        } else {
            logger.warn("getAppIpWhiteList failed, appId[{}], appIpWhiteListResult[{}]", appId, appIpWhiteListResult);
        }
        //添加应用关联服务号
        OpenAppDO openAppDO = loadAppBrief(appId);
        if (AppType.CUSTOM_APP.value() == openAppDO.getAppType()) {
            GetServiceRefAppResult getServiceRefAppResult = appServiceRefService.getServiceRefAppList(appId, null);
            if (!getServiceRefAppResult.isSuccess()) {
                logger.warn("failed to call getServiceRefAppList, appId[{}], user[{}]", appId, user);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询关联服务号失败"); // ignoreI18n
            } else {
                List<AppServiceRefDO> appServiceRefDOs = getServiceRefAppResult.getAppServiceRefDOs();
                if (!CollectionUtils.isEmpty(appServiceRefDOs)) {
                    Map<String, String> serviceIdNameMap = appServiceRefDOs.stream().map(s -> {
                        if (StringUtils.isNotBlank(s.getServiceId())) {
                            return loadAppBrief(s.getServiceId());
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toMap(OpenAppDO::getAppId, OpenAppDO::getAppName));
                    appDevModeInfo.setServiceIdNameMap(serviceIdNameMap);
                }
            }
        }
        return appDevModeInfo;
    }

    private Map<String, Object> toMapYhSpecial(OpenAppDO app) {
        Map<String, Object> map = toMap(app);
        map.put("appName", BizCommonUtils.getAppComponentName(app.getAppName(), app.getAppId()));

        BaseResult<String> iconUrlResult = appIconService
                .queryAppIconUrlByUser(UserContextHolder.getFsUserVO(), app.getAppId(), IconType.WEB);
        if (!iconUrlResult.isSuccess()) {
            logger.warn("queryAppIconUrlByUser failed, appIdOrComponentId[{}], type[{}], result[{}] : " + app.getAppId(),
                    IconType.WEB, iconUrlResult);
        }

        map.put("appLogoUrl", iconUrlResult.getResult());
        map.put("serviceLogoUrl", iconUrlResult.getResult());
        return map;
    }

    private Map<String, Object> toMap(OpenAppDO app) {
        Map<String, Object> appData = new HashMap<>();
        appData.put("appId", app.getAppId());

        String logoUrl = queryAppIconUrl(app.getAppId(), IconType.WEB);
        // TODO: 2023/5/31  chouli 
        if (logoUrl.matches(ConfigCenter.systemIconRule) || logoUrl.startsWith(ConfigCenter.SYS_ICON_START)) {
            appData.put("isPreIcon", true);
        } else {
            appData.put("isPreIcon", false);
        }
        appData.put("appLogoUrl", logoUrl);
        appData.put("serviceLogoUrl", logoUrl);
        appData.put("appName", app.getAppName());
        appData.put("appDesc", app.getAppDesc());
        appData.put("appType", app.getAppType());
        appData.put("status", app.getStatus());
        appData.put("bindStatus", app.getBindStatus());
        appData.put("appMode", app.getAppMode());
        appData.put("appModeName", dictManager.loadValueByKey(
                CommonConstant.DICT_TYPE_APP_MODE, app.getAppMode() + ""));
        appData.put("serviceName", app.getServiceName());
        appData.put("dev", app.getOpenDevDO());
        appData.put("appClass", app.getAppClass());
        appData.put("appClassName",
                dictManager.loadValueByKey(CommonConstant.DICT_TYPE_APP_CLASS, app.getAppClass() + ""));
        return appData;
    }

    private Map<String, Object> toMap(final OpenAppComponentDO componentDO) {
        Map<String, Object> componentData = new HashMap<>();
        componentData.put("componentId", componentDO.getComponentId());
        componentData.put("componentLogoUrl", queryAppIconUrl(componentDO.getComponentId(), IconType.WEB));
        componentData.put("componentName", componentDO.getComponentName());
        componentData.put("componentType", componentDO.getComponentType());
        componentData.put("componentDesc", componentDO.getComponentDesc());
        componentData.put("componentLabel", componentDO.getComponentLabel());
        componentData.put("appId", componentDO.getAppId());
        return componentData;
    }

    private Map<String, Object> toMapYhSpecial(final OpenAppComponentDO componentDO) {
        Map<String, Object> map = toMap(componentDO);
        map.put("componentName",
                BizCommonUtils.getAppComponentName(componentDO.getComponentName(), componentDO.getComponentId()));


        BaseResult<String> iconUrlResult = appIconService
                .queryAppIconUrlByUser(UserContextHolder.getFsUserVO(), componentDO.getComponentId(), IconType.WEB);
        if (!iconUrlResult.isSuccess()) {
            logger.warn("queryAppIconUrlByUser failed, appIdOrComponentId[{}], type[{}], result[{}] : " + componentDO.getComponentId(),
                    IconType.WEB, iconUrlResult);
        }
        //告诉前端这个图标是否是预制图标
        if (iconUrlResult.getResult().matches(ConfigCenter.systemIconRule)
                || iconUrlResult.getResult().startsWith(ConfigCenter.SYS_ICON_START)) {
            map.put("isPreIcon", true);
        } else {
            map.put("isPreIcon", false);
        }
        map.put("componentLogoUrl", iconUrlResult.getResult());
        return map;
    }


    private void processIconByLogo(String appId, String appLogo) {
        List<IconType> types = Collections.singletonList(IconType.LOGO);

        String masterId;
        if (appLogo.contains("group1/M00") || appLogo.contains("group0/M00")) {
            masterId = appLogo.substring(appLogo.indexOf("=group") + 1);
            if (masterId.contains("&")) {
                masterId = masterId.substring(0, masterId.indexOf("&"));
            }
        } else {//新逻辑走北京图片url
            masterId = appLogo.substring(appLogo.indexOf("=") + 1);
            if (masterId.contains("&")) {
                masterId = masterId.substring(0, masterId.indexOf("&"));
            }
        }

        BaseResult result = appIconService.update(appId, types, masterId);
        if (!result.isSuccess()) {
            logger.warn("Add icon fail. result: {}.", result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, result.getErrDescription());
        }
    }

    private void processIconByLogo(String appId, String appLogo, String ea, Integer userId) {
        List<IconType> types = Collections.singletonList(IconType.LOGO);
        String icon;
        if (GraySwitch.isAllowForEa(GraySwitch.useNewIconPath, ea)) {
            icon = tempFileToFormalFile.tNPathToCPath(ea, userId, appLogo);
        } else {
            icon = tempFileToFormalFile.tNPathToAPath(ea, userId, appLogo);
        }

        //保存 应用图标
        BaseResult result = appIconService.update(appId, types, icon);
        if (!result.isSuccess()) {
            logger.warn("Add icon fail. result: {}.", result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, result.getErrDescription());
        }
    }

    private void processIconByLogo(String appId, String appLogo, String ea, Integer userId, AppCreateForm form) {
        List<IconType> types = Collections.singletonList(IconType.LOGO);
        String icon;
        if (GraySwitch.isAllowForEa(GraySwitch.useNewIconPath, ea)) {
            icon = tempFileToFormalFile.tNPathToCPath(ea, userId, appLogo);
        } else {
            icon = tempFileToFormalFile.tNPathToAPath(ea, userId, appLogo);
        }
        for (CustomComponentVO componentVO : form.getAppComponents()) {
            componentVO.setComponentLogo(icon);
        }


        //保存 应用图标
        BaseResult result = appIconService.update(appId, types, icon);
        if (!result.isSuccess()) {
            logger.warn("Add icon fail. result: {}.", result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, result.getErrDescription());
        }
    }

    @SuppressWarnings("all")
    private void processIcon(String appId, IconFileVO icon) {
        // 目前同一张图像适用于所有类型
        List<IconType> types = Arrays.asList(IconType.LOGO);

        // 目前裁剪方案为取短边，裁剪中间
        int width = icon.getWidth();
        int height = icon.getHeight();
        // 取短边
        int value = width < height ? width : height;
        double w = (double) width;
        double h = (double) height;
        int x = (int) Math.round((w - value) / 2);
        int y = (int) Math.round((h - value) / 2);
        Rectangle rectangle = new Rectangle(x, y, value, value);

        BaseResult result = appIconService.update(appId, types, icon.getData(), icon.getExt(), rectangle);
        if (!result.isSuccess()) {
            logger.warn("Add icon fail. result: {}.", result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, result.getErrDescription());
        }
    }

    @Override
    public String createCustomComponent(FsUserVO fsAdminUser, CustomComponentVO customComponentVO) {
        // 自定义应用同一类型的组件只能创建一个
        BaseResult<List<OpenAppComponentDO>> componentDOsResult = openAppComponentService.queryAppComponentListByAppId(
                fsAdminUser, customComponentVO.getAppId());

        if (!componentDOsResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取组件失败"); // ignoreI18n
        }

        if (componentDOsResult.getResult().stream().anyMatch(openAppComponentDO -> openAppComponentDO.getComponentType()
                .equals(customComponentVO.getComponentType()))) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "该类型的组件只能创建一个"); // ignoreI18n
        }

        // 头像预处理
        IconFileVO logoIcon = null;
        if (null != customComponentVO.getLogoIconFile()) {
            try {
                logoIcon = IconFileVO.create(customComponentVO.getLogoIconFile());
            } catch (Exception e) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "图片异常"); // ignoreI18n
            }
        }

        AppArg arg = new AppArg();
        arg.setAppType(AppTypeEnum.CUSTOM_APP);

        if (AppComponentTypeEnum.APP.getType() == customComponentVO.getComponentType()) {
            arg.setCallBackLoginUrl(customComponentVO.getLoginUrl());
        }

        if (AppComponentTypeEnum.WEB.getType() == customComponentVO.getComponentType()) {
            arg.setCallBackWebLoginUrl(customComponentVO.getLoginUrl());
        }

        if (AppComponentTypeEnum.SERVICE.getType() == customComponentVO.getComponentType()) {
            arg.setAppType(AppTypeEnum.SERVICE_APP);
            arg.setCallBackLoginUrl(customComponentVO.getLoginUrl());
            arg.setCallBackWebLoginUrl(customComponentVO.getLoginUrl());
        }

        if (AppComponentTypeEnum.LINK_SERVICE.getType() == customComponentVO.getComponentType()) {
            arg.setAppType(AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE);
            arg.setCallBackLoginUrl(customComponentVO.getLoginUrl());
            arg.setCallBackWebLoginUrl(customComponentVO.getLoginUrl());
        }

        // oauth创建组件
        CreateComponentResult createComponentResult = appService.createComponent(customComponentVO.getAppId(), arg);
        if (!createComponentResult.isSuccess()) {
            logger.error("createCustomComponent failed,appId[{}],openAppComponentVO [{}] result[{}]", customComponentVO.getAppId(),
                    customComponentVO.toString(), createComponentResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, createComponentResult, "无法创建组件"); // ignoreI18n
        }
        customComponentVO.setComponentId(createComponentResult.getComponentId());

        // 头像存储
        if (null != logoIcon) {
            processIcon(createComponentResult.getComponentId(), logoIcon);
        } else if (StringUtils.isNotBlank(customComponentVO.getComponentLogo())) {
            processIconByLogo(createComponentResult.getComponentId(), customComponentVO.getComponentLogo());
        }
        // 应用中心创建组件
        OpenAppComponentDO entity = new OpenAppComponentDO();
        entity.setAppId(customComponentVO.getAppId());
        entity.setComponentId(createComponentResult.getComponentId());
//        entity.setComponentDesc(StringEscapeUtils.escapeHtml4(customComponentVO.getComponentDesc()));
//        entity.setComponentName(StringEscapeUtils.escapeHtml4(customComponentVO.getComponentName()));
        // 防xss在前端显示时处理.java不处理.add by lambo@********.
        entity.setComponentName(customComponentVO.getComponentName());
        entity.setComponentDesc(customComponentVO.getComponentDesc());
        entity.setComponentLabel(CommonConstant.APP_LABEL_OTHER);
        entity.setComponentType(customComponentVO.getComponentType());//see AppComponentTypeEnum
        entity.setStatus(AppComponentStatus.VALID.getStatus());
        entity.setGmtCreate(new Date());
        entity.setGmtModified(new Date());
        BaseResult<Void> baseResult = openAppComponentService.createOpenAppComponent(null, entity);
        if (!baseResult.isSuccess()) {
            logger.error("createCustomComponent failed,appId[{}],openAppComponentVO [{}] result[{}]", customComponentVO.getAppId(),
                    customComponentVO.toString(), baseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "无法创建组件"); // ignoreI18n
        }

        // 保存组件的可见范围
        final AppComponentTypeEnum componentTypeEnum = AppComponentTypeEnum.getByCode(customComponentVO.getComponentType());

        //修改自定义应用的组件的默认可见范围为创建人.
        AppViewDO appViewDO = new AppViewDO();
        appViewDO.setMember(new Integer[]{fsAdminUser.getUserId()});
        //使用用户自动配置的可见范围.add by lambo@20160328
        if (StringUtils.isNotBlank(customComponentVO.getView())) {
            appViewDO = JsonKit.json2object(customComponentVO.getView(), AppViewDO.class);
        }
        if (GraySwitch.isAllowForEa(GraySwitch.useNewPaasAppGray, fsAdminUser.getEa())) {
            openFsUserAppViewService.saveAppComponentView(fsAdminUser, customComponentVO.getComponentId(), customComponentVO.getComponentType(), appViewDO);
        } else {
            BaseResult<Void> saveAppViewResult = openFsUserAppViewService
                    .saveFsUserAppViewList(fsAdminUser, customComponentVO.getComponentId(), componentTypeEnum,
                            appViewDO);
            if (!saveAppViewResult.isSuccess()) {
                logger.error("saveFsUserAppViewList failed,fsAdminUser[{}],componentId [{}],appViewDO[{}]，saveAppViewResult=[{}]", fsAdminUser,
                        customComponentVO.getComponentId(), appViewDO, saveAppViewResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, saveAppViewResult, "保存组件范围错误"); // ignoreI18n
            }
        }


        // warn: 上述非事务处理方式有可能导致组件已经创建,但是却抛出异常,客户以为创建失败,会多次创建
        return createComponentResult.getComponentId();
    }

    @Override
    public void updateCustomComponent(FsUserVO fsAdminUser, CustomComponentVO customComponentVO) {
        // 头像预处理
        IconFileVO logoIcon = null;
        if (null != customComponentVO.getLogoIconFile()) {
            try {
                logoIcon = IconFileVO.create(customComponentVO.getLogoIconFile());
            } catch (Exception e) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "无法解析您上传的图片，该图片可能采用了比较特殊的编码格式或颜色空间"); // ignoreI18n
            }
        }

        final String componentId = customComponentVO.getComponentId();
        BaseResult<OpenAppComponentDO> componentDOResult =
                openAppComponentService.loadOpenAppComponentById(null, componentId);
        if (!componentDOResult.isSuccess()) {
            logger.error("failed to call openAppComponentService.loadOpenAppComponentById: componentId={}",
                    componentId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, componentDOResult, "无法加载组件"); // ignoreI18n
        }

        final OpenAppComponentDO componentDO = componentDOResult.getResult();
        if (!componentDO.getComponentType().equals(customComponentVO.getComponentType())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "组件类型不可以修改"); // ignoreI18n
        }

        // 更新OAuth
        AppDO appDO = new AppDO();
        appDO.setAppId(customComponentVO.getComponentId());
        if (customComponentVO.getComponentType() == AppComponentTypeEnum.APP.getType() || customComponentVO.getComponentType() == AppComponentTypeEnum.WEB.getType()) {
            if (customComponentVO.getComponentType() == AppComponentTypeEnum.APP.getType()) {
                appDO.setCallBackLoginUrl(customComponentVO.getLoginUrl());
            }

            if (customComponentVO.getComponentType() == AppComponentTypeEnum.WEB.getType()) {
                appDO.setCallBackWebLoginUrl(customComponentVO.getLoginUrl());
            }
            CommonResult commonResult = appService.updateComponent(null, null, appDO);
            if (!commonResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, commonResult, "修改组件失败."); // ignoreI18n
            }
        }

        // 头像存储
        if (null != logoIcon) {
            processIcon(componentId, logoIcon);
        } else if (StringUtils.isNotBlank(customComponentVO.getComponentLogo())) {
            processIconByLogo(componentId, customComponentVO.getComponentLogo());
        }

        // 更新应用中心组件
        OpenAppComponentDO entity = new OpenAppComponentDO();
        entity.setComponentId(customComponentVO.getComponentId());
        entity.setComponentDesc(customComponentVO.getComponentDesc());
        entity.setComponentName(customComponentVO.getComponentName());
        openAppComponentService.updateOpenAppComponent(null, entity);

        // 修改App组件,还需要通知终端拉取列表
        if (customComponentVO.getComponentType() == AppComponentTypeEnum.APP.getType()) {
            final BaseResult<EmployeeRange> employeeRangeResult =
                    openFsUserAppViewService.loadComponentView(fsAdminUser, componentId);
            if (!employeeRangeResult.isSuccess()) {
                logger.warn("failed to loadComponentView: user={}, componentId={}",
                        fsAdminUser, componentId);
            } else {
                final BaseResult<Void> notifyResult = openFsUserAppViewService.notifyEndUsers(
                        fsAdminUser, customComponentVO.getAppId(), employeeRangeResult.getResult());
                if (!notifyResult.isSuccess()) {
                    logger.warn("failed to notifyEndUsers: user={}, appId={}, employeeRange={}",
                            fsAdminUser, customComponentVO.getAppId(), employeeRangeResult.getResult());
                }
            }
        }

        resetComponentUrlCache(fsAdminUser, componentId);
    }

    /**
     * 清理组件地址缓存.
     *
     * @param fsAdminUser
     * @param componentId
     */
    private void resetComponentUrlCache(FsUserVO fsAdminUser, String componentId) {
        BaseResult<Void> voidBaseResult = openComponentLoginUrlService.resetComponentLoginUrlCacheBatch(fsAdminUser, Lists.newArrayList(componentId));
        if (!voidBaseResult.isSuccess()) {
            logger.error("resetComponentLoginUrlCacheBatch failed, user[{}],componentId[{}],result [{}]", fsAdminUser, componentId, voidBaseResult);
        }
    }

    @Override
    public void deleteCustomComponent(FsUserVO fsAdminUser, String componentId) {
        BaseResult<OpenAppComponentDO> openAppComponentDOResult =
                openAppComponentService.loadOpenAppComponentById(fsAdminUser, componentId);
        if (!openAppComponentDOResult.isSuccess()) {
            logger.error("failed to load openAppComponent: user={}, componentId={}",
                    fsAdminUser, componentId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, openAppComponentDOResult, "加载组件信息失败"); // ignoreI18n
        }
        final OpenAppComponentDO openAppComponentDO = openAppComponentDOResult.getResult();

        AppResult appResult = openAppService.loadOpenAppFast(openAppComponentDO.getAppId());

        if (!appResult.isSuccess()) {
            logger.error("openAppService.loadOpenApp: user={}, appId={}",
                    fsAdminUser, openAppComponentDO.getAppId());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "加载应用信息失败"); // ignoreI18n
        }

        if (!fsAdminUser.getEnterpriseAccount().equals(appResult.getResult().getAppCreater())) {
            logger.warn("ea not equals app creater : user={}, appId={}",
                    fsAdminUser, openAppComponentDO.getAppId());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "不能删除其他企业的组件"); // ignoreI18n
        }

        final AppComponentTypeEnum componentTypeEnum =
                AppComponentTypeEnum.getByCode(openAppComponentDO.getComponentType());

        final BaseResult<EmployeeRange> employeeRangeResult = openFsUserAppViewService.loadComponentView(fsAdminUser,
                componentId);
        if (!employeeRangeResult.isSuccess()) {
            logger.warn("failed to loadComponentView: user={}, componentId={}",
                    fsAdminUser, componentId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, employeeRangeResult, "加载组件可见范围失败"); // ignoreI18n
        }

        CommonResult commonResult = appService.deleteComponent(componentId);
        if (!commonResult.isSuccess()) {
            logger.warn("failed to call appService.deleteComponent: componentId={}",
                    componentId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, commonResult, "删除组件授权信息失败"); // ignoreI18n
        }

        BaseResult<Void> deleteResult = openAppComponentService.deleteCustomComponent(null, componentId);
        if (GraySwitch.isAllowForEa(GraySwitch.clearAppCache, fsAdminUser.getEnterpriseAccount())) {
            clearCache(fsAdminUser);
        }
        if (!deleteResult.isSuccess()) {
            logger.warn("failed to call openAppComponentService.deleteCustomComponent: componentId={}",
                    componentId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, commonResult, "删除组件基本信息失败"); // ignoreI18n
        }
        // 删除App组件后需要通知终端重新拉取列表
        if (componentTypeEnum == AppComponentTypeEnum.APP) {
            BaseResult<Void> notifyResult = openFsUserAppViewService.notifyEndUsers(fsAdminUser,
                    openAppComponentDO.getAppId(),
                    employeeRangeResult.getResult());
            if (!notifyResult.isSuccess()) {
                logger.warn("failed to notifyEndUsers: user={}, appId={}, employeeRange={}",
                        fsAdminUser, openAppComponentDO.getAppId(), employeeRangeResult.getResult());
                // remark by xialf: 这个失败也没办法了,组件已经删除了
            }
        }
    }

    public void clearCache(FsUserVO fsAdminUser) {
        // 新建企业自建清除企业级缓存
        int ei = eieaConverter.enterpriseAccountToId(fsAdminUser.getEnterpriseAccount());
        ClearCacheArg clearCacheArg = new ClearCacheArg();
        clearCacheArg.setEnterpriseId(ei);
        Map<String, String> header = new HashMap<>();
        clearCacheArg.setPreKey(paasAppViewsForEaVoPreKey);
        header.put("x-fs-ei", String.valueOf(ei));
        try {
            clientDataRestService.clearCache(clearCacheArg, header);
        } catch (Exception e) {
            logger.error("updateComponent Cache error clearCacheArg:{},error", clearCacheArg, e);
        }
    }

    @Override
    public void saveApplicationIfToPaas(FsUserVO user, String appId, Integer appStatus) {
        Map<String, Object> appInfo = loadAppByAppId(user, appId, "CH");
        int tenantId = eieaConverter.enterpriseAccountToId(user.getEnterpriseAccount());
        int userId = user.getUserId();
        List<SavePaasAppArg> args = new ArrayList<>();
        if (appInfo != null) {
            //获取组件信息,修改应用下组件信息
            //appInfo ->JSONobject
            JSONObject appInfoJson = new JSONObject(appInfo);
            JSONArray components = appInfoJson.getJSONArray("components");
            if (!CollectionUtils.isEmpty(components)) {
                for (Object component : components) {
                    SavePaasAppArg arg = new SavePaasAppArg();
                    arg.setTenantId(tenantId);
                    arg.setUserId(userId);
                    arg.setStatus(appStatus);
                    JSONObject componentJson = new JSONObject((Map<String, Object>) component);
                    int componentType = componentJson.getIntValue("componentType");
                    arg.setAppId(componentJson.getString("componentId"));
                    arg.setParentAppId(componentJson.getString("appId"));
                    //判断是移动端App还是web端app
                    if (componentType == AppComponentTypeEnum.APP.getType()) {
                        arg.setAccessType(AccessTypeEnum.APP.getType());
                    } else {
                        arg.setAccessType(AccessTypeEnum.WEB.getType());
                    }
                    //获取组件的适用范围
                    AppViewDO appViewDO = componentJson.getObject("view", AppViewDO.class);
                    List<Scope> scopes = ScopeCenter.convertViewToScope(appViewDO);
                    arg.setScopes(scopes);
                    args.add(arg);
                }
            }
            //获取应用信息
            SavePaasAppArg savePaasAppArg = new SavePaasAppArg();
            savePaasAppArg.setAppId(appId);
            savePaasAppArg.setUserId(userId);
            savePaasAppArg.setTenantId(tenantId);
            savePaasAppArg.setStatus(appStatus);
            args.add(savePaasAppArg);
        }
        for (SavePaasAppArg arg : args) {
            openFsUserAppViewService.saveAppComponentStatus(arg);
        }
    }

    @Override
    public String enableDevMode(FsUserVO fsAdminUser, AppDevModeInfo appDevModeInfo) {
        // 获取永久授权码
        AuthorizationCodeResult authorizationCodeResult = corpService.createAuthorizationCode(
                fsAdminUser.getEnterpriseAccount(), appDevModeInfo.getAppId());
        if (!authorizationCodeResult.isSuccess()) {
            logger.warn("failed to call corpService.createAuthorizationCode: fsEa={}, appId={}",
                    fsAdminUser.getEnterpriseAccount(), appDevModeInfo.getAppId());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, authorizationCodeResult, "获取永久授权码失败"); // ignoreI18n
        }

        // 更新自定义应用授权信息OAuth
        AppDO appDO = new AppDO();
        appDO.setAppId(appDevModeInfo.getAppId());
        appDO.setComponent(false);
        appDO.setCustomAppDevStatus(CustomAppDevStatus.ENABLED);

        appDO.setAppSecret(appDevModeInfo.getAppSecret());
        appDO.setEncodingAesKey(appDevModeInfo.getEncodingAesKey());
        appDO.setToken(appDevModeInfo.getToken());

        appDO.setCallBackDomain(appDevModeInfo.getCallBackDomain());
        appDO.setCallBackMsgUrl(appDevModeInfo.getCallBackMsgUrl());
        UpdateAppResult updateAppResult = appService.updateApp(null, null, appDO);
        if (!updateAppResult.isSuccess()) {
            logger.warn("failed to call appService.updateApp: appDO=[{}]",
                    appDO);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, updateAppResult, "更新自定义应用授权信息失败"); // ignoreI18n
        }

        // 新增应用白名单OAuth
        String[] ipWhiteList = appDevModeInfo.getIpWhites().split(";");
        com.facishare.open.appaccesscontrol.result.CommonResult commonResult = appIpWhiteListService.setAppIpWhiteListV2(appDevModeInfo.getAppId(), ipWhiteList, appDevModeInfo.getIsStop());
        if (!commonResult.isSuccess()) {
            logger.warn("setAppIpWhiteList error, appId [{}], ipWhiteList [{}], isStop [{}], commonResult[{}]", appDevModeInfo.getAppId(), ipWhiteList, appDevModeInfo.getIsStop(), commonResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, commonResult, "更新应用白名单失败。"); // ignoreI18n
        }


        // 更新自定义应用基础信息(应用中心)
        OpenAppDO openAppDO = new OpenAppDO();
        openAppDO.setAppId(appDevModeInfo.getAppId());
        openAppDO.setGmtModified(new Date());
        openAppDO.setAppMode(AppModeEnum.DEV.getCode());
        AppResult appResult = openAppService.updateOpenApp(openAppDO);
        if (!appResult.isSuccess()) {
            logger.warn("failed to call openAppService.updateOpenApp: openAppDO={}",
                    openAppDO);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, updateAppResult, "更新自定义应用授权信息失败"); // ignoreI18n
        }
        return authorizationCodeResult.getAuthorizationCode();
    }

    @Override
    public void disableDevMode(String appId) {
        AppDO appDO = new AppDO();
        appDO.setAppId(appId);
        appDO.setCustomAppDevStatus(CustomAppDevStatus.DISABLED);
        UpdateAppResult updateAppResult = appService.updateApp(null, null, appDO);
        if (!updateAppResult.isSuccess()) {
            logger.warn("failed to call appService.updateApp(disable): appDO=[{}]",
                    appDO);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, updateAppResult, "取消自定义应用开发模式失败"); // ignoreI18n
        }

        OpenAppDO openAppDO = new OpenAppDO();
        openAppDO.setAppId(appId);
        openAppDO.setAppMode(AppModeEnum.COMMON.getCode());
        openAppDO.setGmtModified(new Date());
        AppResult appResult = openAppService.updateOpenApp(openAppDO);
        if (!appResult.isSuccess()) {
            logger.warn("failed to call openAppService.updateOpenApp: appId={}",
                    appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "取消自定义应用开发模式失败"); // ignoreI18n
        }

        //移除ip白名单缓存
        com.facishare.open.appaccesscontrol.result.CommonResult commonResult = appIpWhiteListService.removeAppIdWhiteListCache(appId);
        if (!commonResult.isSuccess()) {
            logger.warn("failed to call appIpWhiteListService.removeAppIdWhiteListCache: appId={}", appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "取消自定义应用开发模式失败"); // ignoreI18n
        }
    }

    @Override
    public Map<String, Object> loadAppDtlByComponentId(FsUserVO user, String componentId) {
        BaseResult<OpenAppComponentDO> openAppComponentDOBaseResult = openAppComponentService.loadOpenAppComponentById(
                user, componentId);
        if (!openAppComponentDOBaseResult.isSuccess() || AppComponentStatus.VALID.getStatus() != openAppComponentDOBaseResult.getResult().getStatus()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, openAppComponentDOBaseResult, "组件不存在"); // ignoreI18n
        }
        Map<String, Object> result = new HashMap<>();
        result.put("componentId", componentId);
        result.put("componentName", openAppComponentDOBaseResult.getResult().getComponentName());
        AppResult appResult = openAppService.loadOpenApp(openAppComponentDOBaseResult.getResult().getAppId());
        if (!appResult.isSuccess() || AppStatus.ON_LINE.getStatus() != appResult.getResult().getStatus()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "加载app失败"); // ignoreI18n
        }
        OpenAppDO openApp = appResult.getResult();
        result.put("appId", openApp.getAppId());
        result.put("appName", openApp.getAppName());
        result.put("appDesc", openApp.getAppDesc());
        result.put("appClass", openApp.getAppClass());
        result.put("appClassName", dictManager.loadValueByKey(CommonConstant.DICT_TYPE_APP_CLASS, openApp.getAppClass() + ""));
        result.put("gmtCreateDate", openApp.getGmtCreate());
        result.put("appType", openApp.getAppType());
        if (null != openApp.getOpenDevDO()) {
            result.put("devName", openApp.getOpenDevDO().getDevName());
        }
        result.put("appLogoUrl", queryAppIconUrl(openApp.getAppId(), IconType.WEB));
        return result;
    }


    @Override
    public Map<String, Object> loadAppDetailByAppId(FsUserVO user, String appId, String lang) {

        Map<String, Object> result = new HashMap<>();
        AppResult appResult = openAppService.loadOpenApp(appId);
        if (!appResult.isSuccess() || AppStatus.ON_LINE.getStatus() != appResult.getResult().getStatus()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "加载app失败"); // ignoreI18n
        }
        OpenAppDO openApp = appResult.getResult();
        int ei = eieaConverter.enterpriseAccountToId(user.getEa());
        //替换多语言
        if (openApp != null) {
            openApp = I18NUtils.modifyOpenAppDOByLang(ei, Arrays.asList(openApp), lang).get(0);
        }

        result.put("appName", openApp.getAppName());
        result.put("appDesc", openApp.getAppDesc());
        result.put("appClass", openApp.getAppClass());
        result.put("appClassName",
                dictManager.loadValueByKey(CommonConstant.DICT_TYPE_APP_CLASS, openApp.getAppClass() + ""));
        result.put("gmtCreateDate", openApp.getGmtCreate());
        result.put("appType", openApp.getAppType());
        result.put("tryType", openApp.getTryType());
        OpenDevDO openDevDO = openApp.getOpenDevDO();

        //替换多语言
        if (openDevDO != null) {
            openDevDO = I18NUtils.modifyDevByLang(ei, Arrays.asList(openDevDO), lang).get(0);
        }

        result.put("devType", 1);
        if (null != openDevDO) {
            result.put("devName", openDevDO.getDevName());
            result.put("phoneNum", openDevDO.getPhoneNo());
            result.put("orgWebsite", openDevDO.getOrgWebsite());
            if (StringUtils.isNotEmpty(openDevDO.getProperties())) {
                OpenDevProperties openDevProperties = OpenDevProperties.fromJson(openDevDO.getProperties());
                result.put("intro", openDevProperties.getIntro());
            }

            if (BizCommonUtils.isOfficialApp(openApp)) {
                result.put("devType", 2);//1非官方 2官方
            }

        }
        result.put("appLogoUrl", queryAppIconUrl(openApp.getAppId(), IconType.WEB));
        OpenAppProperties openAppProperties = OpenAppProperties.fromJson(openApp.getProperties());

        if (null != openAppProperties) {
            if (!CollectionUtils.isEmpty(openAppProperties.getDetailImageUrl())) {
                List<String> collect = openAppProperties.getDetailImageUrl().stream().map(s -> {
                    if (s.contains("group1/M00") || s.contains("group0/M00")) { //老图片系统
                        return imageUrl + s;
                    } else {
                        return ConfigCenter.BJ_IMAGE_VIEW_URL + s;
                    }
                }).collect(Collectors.toList());

                result.put("detailImageUrls", collect);
            }
            result.put("appIntro", openAppProperties.getAppIntro());
            result.put("payDesc", openAppProperties.getPayDesc()); // 增加付费信息
            if (Integer.valueOf(1).equals(openApp.getPayType())) { //收费应用
                result.put("purchaseOnline", openAppProperties.getPurchaseOnline());
                result.put("isGrayed", BizCommonUtils.isAllowPurchaseOnline(user.getEnterpriseAccount()) ? 1 : 0);
            }
        }

        final BaseResult<Boolean> isAppAdminResult =
                openAppAdminService.isAppAdmin(user.asStringUser(), ConfigCenter.ENTERPRISE_WALLET_APP_ID);
        if (!isAppAdminResult.isSuccess()) {
            logger.warn("fail to openAppAdminService.isAppAdmin, user[{}], appId[{}], result[{}]",
                    user, ConfigCenter.ENTERPRISE_WALLET_APP_ID, isAppAdminResult);
            throw new BizException(isAppAdminResult);
        }

        final boolean isAdmin = webAuthManager.isFsAdmin(user);
        result.put("isAdmin", isAdmin ? 1 : 0);
        result.put("isWalletAdmin", isAppAdminResult.getResult() ? 1 : 0);
        result.put("payType", openApp.getPayType());
        result.put("targetUrl", appTargetUrl + appId);

        final com.facishare.open.common.result.BaseResult<TryStatusEnum> statusResult =
                tryStatusService.queryTryStatus(openApp, user);
        if (!statusResult.isSuccess()) {
            throw new BizException(statusResult);
        }

        final TryStatusEnum tryStatusEnum = statusResult.getResult();
        final boolean isAppAdded = APP_ADDED_STATUS.contains(tryStatusEnum);
        result.put("appTryStatus", tryStatusEnum.getCode());
        result.put("btnText", isAppAdded ? "已添加" : "添加"); // ignoreI18n
        result.put("btnStatus", isAppAdded ? 1 : 2);
        if (!isAppAdded && isAdmin) {
            result.put("btnPrompt", ConfigCenter.ADD_BTN_PROMPT_ADMIN);
        }
        if (!isAppAdded && !isAdmin) {
            final BaseResult<List<Integer>> adminIds = openAppAddressBookEmployeeService.getAdminIds(user.getEnterpriseAccount());
            if (!adminIds.isSuccess()) {
                throw new BizException(adminIds);
            }
            final BaseResult<List<Employee>> employeesNoAdminId = openAppAddressBookEmployeeService.getEmployeesNoAdminId(user.getEnterpriseAccount(), adminIds.getResult());
            if (!employeesNoAdminId.isSuccess()) {
                throw new BizException(employeesNoAdminId);
            }

            result.put("admins", employeesNoAdminId.getResult().stream().map(emp -> new Admin(emp.getEmployeeId(), emp.getName())).collect(Collectors.toList()));
            result.put("btnPrompt", ConfigCenter.ADD_BTN_PROMPT_USER);
        }
        result.put("btnUrl", getJumpUrlForButton(appId, tryStatusEnum.getTryUrlEnum()));

        //临时方案：需要调整已到期的状态的动作为6
        result.put("btnAction", EXPIRED_STATUS_ENUMS.contains(tryStatusEnum) ? 6 : tryStatusEnum.getTargetTypeEnum().getCode());
        result.put("isAdd", isAddStatusSet.contains(tryStatusEnum) ? 1 : 2);

        // 增加组件描述信息
        final BaseResult<List<OpenAppComponentDO>> componentsResult =
                openAppComponentService.queryAppComponentListByAppId(user, appId);
        if (!componentsResult.isSuccess()) {
            logger.warn("fail to openAppComponentService.queryAppComponentListByAppId, user={}, appId={}, result={}",
                    user, appId, componentsResult);
            throw new BizException(componentsResult);
        }

        //替换多语言
        componentsResult.setResult(I18NUtils.modifyComponentByLang(ei, componentsResult.getResult(), lang));

        final List<Map<String, Object>> componentsMap =
                componentsResult.getResult().stream()
                        .sorted(Comparator.comparing(OpenAppComponentDO::getComponentType))
                        .map(this::toMap).collect(Collectors.toList());
        result.put("components", componentsMap);
        result.put("isAllowSetView", !BizCommonUtils.getCrmAppId().equals(appId));
        return result;
    }

    @Getter
    public static class Admin {
        final int id;
        final String name;

        Admin(int id, String name) {
            this.id = id;
            this.name = name;
        }
    }


    /**
     * 已添加状态集.
     */
    private final ImmutableSet<TryStatusEnum> isAddStatusSet = ImmutableSet.of(TryStatusEnum.ADMIN_ADDED,
            TryStatusEnum.ADMIN_TRYING, TryStatusEnum.BOUGHT, TryStatusEnum.EMPLOYEE_TRYING);

    @Override
    public OpenAppDO loadAppBrief(String appId) {
        final AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            logger.warn("fail to loadOpenAppFast, appId={}, result={}",
                    appId, appResult);
            throw new BizException(appResult);
        }
        return appResult.getResult();
    }

    /**
     * 获取跳转地址.
     *
     * @param tryUrlEnum
     * @return
     */
    private String getJumpUrlForButton(final String appId, final TryUrlEnum tryUrlEnum) {
        String url = null;
        switch (tryUrlEnum) {
            case TRY_URL:
                url = trialUrl + appId;
                break;
            case AUTH_URL:
                url = authorizeUrl + appId;
                break;
            case NOTIFY_ADMIN_URL:
                url = notifyAdminUrl + appId;
                break;
            case VOID:
                break;
            default:
                logger.error("no case for enum {} of TryUrlEnum" + tryUrlEnum);
        }
        return url;
    }


    @Override
    public void deleteCustomApp(FsUserVO user, String lang, String appId) {
        OpenAppDO openAppDO = this.loadAppBrief(appId);
        String enterpriseAccount = openAppDO.getAppCreater();
        StatusResult statusResult = openAppService.deleteCustomApp(user, appId, enterpriseAccount);
        if (!statusResult.isSuccess()) {
            logger.error("delete custom app failed, appId[{}],user[{}] result[{}]", appId, user, statusResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, statusResult, statusResult.getErrDescription());
        }

        int appType = openAppDO.getAppType();
        if (AppType.SERVICE.value() == appType) {
            customMenuManager.deleteCustomMenu(user, appId);
            //删除服务号的同时,删除服务号推广企业服务号数缓存
            servicePromotionManager.delEaServiceCountCache(user, appId);
        }

        // 删除应用或者服务号写日志到mq
        try {
            if (AppType.LINK_SERVICE.value() == appType
                    || AppType.OUT_SERVICE_APP.value() == appType
                    || AppType.OPERATION_SERVICE_APP.value() == appType
                    || AppType.EXT_SERVICE_APP.value() == appType
                    || AppType.BASE_SERVICE_APP.value() == appType
                    || AppType.SERVICE.value() == appType) {
                appMessageToMqManager.sendAppMessageToMq(user, lang, null, openAppDO.getAppName(), CommonConstant.DELETE_SERVICE_MODE, appId, AppCenterMqTagsConstant.DELETE_APP_OR_SERVICE_TAGS);
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                        user.getUserId(), System.currentTimeMillis() + "", "删除了服务号\"" + openAppDO.getAppName() + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            } else if (AppType.BASE_APP.value() == appType
                    || AppType.DEV_APP.value() == appType
                    || AppType.CUSTOM_APP.value() == appType) {
                appMessageToMqManager.sendAppMessageToMq(user, lang, null, openAppDO.getAppName(), CommonConstant.DELETE_APP_MODE, appId, AppCenterMqTagsConstant.DELETE_APP_OR_SERVICE_TAGS);
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                        user.getUserId(), System.currentTimeMillis() + "", "删除了应用\"" + openAppDO.getAppName() + "\"", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            }
        } catch (Exception e) {
            logger.warn("failed to call appMessageToMqManager.sendAppMessageToMq: user={}, appName={}, appType={}, appId={}, tags={}",
                    user, openAppDO.getAppName(), appType, appId, AppCenterMqTagsConstant.DELETE_APP_OR_SERVICE_TAGS, e);
        }

        // 删除一个企业下的所有用户的服务号Session
        if (AppCenterEnum.AppType.SERVICE.value() == appType
                || AppType.OUT_SERVICE_APP.value() == appType
                || AppType.LINK_SERVICE.value() == appType) {
            DeleteSessionSingleEaVO deleteSessionSingleEaVO = new DeleteSessionSingleEaVO();
            deleteSessionSingleEaVO.setEnterpriseAccount(enterpriseAccount);
            deleteSessionSingleEaVO.setAppId(openAppDO.getAppId());
            deleteSessionSingleEaVO.setDeleteAllMessage(true);
            deleteSessionSingleEaVO.setHide(true);
            int sessionType = 0;
            if (appType == AppType.LINK_SERVICE.value()) {
                sessionType = 2;
            }
            deleteSessionSingleEaVO.setSessionType(sessionType);
            msgSessionService.deleteSessionSingleEnterprise(deleteSessionSingleEaVO);
        }

        // 删除服务通工单配置
        if (!Objects.equals(AppType.LINK_SERVICE.value(), appType)) {
            eserviceManager.deleteEserviceWorkOrderSetting(user.getEnterpriseAccount(), appId);
        }

        // 移除服务号的下游授权
        if (AppType.LINK_SERVICE.value() == appType) {
            HeaderObj headerObj = HeaderObj.newInstance(user.getEnterpriseAccount(), appId, null, null);
            customerServiceService.batchRemoveCustomerServiceAuth(headerObj, BatchRemoveCustomerServiceAuthArg.builder().upstreamEa(user.getEnterpriseAccount()).customerServiceId(appId).downstreamEaRemoveList(null).build());
        }
    }

    @Override
    public String getAppIdByComponentId(FsUserVO user, String componentId) {
        return getComponentByComponentId(user, componentId).getAppId();
    }

    /**
     * 通过ComponentId查询组件实体.
     *
     * @param user        当前登录的的用户信息.
     * @param componentId 组件id.
     * @return OpenAppComponentDO.
     */
    @Override
    public OpenAppComponentDO getComponentByComponentId(FsUserVO user, String componentId) {
        BaseResult<OpenAppComponentDO> componentDoResult = openAppComponentService.loadOpenAppComponentById(user,
                componentId);
        if (!componentDoResult.isSuccess() || null == componentDoResult.getResult()) {
            logger.warn("failed to call openAppComponentService.loadOpenAppComponentById: user={}, componentId={}, result={}",
                    user, componentId, componentDoResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询组件失败"); // ignoreI18n
        }
        return componentDoResult.getResult();
    }

    @Override
    public OpenAppDO checkAppTypeAndOwner(FsUserVO user, String appId) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不存在"); // ignoreI18n
        }
        boolean checkType = AppType.CUSTOM_APP.value() != appResult.getResult().getAppType() &&
                AppType.LINK_SERVICE.value() != appResult.getResult().getAppType() &&
                AppType.SERVICE.value() != appResult.getResult().getAppType();
        if (checkType || !user.getEnterpriseAccount().equals(appResult.getResult().getAppCreater())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult, "应用不是当前企业创建"); // ignoreI18n
        }
        return appResult.getResult();
    }

    @Override
    public Pager<Map<String, Object>> queryPlatFormAppList(FsUserVO user, Integer currentPage, Integer pageSize, String lang) {
        Pager<OpenAppDO> page = new Pager<OpenAppDO>();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.addParam("fsEnterpriseAccount", user.getEnterpriseAccount());
        page.addParam("fsAdminUser", user.asStringUser());
        // 1.获取所有应用（排除已授权的应用）
        BaseResult<Pager<OpenAppDO>> pageResult = openAppService.queryPlatFormAppList(page);
        if (!pageResult.isSuccess()) {
            logger.warn("failed to queryPlatFormAppList, user={}, page={}, result={}", user, page, pageResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取平台错误"); // ignoreI18n
        }
        Pager<OpenAppDO> pp = pageResult.getResult();

        //替换多语言
        int ei = eieaConverter.enterpriseAccountToId(user.getEa());
        pp.setData(I18NUtils.modifyOpenAppDOByLang(ei, pp.getData(), lang));

        Pager<Map<String, Object>> mapResult = new Pager<>();
        mapResult.setCurrentPage(currentPage);
        mapResult.setPageSize(pageSize);
        mapResult.setRecordSize(pp.getRecordSize());
        List<Map<String, Object>> lst = new ArrayList<>();
        if (null != pp.getData()) {
            lst = pp.getData().stream()
                    .map(this::toMap)
                    .collect(Collectors.toList());
        }
        mapResult.setData(lst);
        return mapResult;
    }

    @Override
    public List<Map<String, Object>> queryCustomAppList(FsUserVO user, String lang, boolean isFsAdmin, boolean isAppAdmin) {
        List<OpenAppDO> openAppDOs = null;

        //初始化示例应用.
        if (BizCommonUtils.isGrayedInitDemoApp(user)) {
            appCreateTemplateManager.checkAndCreateDemoApp(user, lang);
        }

        final List<String> appIdByAppAdminLoad = new ArrayList<>();
        if (isFsAdmin) {
            openAppDOs = appBindManager.queryAppsByFsAdmin(user);
        } else {
            BaseResult<List<OpenAppDO>> baseResult = openFsUserAppViewService.queryVisibleAppList(user);
            if (!baseResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "加载可见应用失败"); // ignoreI18n
            }
            openAppDOs = baseResult.getResult();
            if (isAppAdmin) {
                BaseResult<List<String>> listBaseResult = openAppAdminService.queryAppIdList(user.asStringUser());
                appIdByAppAdminLoad.addAll(listBaseResult.getResult());
                if (!listBaseResult.isSuccess()) {
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "加载可见应用失败"); // ignoreI18n
                }
                if (!CollectionUtils.isEmpty(listBaseResult.getResult())) {
                    AppListResult appListResult = openAppService.loadOpenAppByIdsFast(listBaseResult.getResult());
                    if (!appListResult.isSuccess()) {
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "加载可见应用失败"); // ignoreI18n
                    }
                    openAppDOs.addAll(appListResult.getResult());
                }
            }
        }
        List<Map<String, Object>> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(openAppDOs)) {
            // 添加排序
            Collections.sort(openAppDOs, (OpenAppDO o1, OpenAppDO o2) -> {
                long a = (null == o1.getGmtCreate()) ? 0 : o1.getGmtCreate().getTime();
                long b = (null == o2.getGmtCreate()) ? 0 : o2.getGmtCreate().getTime();
                return a == b ? 0 : (a < b ? 1 : -1);
            });
            //用来过滤重复app
            HashSet<String> appListSet = new HashSet<>();
            List<String> appIds = openAppDOs.stream().map(OpenAppDO::getAppId).collect(Collectors.toList());
            Map<String, String> appIdIconUrlMap = batchQueryAppIconUrl(appIds, IconType.WEB);

            openAppDOs.forEach(app -> {
                //只出自定义应用.
                if (AppType.CUSTOM_APP.value() == app.getAppType() && appListSet.add(app.getAppId())) {
                    Map<String, Object> appData = new HashMap<String, Object>();
                    appData.put("appId", app.getAppId());
                    appData.put("appName", app.getAppName());
                    appData.put("status", app.getStatus());
                    appData.put("isAdmin", CommonConstant.NO);//判断是否是管理员（应用管理员或企业管理员），用于跳转应用的主页.
                    if (isFsAdmin || appIdByAppAdminLoad.contains(app.getAppId())) {
                        appData.put("isAdmin", CommonConstant.YES);
                    }
                    String iconUrl = appIdIconUrlMap.get(app.getAppId());
                    if (StringUtils.isNotBlank(iconUrl)) {
                        appData.put("appLogoUrl", iconUrl);
                    }
                    result.add(appData);
                }
            });
        }
        return result;
    }

    @Override
    public List<ServiceFAQVO> queryServiceDashboardFAQs(Integer categoryId, Integer faqCount) {
        List<ServiceFAQVO> result = new ArrayList<>();
        com.facishare.open.common.result.BaseResult<List<ArticleVO>> articleResult =
                articleService.getArticlesByCategoryId(categoryId, faqCount);

        if (!articleResult.isSuccess() || CollectionUtils.isEmpty(articleResult.getResult())) {
            logger.error("getArticlesByCategoryId failed, categoryId[{}], count[{}], result[{}]",
                    categoryId, faqCount, articleResult);
            return result;
        }


        articleResult.getResult().forEach(article -> {
            ServiceFAQVO vo = new ServiceFAQVO();
            vo.setTitle(article.getTitle());
            vo.setUrl(BizCommonUtils.getArticleTargetUrl(article.getArticleId()));
            result.add(vo);
        });
        return result;
    }

    @Override
    public Map<String, String> queryServiceDashboardNotice() {
        Map<String, String> noticeMap = new HashMap<>();
        noticeMap.put("noticeName", "");
        noticeMap.put("noticeTargetUrl", "");
        try {
            com.facishare.open.common.result.BaseResult<List<ArticleVO>> latestAnnouncement = articleService.getLatestAnnouncements(1);
            if (!latestAnnouncement.isSuccess() || CollectionUtils.isEmpty(latestAnnouncement.getResult())) {
                logger.warn("getLatestAnnouncements failed, result[{}]", latestAnnouncement);
            } else {
                ArticleVO articleVO = latestAnnouncement.getResult().get(0);
                noticeMap.put("noticeName", articleVO.getTitle());
                noticeMap.put("noticeTargetUrl", BizCommonUtils.getArticleTargetUrl(articleVO.getArticleId()));
            }
        } catch (Exception e) {
            logger.error("getLatestAnnouncements error.", e);
        }
        return noticeMap;
    }

    @Override
    public ServiceDashboardStatisticsVO queryServiceDashboardStatistics(String appId, String fsEa) {
        ServiceDashboardStatisticsVO statisticsVO = new ServiceDashboardStatisticsVO();
        statisticsVO.setAppId(appId);

        com.facishare.open.common.result.BaseResult<ServiceDashboardStatisticsDO> statisticsResult =
                serviceDashboardStatisticsService.queryDashboardStatisticsByAppId(appId, fsEa);
        if (!statisticsResult.isSuccess()) {
            logger.warn("failed call serviceDashboardStatisticsService.queryDashboardStatisticsByAppId, " +
                    "appId[{}], statisticsResult[{}]", appId, statisticsResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, statisticsResult, "查询服务号统计数据异常"); // ignoreI18n
        }
        ServiceDashboardStatisticsDO statisticsDO = statisticsResult.getResult();

        // 如果暂无统计数据，则返回默认值
        if (null == statisticsDO) {
            statisticsVO.setDataEndTime(getDefaultDataEndTime());
            statisticsVO.setViewCount(0);
            statisticsVO.setCommentCount(0);
            statisticsVO.setReceiveMsgCount(0);
            statisticsVO.setSendMsgCount(0);
            statisticsVO.setLikeCount(0);
            statisticsVO.setReadCount(0);
        } else {
            statisticsVO.setViewCount(statisticsDO.getViewCount());
            statisticsVO.setCommentCount(statisticsDO.getCommentCount());
            statisticsVO.setReceiveMsgCount(statisticsDO.getReceiveMsgCount());
            statisticsVO.setSendMsgCount(statisticsDO.getSendMsgCount());
            statisticsVO.setLikeCount(statisticsDO.getLikeCount());
            statisticsVO.setReadCount(statisticsDO.getReadCount());
            statisticsVO.setDataEndTime(statisticsDO.getDataEndTime().getTime());
        }

        return statisticsVO;
    }

    /**
     * 默认时间取当前时间昨天晚上23：59：59秒.
     *
     * @return
     */
    private long getDefaultDataEndTime() {
        LocalDateTime defaultDateTime = LocalDate.now().atStartOfDay().minusSeconds(1);
        return defaultDateTime.toInstant(ZonedDateTime.now().getOffset()).toEpochMilli();
    }

    @Override
    public Map<String, Object> queryServiceFeatureList(FsUserVO user, String appId, Integer appType) {
        Map<String, Object> result = new HashMap<>();

        List<String> basicList = new ArrayList<>();
        basicList.add(ServiceFeatureTypeEnum.SEND_MSG.getType());
        basicList.add(ServiceFeatureTypeEnum.USER_SESSIONS.getType());
        basicList.add(ServiceFeatureTypeEnum.SEND_MSG_HISTORY.getType());
        basicList.add(ServiceFeatureTypeEnum.MATERIAL_LIBRARY.getType());
        basicList.add(ServiceFeatureTypeEnum.SETTING.getType());
        result.put("basic", basicList);

        List<String> extensionList = new ArrayList<>();
        //只有自建服务号显示自定义菜单
        OpenAppDO openAppDO = loadAppBrief(appId);//todo 这样做不太好，但是本着不想改接口的目的才会又查一遍，可优化
        if (AppType.SERVICE.value() == openAppDO.getAppType() || AppType.LINK_SERVICE.value() == openAppDO.getAppType()) {
            Integer customMenuStatus = customMenuManager.queryCustomMenuStatusByAppId(user, appId);
            if (null != customMenuStatus && CommonConstant.YES == customMenuStatus) {
                extensionList.add(ServiceFeatureTypeEnum.CUSTOM_MENU.getType());
            }
        }
        int autoReplyStatus = msgAutoReplyService.queryAutoReplySwitch(user.getEnterpriseAccount(), appId);
        if (CommonConstant.YES == autoReplyStatus) {
            extensionList.add(ServiceFeatureTypeEnum.AUTO_REPLY.getType());
        }

        GetCustomServiceSwitchResult customServiceSwitchResult = msgAutoReplyService.queryCustomServiceReplySwitch(user.getEnterpriseAccount(), appId);
        if (!customServiceSwitchResult.isSuccess()) {
            logger.warn("failed to call queryCustomServiceReplySwitch, enterpriseAccount[{}], appId[{}}, result[{}}",
                    user.getEnterpriseAccount(), appId, customServiceSwitchResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询多客服开关失败"); // ignoreI18n
        }
        if (CommonConstant.YES == customServiceSwitchResult.getReplySwitch()) {
            extensionList.add(ServiceFeatureTypeEnum.CUSTOMER_SERVICE.getType()); //移动客服
        }
/*
        老工单下线
        Integer workOrderStatus = serviceNumberManager.queryWorkOrderStatus(user, new ServiceNumberForm(appId));
        if (CommonConstant.YES == workOrderStatus) {
            //在灰度里面才显示
            if (BizCommonUtils.isAllowShowByModelKey(CAN_SHOW_OLD_WORK_ORDER_MODEL_KEY, user.getEnterpriseAccount())) {
                extensionList.add(ServiceFeatureTypeEnum.WORK_ORDER.getType()); //工单
            }
        }*/

        //外联服务号1.0 无服务工单、问卷和文章分类
        if (!Objects.equals(AppType.LINK_SERVICE.value(), appType)) {


            Integer workOrderPaasStatus = serviceNumberManager.queryWorkOrderPaasStatus(user, new ServiceNumberForm(appId));
            if (CommonConstant.YES == workOrderPaasStatus) {
                extensionList.add(ServiceFeatureTypeEnum.WORK_ORDER_PAAS.getType()); //服务工单
            }

            if (ConfigCenter.grayToEserviceWorkOrder(user.getEa())) {
                // 判断是否开启
                boolean openStatus = eserviceManager.isOpenEserviceWorkOrder(user.getEa(), user.getUserId(), appId);
                if (openStatus) {
                    extensionList.add(ServiceFeatureTypeEnum.ESERVICE_WORK_ORDER.getType());
                }
            }

            Integer questionnaireStatus = serviceNumberManager.queryQuestionnaireStatus(user, new ServiceNumberForm(appId));
            if (CommonConstant.YES == questionnaireStatus) {
                extensionList.add(ServiceFeatureTypeEnum.QUESTIONNAIRE.getType()); //问卷
            }
        }

        //互联服务号才有：审批单    审批单已下线
//        if(Objects.equals(AppType.LINK_SERVICE.value(), appType)){
//            Integer approvalStatus = serviceNumberManager.queryApprovalStatus(user, new ServiceNumberForm(appId));
//            if (CommonConstant.YES == approvalStatus) {
//                extensionList.add(ServiceFeatureTypeEnum.APPROVAL.getType()); //审批单
//            }
//        }

        // 文章评论
        com.facishare.open.common.result.BaseResult<ServiceCommentSwitchEnum> commentSwitchResult =
                serviceCommentSwitchService.queryCommentSwitchByAppId(user.getEnterpriseAccount(), appId);
        if (!commentSwitchResult.isSuccess()) {
            logger.warn("failed to call serviceCommentSwitchService.queryCommentSwitchByAppId, appId[{}], result[{}]", appId, commentSwitchResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询评论管理开关失败"); // ignoreI18n
        }
        if (ServiceCommentSwitchEnum.ON.equals(commentSwitchResult.getResult())) {
            extensionList.add(ServiceFeatureTypeEnum.ARTICLE_COMMENT.getType());
        }
        // 文章分类
        //外联服务号1.0 无服务工单、问卷和文章分类
        if (!Objects.equals(AppType.LINK_SERVICE.value(), appType)) {
            com.facishare.open.common.result.BaseResult<ArticleCategorySwitchEnum> articleCategorySwitchEnumBaseResult =
                    articleCategorySwitchService.queryCategorySwitchByFsEaAndAppId(user.getEnterpriseAccount(), appId);
            if (!articleCategorySwitchEnumBaseResult.isSuccess()) {
                logger.warn("failed to call articleCategorySwitchService.queryCategorySwitchByFsEaAndAppId, appId[{}], result[{}]", appId, articleCategorySwitchEnumBaseResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询分类开关失败"); // ignoreI18n
            }
            if (ArticleCategorySwitchEnum.ON.equals(articleCategorySwitchEnumBaseResult.getResult())) {
                extensionList.add(ServiceFeatureTypeEnum.ARTICLE_CATEGORY.getType());
            }
        }
//        //只有自建服务号显示添加功能
//        if (AppType.SERVICE.value() == openAppDO.getAppType()) {
//            extensionList.add(ServiceFeatureTypeEnum.ADDING.getType());
//        }

        // 智能机器人
        if (ConfigCenter.grayToOnlineServiceAssistant(user.getEa())) {
            extensionList.add(ServiceFeatureTypeEnum.ONLINE_SERVICE_ASSISTANT.getType());
        }

        //临时放开
        extensionList.add(ServiceFeatureTypeEnum.ADDING.getType());
        result.put("extensions", extensionList);
        return result;
    }

    @Override
    public AppOnOffEnum getAppOnOffStatus(String appId, String fsEa) {
        com.facishare.open.common.result.BaseResult<AppOnOffEnum> result = null;
        //接口出问题的情况下默认返回打开
        try {
            result = appOnOffService.queryStatus(appId, fsEa);
        } catch (Exception e) {
            logger.error("call appOnOffService.queryStatus result in error.appId={}, fsEa={},result={}", appId, fsEa,
                    result, e);
            return AppOnOffEnum.ON;
        }
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            logger.warn("failed to call appOnOffService.queryStatus,appId={}, fsEa={},result={}", appId, fsEa, result);
            return AppOnOffEnum.ON;
        }
    }

    @Override
    public PayStatus getPayStatus(String appId, String fsEa) {
        final com.facishare.open.common.result.BaseResult<QuotaVo> quotaVoResult = quotaService.queryQuotaInfo(fsEa, appId);
        if (!quotaVoResult.isSuccess()) {
            logger.warn("fail to call quotaService.queryQuotaInfo, fsEa={}, appId={}, result={}",
                    fsEa, appId, quotaVoResult);
            throw new BizException(quotaVoResult);
        }
        return quotaVoResult.getResult().getPayStatus();
    }

    @Override
    public void checkAppOnOffStatus(String appId, String fsEa) {
        AppOnOffEnum appOnOffEnum = getAppOnOffStatus(appId, fsEa);
        if (appOnOffEnum.getValue().equals(AppOnOffEnum.OFF.getValue())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, ConfigCenter.APP_OFF_DIALOG_BOX);
        }
    }

    @Override
    public Map<String, Integer> queryComponentUsersCount(FsUserVO fsUserVO, String componentId) {
        BaseResult<Map<String, Integer>> usersCountResult = openFsUserAppViewService.queryComponentUsersCount(fsUserVO, componentId);
        if (usersCountResult.isSuccess()) {
            logger.info("success to call openFsUserAppViewService.queryComponentUsersCount.fsUserVO={},componentId={},result={}",
                    fsUserVO, componentId, usersCountResult);
            return usersCountResult.getResult();
        }
        return Collections.emptyMap();
    }

    @Override
    public String queryAppIconUrl(String appIdOrComponentId, IconType type) {
        BaseResult<String> iconUrlResult = appIconService.queryAppIconUrl(appIdOrComponentId, type);
        if (!iconUrlResult.isSuccess()) {
            logger.warn("queryAppIconUrl failed, appIdOrComponentId[{}], type[{}], result[{}] : " + appIdOrComponentId, type, iconUrlResult);
        }
        return iconUrlResult.getResult();
    }

    @Override
    public Map<String, String> batchQueryAppIconUrl(List<String> appIds, IconType type) {
        BaseResult<Map<String, String>> iconUrlResult = appIconService.batchQueryAppIconUrl(appIds, type);
        logger.debug("batchQueryAppIconUrl. iconUrlResult[{}]", iconUrlResult);
        if (!iconUrlResult.isSuccess()) {
            logger.warn("batchQueryAppIconUrl failed, appIds[{}], type[{}], result[{}] : " + appIds, type, iconUrlResult);
            return Maps.newHashMap();
        }
        return iconUrlResult.getResult();
    }

    @Override
    public int saveMaterialAccessPermission(FsUserVO user, String appId, Integer materialAccessPermission) {
        if (null == PermissionEnum.getByCode(materialAccessPermission)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "访问权限值不正确"); // ignoreI18n
        }
        MaterialAccessPermissionVO materialAccessPermissionVO = new MaterialAccessPermissionVO();
        materialAccessPermissionVO.setAppId(appId);
        materialAccessPermissionVO.setFsEa(user.getEnterpriseAccount());
        materialAccessPermissionVO.setPermission(materialAccessPermission);
        materialAccessPermissionVO.setModifiedUser(FsUserVO.toFsUserString(user));
        materialAccessPermissionService.saveOrModifyPermission(materialAccessPermissionVO);
        com.facishare.open.common.result.BaseResult<Integer> result =
                materialAccessPermissionService.saveOrModifyPermission(materialAccessPermissionVO);
        if (!result.isSuccess()) {
            logger.warn("failed to call materialAccessPermissionService.saveOrModifyPermission, materialAccessPermissionVO[{}], result[{}]",
                    materialAccessPermissionVO, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "保存素材访问权限失败"); // ignoreI18n
        }
        return result.getResult();
    }

    /**
     * 查询应用组件的信息
     *
     * @param user
     * @param componentId
     * @return
     */
    @Override
    public CustomComponentVO queryComponentInfo(FsUserVO user, String componentId) {
        if (null == user || StringUtils.isBlank(componentId)) {
            return null;
        }
        //获取应用所有组件
        BaseResult<OpenAppComponentDO> openComponentResult = openAppComponentService.loadOpenAppComponentById(user, componentId);
        if (!openComponentResult.isSuccess() || null == openComponentResult.getResult()) {
            logger.warn("failed to call openAppComponentService.loadOpenAppComponentById, user=[{}], componentId={}, openComponentResult={}",
                    user, componentId, openComponentResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "加载应用的组件错误"); // ignoreI18n
        }
        OpenAppComponentDO componentDO = openComponentResult.getResult();
        Integer componentType = componentDO.getComponentType();
        // 返回customComponentVO数据
        CustomComponentVO customComponentVO = new CustomComponentVO();
        customComponentVO.setAppId(componentDO.getAppId());
        customComponentVO.setComponentId(componentId);
        customComponentVO.setComponentType(componentType);

        final com.facishare.open.oauth.result.AppResult componentInfo =
                appService.getComponentInfo(null, null, componentId);
        if (!componentInfo.isSuccess() || null == componentInfo) {
            logger.warn("failed to appService.getComponentInfo: componentId={}", componentId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询组件信息失败"); // ignoreI18n
        }
        if (componentDO.getComponentType() == AppComponentTypeEnum.WEB.getType()) {
            customComponentVO.setLoginUrl(componentInfo.getCallBackWebloginUrl());
        } else {
            customComponentVO.setLoginUrl(componentInfo.getCallBackloginUrl());
        }

        final BaseResult<AppViewDO> appViewResult =
                openFsUserAppViewService.loadAppViewByType(user, componentId,
                        AppComponentTypeEnum.getByCode(componentType));
        if (!appViewResult.isSuccess() || null == appViewResult.getResult()) {
            logger.warn("failed to call loadAppViewByType, user=[{}], componentId={}, componentType={}",
                    user, componentId, AppComponentTypeEnum.getByCode(componentType));
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询组件可见范围失败"); // ignoreI18n
        }
        customComponentVO.setComponentName(componentDO.getComponentName());
        customComponentVO.setComponentLogo(queryAppIconUrl(componentId, IconType.WEB));
        customComponentVO.setView(appViewResult.getResult().getJsonString());
        return customComponentVO;
    }
}
