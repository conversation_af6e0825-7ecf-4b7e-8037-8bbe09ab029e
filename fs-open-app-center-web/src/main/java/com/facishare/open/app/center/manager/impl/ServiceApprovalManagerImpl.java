package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.Circle;
import com.facishare.open.app.center.api.model.Employee;
import com.facishare.open.app.center.api.model.OpenAppCreateTemplateDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.ServiceApprovalStatusEnum;
import com.facishare.open.app.center.api.model.vo.ServiceApprovalVO;
import com.facishare.open.app.center.api.service.OpenAppAddressBookCircleService;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.api.service.OpenAppCreateTemplateService;
import com.facishare.open.app.center.api.service.ServiceApprovalService;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.AppCreateTemplateManager;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.ServiceApprovalManager;
import com.facishare.open.app.center.manager.WebAuthManager;
import com.facishare.open.app.center.model.AbleCreateServiceVO;
import com.facishare.open.app.center.model.CustomAppCreateForm;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.model.SendOfficeMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.SendMessageService;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * impl.
 * Created by zenglb on 2016/3/22.
 */
@Service
public class ServiceApprovalManagerImpl implements ServiceApprovalManager {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private ServiceApprovalService serviceApprovalService;

    @Resource
    private OpenAppCreateTemplateService openAppCreateTemplateService;

    @Resource
    private AppCreateTemplateManager appCreateTemplateManager;

    @Resource
    private AppManager appManager;

    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;

    @Resource
    private OpenAppAddressBookCircleService openAppAddressBookCircleService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private WebAuthManager webAuthManager;

    public Boolean hasResubmitted(String preApprovalId){
        BaseResult<Boolean> baseResult = serviceApprovalService.queryByPreApprovalId(preApprovalId);
        if (!baseResult.isSuccess()) {
            logger.error("call serviceApprovalService.queryByPreApprovalIdAndStatus fail.preApprovalId[{}], baseResult[{}]",
                    preApprovalId, baseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "验证是否存已经重新提交失败"); // ignoreI18n
        }
        return baseResult.getResult();
    }



    @Override
    public Boolean existsName(String fsEa, String serviceName) {
        BaseResult<Boolean> baseResult = serviceApprovalService.existsName(fsEa, serviceName);
        if (!baseResult.isSuccess()) {
            logger.error("call serviceApprovalService.existsName fail.fsEa[{}], serviceName[{}], baseResult[{}]",
                    fsEa, serviceName, baseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "验证是否存在同名应用失败"); // ignoreI18n
        }
        return baseResult.getResult();
    }

    @Override
    public ServiceApprovalVO queryApproval(FsUserVO fsUserVO, String approvalId) {
        BaseResult<ServiceApprovalVO> baseResult = serviceApprovalService.queryApproval(approvalId);
        if (!baseResult.isSuccess()) {
            logger.error("call serviceApprovalService.queryApproval fail.approvalId[{}], baseResult[{}]",
                    approvalId, baseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult, "查询服务号申请失败"); // ignoreI18n
        }
        ServiceApprovalVO serviceApprovalVO = baseResult.getResult();
        if (!serviceApprovalVO.getFsEa().equals(fsUserVO.getEnterpriseAccount())){
            logger.warn("no authority. baseResult[{}]", approvalId, baseResult);
            throw new BizException(AjaxCode.NO_AUTHORITY, baseResult, "无权限查看"); // ignoreI18n
        }
        //企业管理员
        List<Integer> fsAdmins = webAuthManager.queryAdminIds(fsUserVO.getEnterpriseAccount());

        serviceApprovalVO.setServiceViewInfo(getServiceViewInfo(serviceApprovalVO.getFsEa(), serviceApprovalVO
                .getServiceView(), fsAdmins));
        serviceApprovalVO.setServiceAdminsInfo(getEmployeeInfo(serviceApprovalVO.getFsEa(), serviceApprovalVO.getServiceAdmins()));
        serviceApprovalVO.setCopiesInfo(getEmployeeInfo(serviceApprovalVO.getFsEa(), serviceApprovalVO.getCopies()));
        serviceApprovalVO.setCreatorUserName(getUserName(new FsUserVO(serviceApprovalVO.getFsEa(), serviceApprovalVO
                .getCreatorUserId())));
        serviceApprovalVO.setCreatorUserDepartmentName(getDepartmentNames(new FsUserVO(serviceApprovalVO.getFsEa(), serviceApprovalVO
                .getCreatorUserId()), fsAdmins));
        if (serviceApprovalVO.getApprovalUserId() != null) {
            serviceApprovalVO.setApprovalUserName(getUserName(new FsUserVO(serviceApprovalVO.getFsEa(), serviceApprovalVO
                    .getApprovalUserId())));
        }
        return serviceApprovalVO;
    }


    @Override
    public Map<String, Object> createApprovalByForm(FsUserVO fsUserVO, CustomAppCreateForm form) {
        Map<String, Object> result = new HashMap<>();
        AppCenterEnum.AppType appTypeEnum;

        BaseResult<OpenAppCreateTemplateDO> openAppCreateTemplateDOResult = openAppCreateTemplateService.loadById(form.getTemplateId());
        if (!openAppCreateTemplateDOResult.isSuccess()) {
            logger.error("openAppCreateTemplateService.loadById failed. fsUserVO[{}], form[{}], " +
                    "openAppCreateTemplateDOResult[{}]", fsUserVO, form, openAppCreateTemplateDOResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, openAppCreateTemplateDOResult, "服务号创建模板不存在."); // ignoreI18n
        }

        // 区分互联服务号和企业服务号
        if (openAppCreateTemplateDOResult.getResult().getTemplateType().equals(AppCenterEnum.AppType.LINK_SERVICE.value())) {
            appTypeEnum = AppCenterEnum.AppType.LINK_SERVICE;
        } else {
            appTypeEnum = AppCenterEnum.AppType.SERVICE;
        }
        //验证是否可以创建服务号
        AbleCreateServiceVO ableCreateServiceVO = appManager.isAbleToCreateService(fsUserVO, appTypeEnum);
        if (!ableCreateServiceVO.getAbleCreateService()) {
            logger.error("appManager.isAbleToCreateService failed. fsUserVO[{}], ableCreateServiceVO[{}]", fsUserVO, ableCreateServiceVO);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, ableCreateServiceVO.getUnableMsg());
        }
        BaseResult<Long> countBaseResult = serviceApprovalService.countByCreatorUser(fsUserVO);
        //第一次次申请
        Boolean isFirstApproval = true;
        if (countBaseResult.isSuccess()){
            isFirstApproval = countBaseResult.getResult() == 0 ;
        }
        //企业管理员
        List<Integer> fsAdmins = webAuthManager.queryAdminIds(fsUserVO.getEnterpriseAccount());
        List<Integer> copies = new ArrayList<>(Arrays.asList(form.getCopies()));

        ServiceApprovalVO serviceApprovalVO = new ServiceApprovalVO();
        serviceApprovalVO.setPreApprovalId(form.getPreApprovalId());
        serviceApprovalVO.setServiceLogo(form.getAppLogo());
        serviceApprovalVO.setServiceName(form.getAppName());
        serviceApprovalVO.setServiceDesc(form.getAppDesc());
        serviceApprovalVO.setServiceAdmins(Arrays.asList(form.getAppAdmins()));
        serviceApprovalVO.setServiceView(form.getServiceView());
        serviceApprovalVO.setTemplateId(form.getTemplateId());
        serviceApprovalVO.setFsEa(fsUserVO.getEnterpriseAccount());
        serviceApprovalVO.setCopies(copies);
        serviceApprovalVO.setCreatorUserId(fsUserVO.getUserId());
        serviceApprovalVO.setStatus(ServiceApprovalStatusEnum.PENDING.getCode());
        serviceApprovalVO.setGmtCreate(System.currentTimeMillis());
        serviceApprovalVO.setGmtModified(System.currentTimeMillis());
        serviceApprovalVO.setApplyReasons(form.getApplyReasons());
        BaseResult<String> createBaseResult = serviceApprovalService.create(serviceApprovalVO);
        if (!createBaseResult.isSuccess()) {
            logger.error("serviceApprovalService.create failed. serviceApprovalVO[{}], createBaseResult[{}]",
                    serviceApprovalVO, createBaseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "创建服务号申请失败"); // ignoreI18n
        }
        //时间
        String now = new SimpleDateFormat("M月d日").format(new Date()); // ignoreI18n
        //服务号管理员名称
        String serviceAdminsNames = getServiceAdminsNames(fsUserVO.getEnterpriseAccount(), Arrays.asList(form.getAppAdmins()));
        //可见范围名称
        String serviceViewNames = getServiceViewNames(fsUserVO.getEnterpriseAccount(), form.getServiceView(), fsAdmins);
        //获取申请人和他的部门名称
        String creatorUserDepartmentNames = getCreatorUserDepartmentNames(fsUserVO, fsAdmins);
        //url查看详情
        String url = String.format(ConfigCenter.SERVICE_APPROVAL_URL, createBaseResult.getResult());
        //发送给申请人的模版消息
        String officeVOJson2Creator = String.format(ConfigCenter.SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE, ConfigCenter
                        .SERVICE_APPROVAL_CREATE_TITLE, now, form.getAppName(), form.getAppDesc(), serviceAdminsNames,
                serviceViewNames, creatorUserDepartmentNames, url);
        sendTemplateMessage(fsUserVO.getEnterpriseAccount(), Arrays.asList(fsUserVO.getUserId()), officeVOJson2Creator);
        //发送给抄送人的模版消息
        copies.removeAll(fsAdmins);//不要发抄送消息给企业管理员
        copies.remove(fsUserVO.getUserId());//不要发抄送消息给自己
        if (!CollectionUtils.isEmpty(copies)) {
            String officeVOJson2Copies = String.format(ConfigCenter.SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE, ConfigCenter.SERVICE_APPROVAL_COPY_TITLE,
                    now, form.getAppName(), form.getAppDesc(), serviceAdminsNames, serviceViewNames, creatorUserDepartmentNames, url);
            sendTemplateMessage(fsUserVO.getEnterpriseAccount(), copies, officeVOJson2Copies);
        }
        //发送给企业管理员的模版消息
        String officeVOJson2FsAdmins = String.format(ConfigCenter.SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE, ConfigCenter.SERVICE_APPROVAL_TITLE,
                now, form.getAppName(), form.getAppDesc(), serviceAdminsNames, serviceViewNames, creatorUserDepartmentNames, url);
        sendTemplateMessage(fsUserVO.getEnterpriseAccount(), fsAdmins, officeVOJson2FsAdmins);
        BaseResult<ServiceApprovalVO> approvalVOBaseResult = serviceApprovalService.queryApproval(createBaseResult.getResult());
        if (!approvalVOBaseResult.isSuccess()){
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询申请失败"); // ignoreI18n
        }
        result.put("approvalId", createBaseResult.getResult());
        result.put("serviceApproval", queryApproval(fsUserVO, createBaseResult.getResult()));
        result.put("isFirstApproval", isFirstApproval);
        return result;
    }

    @Override
    public void throughApproval(FsUserVO fsUserVO, String lang, String approvalId, String approvalMsg) {
        BaseResult<ServiceApprovalVO> baseResult = serviceApprovalService.handleApproval(fsUserVO, approvalId,
                ServiceApprovalStatusEnum.APPROVAL.getCode(), approvalMsg);
        if (!baseResult.isSuccess()) {
            logger.error("serviceApprovalService.handleApproval failed. fsUserVO[{}], approvalId[{}], baseResult[{}]", fsUserVO,
                    approvalId, baseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult.getErrDescription());
        }
        ServiceApprovalVO serviceApprovalVO = baseResult.getResult();
        if (!serviceApprovalVO.getFsEa().equals(fsUserVO.getEnterpriseAccount())){
            logger.warn("no authority. baseResult[{}]", approvalId, baseResult);
            throw new BizException(AjaxCode.NO_AUTHORITY, baseResult, "无权限查看"); // ignoreI18n
        }
        CustomAppCreateForm form = new CustomAppCreateForm();
        form.setAppLogo(serviceApprovalVO.getServiceLogo());
        form.setAppName(serviceApprovalVO.getServiceName());
        form.setAppDesc(serviceApprovalVO.getServiceDesc());
        form.setAppAdmins(serviceApprovalVO.getServiceAdmins().toArray(new Integer[0]));
        form.setTemplateId(serviceApprovalVO.getTemplateId());
        form.setServiceView(serviceApprovalVO.getServiceView());

        appCreateTemplateManager.createCustomAppByForm(new FsUserVO(serviceApprovalVO.getFsEa(),
                serviceApprovalVO.getCreatorUserId()), lang, form, AppCenterEnum.AppType.SERVICE);

        //企业管理员
        List<Integer> fsAdmins = webAuthManager.queryAdminIds(serviceApprovalVO.getFsEa());
        //时间
        String now = new SimpleDateFormat("M月d日").format(new Date()); // ignoreI18n
        //服务号管理员名称
        String serviceAdminsNames = getServiceAdminsNames(serviceApprovalVO.getFsEa(), serviceApprovalVO.getServiceAdmins());
        //可见范围名称
        String serviceViewNames = getServiceViewNames(serviceApprovalVO.getFsEa(), serviceApprovalVO.getServiceView(), fsAdmins);
        //获取申请人和他的部门名称
//        String creatorUserCircleNames = getCreatorUserDepartmentNames(new FsUserVO(serviceApprovalVO.getFsEa(), serviceApprovalVO
//                .getCreatorUserId()), fsAdmins);
        //审批人名称
//        String approvalUserName = getUserName(fsUserVO);
        //url查看详情
        String url = String.format(ConfigCenter.SERVICE_APPROVAL_URL, serviceApprovalVO.getApprovalId());
        //发送给申请人的模版消息
        String officeVOJson2Creator = String.format(ConfigCenter.SERVICE_APPROVAL_NOTICE_TEMPLATE, now, serviceApprovalVO
                        .getServiceName(), serviceApprovalVO.getServiceDesc(), serviceAdminsNames, serviceViewNames, url);
        sendTemplateMessage(serviceApprovalVO.getFsEa(), Arrays.asList(serviceApprovalVO.getCreatorUserId()),
                officeVOJson2Creator);
    }

    @Override
    public void unThroughUnApproval(FsUserVO fsUserVO, String approvalId, String approvalMsg) {
        BaseResult<ServiceApprovalVO> baseResult = serviceApprovalService.handleApproval(fsUserVO, approvalId,
                ServiceApprovalStatusEnum.REJECT.getCode(), approvalMsg);
        if (!baseResult.isSuccess()) {
            logger.error("serviceApprovalService.handleApproval failed. fsUserVO[{}], approvalId[{}]", fsUserVO, approvalId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, baseResult.getErrDescription());
        }
        //企业管理员
        List<Integer> fsAdmins = webAuthManager.queryAdminIds(fsUserVO.getEnterpriseAccount());
        ServiceApprovalVO serviceApprovalVO = baseResult.getResult();
        if (!serviceApprovalVO.getFsEa().equals(fsUserVO.getEnterpriseAccount())){
            logger.warn("no authority. baseResult[{}]", approvalId, baseResult);
            throw new BizException(AjaxCode.NO_AUTHORITY, baseResult, "无权限查看"); // ignoreI18n
        }
        //时间
        String now = new SimpleDateFormat("M月d日").format(new Date()); // ignoreI18n
        //服务号管理员名称
        String serviceAdminsNames = getServiceAdminsNames(serviceApprovalVO.getFsEa(), serviceApprovalVO.getServiceAdmins());
        //可见范围名称
        String serviceViewNames = getServiceViewNames(serviceApprovalVO.getFsEa(), serviceApprovalVO.getServiceView(), fsAdmins);
        //获取申请人和他的部门名称
//        String creatorUserCircleNames = getCreatorUserDepartmentNames(new FsUserVO(serviceApprovalVO.getFsEa(),
//                serviceApprovalVO.getCreatorUserId()), fsAdmins);
        //审批人名称
//        String approvalUserName = getUserName(fsUserVO);
        //url查看详情
        String url = String.format(ConfigCenter.SERVICE_APPROVAL_URL, serviceApprovalVO.getApprovalId());
        //发送给申请人的模版消息
        String officeVOJson2Creator = String.format(ConfigCenter.SERVICE_REJECT_NOTICE_TEMPLATE, now, serviceApprovalVO
                        .getServiceName(), serviceApprovalVO.getServiceDesc(),
                serviceAdminsNames, serviceViewNames, approvalMsg, url);
        sendTemplateMessage(fsUserVO.getEnterpriseAccount(), Arrays.asList(serviceApprovalVO.getCreatorUserId()),
                officeVOJson2Creator);
    }

    /**
     * 发送模版消息
     *
     * @param fsEa
     * @param toUserList
     * @param officeVOJson
     */
    private void sendTemplateMessage(String fsEa, List<Integer> toUserList, String officeVOJson) {
        SendOfficeMessageVO msgVO = new SendOfficeMessageVO();
        msgVO.setAppId(ConfigCenter.SYSTEM_MSG_FORSERVICE_APPID);
        msgVO.setEnterpriseAccount(fsEa);
        msgVO.setToUserList(toUserList);
        msgVO.setType(MessageTypeEnum.TEMPLATE);
        msgVO.setPostId(UUID.randomUUID().toString());
        msgVO.setContent(officeVOJson);
        MessageResult messageResult2FsAdmins = sendMessageService.sendOfficeMessage(msgVO, MessageSendTypeEnum
                .ADMINISTRATOR_PUSH);
        if (!messageResult2FsAdmins.isSuccess()) {
            logger.error("sendOfficeMessage failed. msgVO[{}], messageResult2Copies[{}]", msgVO, messageResult2FsAdmins);
        }
    }

    /**
     * 获取服务号管理员名称
     *
     * @param fsEa
     * @param serviceAdmins
     * @return
     */
    private String getServiceAdminsNames(String fsEa, List<Integer> serviceAdmins) {
        //服务号管理员名称
        String serviceAdminsNames = null;
        StringBuffer serviceAdminNamesBuffer = new StringBuffer();
        BaseResult<List<Employee>> serviceAdminListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsEa, serviceAdmins);
        if (!serviceAdminListResult.isSuccess()) {
            logger.error("employeeService.getEmployeesNoAdminId failed. fsEa[{}], serviceAdmins[{}], serviceAdminListResult[{}]",
                    fsEa, serviceAdmins, serviceAdminListResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询服务号管理员失败"); // ignoreI18n
        }
        serviceAdminListResult.getResult().forEach(employee -> serviceAdminNamesBuffer.append(employee.getName() + "，"));
        if (serviceAdminNamesBuffer.length() > 0) {
            // TODO: 2016/11/1 这里可以使用setLength来处理 最后的逗号. add by lambo .to xiaoww. serviceAdminNamesBuffer.setLength(serviceAdminNamesBuffer.length() - 1);
            serviceAdminsNames = serviceAdminNamesBuffer.substring(0, serviceAdminNamesBuffer.length() - 1);
        }
        return serviceAdminsNames;
    }

    /**
     * 获取用户信息
     *
     * @param fsEa
     * @param employees
     * @return
     */
    private Map<Integer, String> getEmployeeInfo(String fsEa, List<Integer> employees) {
        Map<Integer, String> serviceAdminsInfo = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(employees)) {
            BaseResult<List<Employee>> serviceAdminListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsEa, employees);
            if (!serviceAdminListResult.isSuccess()) {
                logger.error("employeeService.getEmployeesNoAdminId failed. fsEa[{}], employees[{}], serviceAdminListResult[{}]",
                        fsEa, employees, serviceAdminListResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
            }
            serviceAdminListResult.getResult().forEach(employee -> serviceAdminsInfo.put(employee.getEmployeeId(), employee.getName()));
        }
        return serviceAdminsInfo;
    }

    /**
     * 获取服务号订阅范围名称
     *
     * @param fsEa
     * @param serviceView
     * @param fsAdmins
     * @return
     */
    private String getServiceViewNames(String fsEa, String serviceView, List<Integer> fsAdmins) {
        String serviceViewNames = null;
        StringBuffer serviceViewNamesBuffer = new StringBuffer();
        Map<Integer, String> departmentsMap = Maps.newHashMap();
        AppViewDO appViewDO = JsonKit.json2object(serviceView, AppViewDO.class);
        if (appViewDO != null){
            if (appViewDO.getDepartment() != null && appViewDO.getDepartment().length > 0){
                List<Integer> departmentIdList = Arrays.asList(appViewDO.getDepartment());
                if (!CollectionUtils.isEmpty(departmentIdList)) {
                    BaseResult<List<Circle>> serviceViewCircleListResult = openAppAddressBookCircleService.getCircles(fsEa, fsAdmins.get(0),
                            departmentIdList);
                    if (!serviceViewCircleListResult.isSuccess()) {
                        logger.error("circleService.getCircles failed. fsEa[{}], serviceView[{}], fsAdmins[{}], " +
                                "serviceViewCircleListResult[{}]", fsEa, serviceView, fsAdmins, serviceViewCircleListResult);
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
                    }
                    serviceViewCircleListResult.getResult().forEach(circle -> departmentsMap.put(circle.getCircleId(), circle.getName()));
                    //北京后续可能会支持对全公司（部门）的查询
                    if (departmentIdList.contains(999999) && !departmentsMap.containsKey(999999)) {
                        serviceViewNamesBuffer.append("全公司,"); // ignoreI18n
                    }
                    serviceViewCircleListResult.getResult().forEach(circle -> serviceViewNamesBuffer.append(circle.getName() + "，"));
                }
            }
            if (appViewDO.getMember() != null && appViewDO.getMember().length > 0){
                List<Integer> memberIdList = Arrays.asList(appViewDO.getMember());
                if (!CollectionUtils.isEmpty(memberIdList)) {
                    BaseResult<List<Employee>> memberListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsEa,
                            memberIdList);
                    if (!memberListResult.isSuccess()) {
                        logger.error("employeeService.getEmployeesNoAdminId failed. fsEa[{}], serviceView[{}], fsAdmins[{}], " +
                                "memberListResult[{}]", fsEa, serviceView, fsAdmins, memberListResult);
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
                    }
                    memberListResult.getResult().forEach(employee -> serviceViewNamesBuffer.append(employee.getName() + ","));
                }
            }
        }
        if (serviceViewNamesBuffer.length() > 0) {
            serviceViewNames = serviceViewNamesBuffer.substring(0, serviceViewNamesBuffer.length() - 1);
        }
        return serviceViewNames;
    }

    /**
     * 获取服务号订阅范围信息
     *
     * @param fsEa
     * @param serviceView
     * @param fsAdmins
     * @return
     */
    private List<Map<Integer, String>> getServiceViewInfo(String fsEa, String serviceView, List<Integer> fsAdmins) {
        List<Map<Integer, String>> serviceViewInfo = Lists.newArrayList();
        Map<Integer, String> departmentsMap = Maps.newHashMap();
        Map<Integer, String> membersMap = Maps.newHashMap();
        AppViewDO appViewDO = JsonKit.json2object(serviceView, AppViewDO.class);
        if (appViewDO != null){
            if (appViewDO.getDepartment() != null && appViewDO.getDepartment().length > 0){
                List<Integer> departmentIdList = Arrays.asList(appViewDO.getDepartment());
                if (!CollectionUtils.isEmpty(departmentIdList)) {
                    BaseResult<List<Circle>> serviceViewCircleListResult = openAppAddressBookCircleService.getCircles(fsEa, fsAdmins.get(0),
                            departmentIdList);
                    if (!serviceViewCircleListResult.isSuccess()) {
                        logger.error("circleService.getCircles failed. fsEa[{}], serviceView[{}], fsAdmins[{}], " +
                                "serviceViewCircleListResult[{}]", fsEa, serviceView, fsAdmins, serviceViewCircleListResult);
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
                    }
                    serviceViewCircleListResult.getResult().forEach(circle -> departmentsMap.put(circle.getCircleId(), circle.getName()));
                    //北京后续可能会支持对全公司（部门）的查询
                    if (departmentIdList.contains(999999) && !departmentsMap.containsKey(999999)) {
                        departmentsMap.put(999999, "全公司"); // ignoreI18n
                    }
                }
            }
            if (appViewDO.getMember() != null && appViewDO.getMember().length > 0){
                List<Integer> memberIdList = Arrays.asList(appViewDO.getMember());
                if (!CollectionUtils.isEmpty(memberIdList)) {
                    BaseResult<List<Employee>> memberListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsEa,
                            memberIdList);
                    if (!memberListResult.isSuccess()) {
                        logger.error("employeeService.getEmployeesNoAdminId failed. fsEa[{}], serviceView[{}], fsAdmins[{}], " +
                                "memberListResult[{}]", fsEa, serviceView, fsAdmins, memberListResult);
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
                    }
                    memberListResult.getResult().forEach(employee -> membersMap.put(employee.getEmployeeId(), employee.getName()));
                }
            }
        }
        serviceViewInfo.add(departmentsMap);
        serviceViewInfo.add(membersMap);
        return serviceViewInfo;
    }

    /**
     * 获取申请人名称和部门名称
     *
     * @param fsUserVO
     * @param fsAdmins
     * @return
     */
    private String getCreatorUserDepartmentNames(FsUserVO fsUserVO, List<Integer> fsAdmins) {
        String creatorUserDepartmentNames = null;
        StringBuffer creatorUserDepartmentNamesBuffer = new StringBuffer();
        BaseResult<List<Employee>> creatorUserListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsUserVO.getEnterpriseAccount(),
                Arrays.asList(fsUserVO.getUserId()));
        if (!creatorUserListResult.isSuccess()) {
            logger.error("employeeService.getEmployeesNoAdminId failed. fsUserVO[{}], fsAdmins[{}], creatorUserListResult[{}]",
                    fsUserVO, fsAdmins, creatorUserListResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
        }
        if (!CollectionUtils.isEmpty(creatorUserListResult.getResult())) {
            //员工姓名
            Employee createUser = creatorUserListResult.getResult().get(0);
            creatorUserDepartmentNamesBuffer.append(createUser.getName());
            BaseResult<List<Circle>> creatorUserCircleListResult = openAppAddressBookCircleService.getCircles(fsUserVO.getEnterpriseAccount(),
                    fsAdmins.get(0), createUser.getFlatCirlceIds());
            if (!creatorUserCircleListResult.isSuccess()) {
                logger.error("circleService.getCircles failed. fsUserVO[{}], fsAdmins[{}], creatorUserCircleListResult[{}]", fsUserVO,
                        fsAdmins, creatorUserCircleListResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
            }
            if (!CollectionUtils.isEmpty(creatorUserCircleListResult.getResult())) {
                creatorUserDepartmentNamesBuffer.append("（");
                creatorUserCircleListResult.getResult().forEach(circle -> creatorUserDepartmentNamesBuffer.append(circle.getName() +
                        "，"));
                creatorUserDepartmentNames = creatorUserDepartmentNamesBuffer.substring(0, creatorUserDepartmentNamesBuffer.length() - 1);
                creatorUserDepartmentNames = creatorUserDepartmentNames + "）";
            } else {
                //员工没有部门
                creatorUserDepartmentNames = creatorUserDepartmentNamesBuffer.toString();
            }
        }
        return creatorUserDepartmentNames;
    }


    /**
     * 获取用户名称
     *
     * @param fsUserVO
     * @return
     */
    private String getUserName(FsUserVO fsUserVO) {
        BaseResult<List<Employee>> userListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsUserVO.getEnterpriseAccount(),
                Arrays.asList(fsUserVO.getUserId()));
        if (!userListResult.isSuccess()) {
            logger.error("employeeService.getEmployeesNoAdminId failed. fsUserVO[{}], userListResult[{}]", fsUserVO, userListResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
        }
        return userListResult.getResult().get(0).getName();
    }


    /**
     * 部门名称
     *
     * @param fsUserVO
     * @param fsAdmins
     * @return
     */
    private String getDepartmentNames(FsUserVO fsUserVO, List<Integer> fsAdmins) {
        String departmentNames = null;
        StringBuffer departmentNamesBuffer = new StringBuffer();
        BaseResult<List<Employee>> creatorUserListResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsUserVO.getEnterpriseAccount(),
                Arrays.asList(fsUserVO.getUserId()));
        if (!creatorUserListResult.isSuccess()) {
            logger.error("employeeService.getEmployeesNoAdminId failed. fsUserVO[{}], fsAdmins[{}], creatorUserListResult[{}]",
                    fsUserVO, fsAdmins, creatorUserListResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
        }
        Employee createUser = creatorUserListResult.getResult().get(0);

        BaseResult<List<Circle>> creatorUserCircleListResult = openAppAddressBookCircleService.getCircles(fsUserVO.getEnterpriseAccount(),
                fsAdmins.get(0), createUser.getFlatCirlceIds());
        if (!creatorUserCircleListResult.isSuccess()) {
            logger.error("circleService.getCircles failed. fsUserVO[{}], fsAdmins[{}], creatorUserCircleListResult[{}]", fsUserVO,
                    fsAdmins, creatorUserCircleListResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询通讯录失败"); // ignoreI18n
        }
        if (!CollectionUtils.isEmpty(creatorUserCircleListResult.getResult())) {
            creatorUserCircleListResult.getResult().forEach(circle -> departmentNamesBuffer.append(circle.getName() +
                    "，"));
            departmentNames = departmentNamesBuffer.substring(0, departmentNamesBuffer.length() - 1);
        }
        return departmentNames;
    }
}
