package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.form.CheckLoginFrom;
import com.facishare.open.common.model.FsUserVO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 管理员的二次密码确认
 *
 * <AUTHOR>
 * @date 2015年8月3日
 */
@Controller
@RequestMapping("/open/appcenter/fsAdmin")
public class FsAdminCheckController extends BaseController{

    /**
     * 二次验证管理员的密码
     *
     * @param  user        操作人.
     * @param form 表单，
     * @return 是否成功.
     */
    @RequestMapping("/checkAdminPassword")
    @ResponseBody
    public AjaxResult checkAdminPassword(@ModelAttribute FsUserVO user,
                                         @RequestBody CheckLoginFrom form) {
        checkParamNotBlank(form, "请填写表单"); // ignoreI18n
        String password = form.getPassword();
        checkParamNotBlank(password, "请填写密码"); // ignoreI18n

        if (!webAuthManager.checkAdminPassword(user, password)) {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "验证失败"); // ignoreI18n
        }
        return SUCCESS;
    }
}
