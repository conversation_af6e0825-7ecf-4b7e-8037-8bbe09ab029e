package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.OuterServiceManager;
import com.facishare.open.app.center.manager.WechatCustomerMenuManager;
import com.facishare.open.app.center.model.WeChatCustomMenuForm;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.wechat.proxy.constants.PermissionType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @describe: 微信自定义菜单
 * @author: huyue
 * @date: 2016/11/07 11:24
 */
@Controller
@RequestMapping("/open/appcenter/wechat/customer/menu")
public class WechatCustomerMenuController extends BaseController {

    @Resource
    private WechatCustomerMenuManager wechatCustomerMenuManager;

    @Resource
    private OuterServiceManager outerServiceManager;

    /**
     * 自定义菜单查询(api创建或者官网通过网站发布)
     */
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @ResponseBody
    public AjaxResult query(@ModelAttribute FsUserVO user, @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "外联服务号ID为空"); // ignoreI18n
        checkServiceIsOn(user, appId);
        outerServiceManager.checkWechat(user, appId, null, Lists.newArrayList(PermissionType.PERMISSION_MENU));
        return new AjaxResult(wechatCustomerMenuManager.querySelfmenu(user, appId));
    }

    /**
     * 自定义菜单更新接口
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult update(@ModelAttribute FsUserVO user, @RequestBody WeChatCustomMenuForm weChatCustomMenuForm) {
        checkParamNotBlank(weChatCustomMenuForm, "更新的自定义菜单不能为空."); // ignoreI18n
        checkServiceIsOn(user, weChatCustomMenuForm.getAppId());
        return new AjaxResult(wechatCustomerMenuManager.updateMenu(user, weChatCustomMenuForm));
    }

}
