package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.vo.FeedbackVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.FeedbackService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.common.model.FsUserVO;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by xialf on 1/20/16.
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/open/appcenter/feedback")
public class FeedbackController extends BaseController {
    @Resource
    private FeedbackService feedbackService;

    private static final int CONTENT_MAX_LENGTH = 400;
    private static final int CONTACT_MAX_LENGTH = 80;

    @RequestMapping("/create")
    @ResponseBody
    public AjaxResult createFeedback(@ModelAttribute FsUserVO user, @RequestBody FeedbackVO feedbackVO) {
        if (StringUtils.isBlank(feedbackVO.getContent())
                || feedbackVO.getContent().length() > CONTENT_MAX_LENGTH) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "反馈内容不能为空, 且长度不大于" + CONTENT_MAX_LENGTH); // ignoreI18n
        }
        if (feedbackVO.getContact() != null
                && feedbackVO.getContact().length() > CONTACT_MAX_LENGTH) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "联系方式长度不能超过" + CONTACT_MAX_LENGTH); // ignoreI18n
        }



        // 防xss在前端显示时处理.java不处理.add by lambo@20160718.
        String content = feedbackVO.getContent();
        String contact = feedbackVO.getContact();
//      content = StringEscapeUtils.escapeHtml4(feedbackVO.getContent());
//      contact = StringEscapeUtils.escapeHtml4(feedbackVO.getContact());

        final BaseResult<Void> createResult =
                feedbackService.createFeedback(user, content, contact);
        if (!createResult.isSuccess()) {
            logger.warn("fail to create feedback, user={}, content={}, contact={}",
                    user, feedbackVO.getContent(), feedbackVO.getContact());
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "创建反馈失败"); // ignoreI18n
        }
        return SUCCESS;
    }

    @RequestMapping("/getByUser")
    @ResponseBody
    public AjaxResult getByUser(@ModelAttribute FsUserVO user) {
        final BaseResult<List<FeedbackVO>> listBaseResult = feedbackService.queryFeedback(user);
        if (!listBaseResult.isSuccess()) {
            logger.warn("fail to queryFeedback, user={}, result={}",
                    user, listBaseResult);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "获取反馈列表失败"); // ignoreI18n
        }
        return new AjaxResult(listBaseResult.getResult());
    }
}
