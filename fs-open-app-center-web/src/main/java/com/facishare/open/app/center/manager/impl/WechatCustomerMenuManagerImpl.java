package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.vo.OuterServiceWechatVO;
import com.facishare.open.app.center.api.model.vo.WeChatClickEventVO;
import com.facishare.open.app.center.api.service.externals.WeChatClickEventService;
import com.facishare.open.app.center.api.service.externals.WechatMsgService;
import com.facishare.open.app.center.api.service.outer.OuterServiceWechatService;
import com.facishare.open.app.center.cons.WechatCustomerMenuEnum;
import com.facishare.open.app.center.cons.WechatCustomerMenuTypeEnum;
import com.facishare.open.app.center.manager.WechatCustomerMenuManager;
import com.facishare.open.app.center.model.WeChatCustomMenuForm;
import com.facishare.open.app.center.model.WeChatMenuFormVO;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.IdGenerateUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.wechat.proxy.common.result.BaseResult;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.result.SelfMenuConfigQueryResult;
import com.facishare.wechat.proxy.model.vo.MenuInfoBaseVo;
import com.facishare.wechat.proxy.model.vo.SelfMenuCreateVo;
import com.facishare.wechat.proxy.model.vo.SelfMenuDeleteVo;
import com.facishare.wechat.proxy.model.vo.SelfmenuQueryVo;
import com.facishare.wechat.proxy.service.WechatSelfmenuService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: 外联服务号
 * User: huyue
 * Date: 2016/11/7
 */

@Service
public class WechatCustomerMenuManagerImpl implements WechatCustomerMenuManager {
    private Logger logger = LoggerFactory.getLogger(getClass());

    //一级菜单最大个数
    private static final int FIRST_LEVEL_MENU_MAX = 3;
    //二级菜单最大个数
    private static final int SECOND_LEVEL_MENU_MAX = 5;
    private static final int CLOSED = 0;

    @Resource
    private OuterServiceWechatService outerServiceWechatService;

    @Resource
    private WechatSelfmenuService wechatSelfmenuService;

    @Resource
    private WeChatClickEventService weChatClickEventService;

    @Resource
    private WechatMsgService wechatMsgService;

    private String getWxAppIdByAppId(FsUserVO user, String appId) {
        OuterServiceWechatVO entity = new OuterServiceWechatVO();
        entity.setAppId(appId);
        entity.setStatus(CommonConstant.VALID);
        entity.setFsEa(user.getEnterpriseAccount());
        com.facishare.open.common.result.BaseResult<OuterServiceWechatVO> outerServiceResult = outerServiceWechatService.queryOuterServiceWechat(entity);
        if (!outerServiceResult.isSuccess() || null == outerServiceResult.getResult()) {
            logger.warn("failed to call outerServiceWechatService.queryOuterServiceWechat, entity=[{}], outerServiceResult=[{}]", entity, outerServiceResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "无外联服务号信息"); // ignoreI18n
        }
        if (StringUtils.isBlank(outerServiceResult.getResult().getWxAppId())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "服务号无微信绑定信息"); // ignoreI18n
        }
        return outerServiceResult.getResult().getWxAppId();
    }

    /**
     * 查询通过API自定义的菜单
     *
     * @param appId
     * @return
     */
    @Override
    public List<WeChatMenuFormVO> querySelfmenu(FsUserVO user, String appId) {
        String wxAppId = getWxAppIdByAppId(user, appId);
        SelfmenuQueryVo selfmenuQueryVo = new SelfmenuQueryVo();
        selfmenuQueryVo.setWxAppId(wxAppId);
        ModelResult<SelfMenuConfigQueryResult> selfMenuConfigQueryResult = wechatSelfmenuService.querySelfConfigMenu(selfmenuQueryVo);
        if (!selfMenuConfigQueryResult.isSuccess() || null == selfMenuConfigQueryResult.getResult()) {
            logger.warn("call wechatSelfmenuService.querySelfConfigMenu failed, selfmenuQueryVo=[{}], selfMenuConfigQueryResult=[{}]",
                    selfmenuQueryVo, selfMenuConfigQueryResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询微信自定义菜单失败"); // ignoreI18n
        }
        //如果清空了菜单，则会设置为CLOSED
        if (CLOSED == selfMenuConfigQueryResult.getResult().getIsMenuOpen()){
            return null;
        }
        List<WeChatMenuFormVO> selfMenuList = new ArrayList<>();
        List<MenuInfoBaseVo.Button> buttons = selfMenuConfigQueryResult.getResult().getMenuInfo().getButtons();
        if (!CollectionUtils.isEmpty(buttons)) {
            for (int i = 0; i < buttons.size(); i++) {
                WeChatMenuFormVO vo = button2Menu(buttons.get(i), i + "", user, wxAppId);
                selfMenuList.add(vo);
            }
        }
        return selfMenuList;
    }

    private WeChatMenuFormVO button2Menu(MenuInfoBaseVo.Button button, String id, FsUserVO user, String wxAppId) {
        WeChatMenuFormVO result = new WeChatMenuFormVO();
        result.setId(id);
        result.setName(button.getName());
        result.setIsOpenResponse(1);
        result.setMaterialId(button.getMediaId());
        result.setUrl(button.getUrl());
        result.setKey(button.getKey());
        result.setSupport(false);
        result.setToolTips("暂不支持该微信公众号菜单类型，请去微信公众号调整"); // ignoreI18n
        button2MenuTypeMatch(button, result, user, wxAppId);

        // 子目录
        if (!CollectionUtils.isEmpty(button.getSubButtons())) {
            result.setChildren(new ArrayList<>());
            for (int i = 0; i < button.getSubButtons().size(); i++) {
                MenuInfoBaseVo.Button childrenButton = button.getSubButtons().get(i);
                WeChatMenuFormVO vo = button2Menu(childrenButton, id + i, user, wxAppId);
                result.getChildren().add(vo);
            }
        }
        return result;
    }

    private void button2MenuTypeMatch(MenuInfoBaseVo.Button button, WeChatMenuFormVO result, FsUserVO user, String wxAppId) {
        if (StringUtils.isBlank(button.getType())) {
            result.setSupport(true);
            result.setToolTips("");
            result.setType(WechatCustomerMenuEnum.PARENT.getCode());
        } else {
            switch (WechatCustomerMenuTypeEnum.getByType(button.getType()).get()) {
                /**
                 * web方式
                 */
                case VIEW:
                    result.setSupport(true);
                    result.setToolTips("");
                    result.setUrl(button.getUrl());
                    result.setType(WechatCustomerMenuEnum.URL.getCode());
                    if (StringUtils.isNotBlank(button.getUrl()) && button.getUrl().contains(ConfigCenter.BIND_APP_URL_SUFFIX)) {
                        result.setType(WechatCustomerMenuEnum.BIND_APP.getCode());
                    }
                    break;
                case TEXT: // Text:保存文字到value；
                    result.setSupport(true);
                    result.setToolTips("");
                    result.setType(WechatCustomerMenuEnum.TEXT.getCode());
                    result.setTxt(null == button.getValue() ? "" : button.getValue());
                    break;
                case IMG: // Img：保存mediaID到value
                    result.setType(WechatCustomerMenuEnum.IMG.getCode());
                    result.setMaterialId(button.getValue());
                    break;
                case NEWS: //  News：保存图文消息到news_info，同时保存mediaID到value
                    result.setType(WechatCustomerMenuEnum.NEWS.getCode());
                    result.setMaterialId(button.getValue());
                    break;
                case VIDEO: //  Video：保存视频下载链接到value
                    result.setType(WechatCustomerMenuEnum.VIDEO.getCode());
                    result.setUrl(button.getValue());
                    break;
                case VOICE: // voice：保存mediaID到value；
                    result.setType(WechatCustomerMenuEnum.VOICE.getCode());
                    result.setMaterialId(button.getValue());
                    break;
                /**
                 * api方式
                 */
                case CLICK:
                    result.setSupport(true);
                    result.setToolTips("");
                    result.setType(WechatCustomerMenuEnum.TEXT.getCode());
                    result.setTxt(null == button.getValue() ? "" : button.getValue());
                    if (StringUtils.isBlank(button.getKey())) {
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, "微信自定义菜单Click事件值为空"); // ignoreI18n
                    } else {
                        // 获取相应的文本消息内容
                        WeChatClickEventVO weChatClickEventVO = new WeChatClickEventVO();
                        weChatClickEventVO.setFsEa(user.getEnterpriseAccount());
                        weChatClickEventVO.setWxAppId(wxAppId);
                        weChatClickEventVO.setKey(button.getKey());
                        com.facishare.open.common.result.BaseResult<String> msgInfoResult = wechatMsgService.callbackCustomerMenuClickEvent(weChatClickEventVO);
                        if (!msgInfoResult.isSuccess()) {
                            logger.warn("call wechatMsgService.callbackCustomerMenuClickEvent failed, weChatClickEventVO=[{}], msgInfoResult=[{}]", weChatClickEventVO, msgInfoResult.toString());
                            break;
                        }
                        result.setTxt(null == msgInfoResult.getResult() ? "" : msgInfoResult.getResult());
                    }
                    break;
                case MEDIA_ID:
                    result.setType(WechatCustomerMenuEnum.MEDIA_ID.getCode());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 自定义菜单更新接口，全量更新接口
     *
     * @param user
     * @param weChatCustomMenuForm
     * @return
     */
    @Override
    public boolean updateMenu(FsUserVO user, WeChatCustomMenuForm weChatCustomMenuForm) {
        String wxAppId = getWxAppIdByAppId(user, weChatCustomMenuForm.getAppId());
        SelfMenuCreateVo selfMenuCreateVo = new SelfMenuCreateVo();
        selfMenuCreateVo.setWxAppId(wxAppId);
        selfMenuCreateVo.setEa(user.getEnterpriseAccount());
        selfMenuCreateVo.setFsUserId(new Long(user.getUserId()));
        List<MenuInfoBaseVo.Button> buttons = new ArrayList<>();
        if (!CollectionUtils.isEmpty(weChatCustomMenuForm.getMenus())) {
            if (weChatCustomMenuForm.getMenus().size() > FIRST_LEVEL_MENU_MAX) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "自定义菜单最多包括3个一级菜单"); // ignoreI18n
            }
            for (int i = 0; i < weChatCustomMenuForm.getMenus().size(); i++) {
                if (weChatCustomMenuForm.getMenus().get(i).getChildren().size() > SECOND_LEVEL_MENU_MAX) {
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "每个一级菜单最多包含5个二级菜单"); // ignoreI18n
                }
                MenuInfoBaseVo.Button buttonVo = menu2Button(weChatCustomMenuForm.getMenus().get(i), i + "", user, wxAppId);
                buttons.add(buttonVo);
            }
        }
        selfMenuCreateVo.setButtons(buttons);
        BaseResult baseResult;
        if (CollectionUtils.isEmpty(buttons)){ //清空菜单需要调用delete接口
            SelfMenuDeleteVo selfMenuDeleteVo = new SelfMenuDeleteVo();
            selfMenuDeleteVo.setWxAppId(wxAppId);
            selfMenuDeleteVo.setEa(user.getEnterpriseAccount());
            selfMenuDeleteVo.setFsUserId(new Long(user.getUserId()));
            baseResult = wechatSelfmenuService.deleteSelfmenu(selfMenuDeleteVo);
        }else {
            baseResult = wechatSelfmenuService.createSelfmenu(selfMenuCreateVo);
        }
        if (!baseResult.isSuccess()) {
            logger.warn("call wechatSelfmenuService.updateMenu, selfMenuCreateVo=[{}], baseResult=[{}]", selfMenuCreateVo, baseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "自定义菜单更新失败"); // ignoreI18n
        }
        return true;
    }

    private MenuInfoBaseVo.Button menu2Button(WeChatMenuFormVO menu, String id, FsUserVO user, String wxAppId) {
        MenuInfoBaseVo.Button button = new MenuInfoBaseVo.Button();
        button.setName(menu.getName());
        button.setValue(menu.getTxt());
        button.setKey(menu.getKey());
        button.setUrl(menu.getUrl());
        button.setMediaId(null);
        menu2ButtonTypeMatch(menu, button, user, wxAppId);

        if (!CollectionUtils.isEmpty(menu.getChildren())) {
            button.setSubButtons(new ArrayList<>());
            for (int i = 0; i < menu.getChildren().size(); i++) {
                WeChatMenuFormVO childrenMenu = menu.getChildren().get(i);
                MenuInfoBaseVo.Button vo = menu2Button(childrenMenu, id + i, user, wxAppId);
                button.getSubButtons().add(vo);
            }
        }
        return button;
    }

    private void menu2ButtonTypeMatch(WeChatMenuFormVO menu, MenuInfoBaseVo.Button button, FsUserVO user, String wxAppId) {
        if (WechatCustomerMenuEnum.PARENT.getCode() == menu.getType()) {
            button.setType(null);
        } else if (WechatCustomerMenuEnum.BIND_APP.getCode() == menu.getType() ||
                WechatCustomerMenuEnum.URL.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.VIEW.getType());
        } else if (WechatCustomerMenuEnum.TEXT.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.CLICK.getType());
            button.setUrl(null);
            // 如果没有key的话,说明是第一次从网站上拉下来的 --保存
            if (StringUtils.isBlank(menu.getKey())) {
                // 设置uuid为key
                String clickKey = IdGenerateUtils.generateUUID();
                button.setKey(clickKey);
                WeChatClickEventVO weChatClickEventVO = new WeChatClickEventVO();
                weChatClickEventVO.setFsEa(user.getEnterpriseAccount());
                weChatClickEventVO.setWxAppId(wxAppId);
                weChatClickEventVO.setKey(clickKey);
                weChatClickEventVO.setTextInfo(null == menu.getTxt() ? "" : menu.getTxt());
                com.facishare.open.common.result.BaseResult<Void> saveClickEventResult = weChatClickEventService.saveWeChatClickEvent(weChatClickEventVO);
                if (!saveClickEventResult.isSuccess()) {
                    logger.warn("call weChatClickEventService.saveWeChatClickEvent failed, weChatClickEventVO=[{}], saveClickEventResult=[{}]", weChatClickEventVO, saveClickEventResult.toString());
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "保存微信自定义菜单失败"); // ignoreI18n
                }
            } else {
                // 如果有key的话,说明是click转换来的，--更新key对应的内容
                WeChatClickEventVO weChatClickEventVO = new WeChatClickEventVO();
                weChatClickEventVO.setFsEa(user.getEnterpriseAccount());
                weChatClickEventVO.setWxAppId(wxAppId);
                weChatClickEventVO.setKey(menu.getKey());
                weChatClickEventVO.setTextInfo(null == menu.getTxt() ? "" : menu.getTxt());
                com.facishare.open.common.result.BaseResult<Void> updateClickEventResult = weChatClickEventService.updateWeChatClickEvent(weChatClickEventVO);
                if (!updateClickEventResult.isSuccess()) {
                    logger.warn("call weChatClickEventService.updateWeChatClickEvent failed, weChatClickEventVO=[{}], updateClickEventResult=[{}]", weChatClickEventVO, updateClickEventResult.toString());
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "微信自定义菜单内容更新失败"); // ignoreI18n
                }
            }
        } else if (WechatCustomerMenuEnum.MEDIA_ID.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.MEDIA_ID.getType());
        } else if (WechatCustomerMenuEnum.IMG.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.MEDIA_ID.getType());
        } else if (WechatCustomerMenuEnum.VOICE.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.MEDIA_ID.getType());
        } else if (WechatCustomerMenuEnum.VIDEO.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.MEDIA_ID.getType());
        } else if (WechatCustomerMenuEnum.NEWS.getCode() == menu.getType()) {
            button.setType(WechatCustomerMenuTypeEnum.MEDIA_ID.getType());
        }
    }
}
