package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppCreateTemplateDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.OpenAppCreateTemplateTypeEnum;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.AppCreateTemplateManager;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.model.CustomAppCreateForm;
import com.facishare.open.app.center.model.OpenAppCreateTemplateCategoryVO;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 应用创建模板接口.
 * Created by zenglb on 2016/3/22.
 */
@Controller
@RequestMapping("/open/appcenter/app/create/template")
public class AppCreateTemplateController extends BaseController {
    @Resource
    private AppCreateTemplateManager appCreateTemplateManager;
    @Resource
    private AppManager appManager;

    /**
     * 查询创建应用的模板分类列表.
     *
     * @param user 用户对象.
     * @return 应用创建模板分类list.
     */
    @RequestMapping("/queryCreateTemplateCategoryList")
    @ResponseBody
    public AjaxResult queryServiceCreateTemplateCategoryList(@ModelAttribute FsUserVO user,
                                                             @RequestParam(value = "templateType", required = false) Integer templateType) {
        // 默认查内部服务号模板
        if (Objects.isNull(templateType)) {
            templateType = OpenAppCreateTemplateTypeEnum.CUSTOM_SERVICE.getValue();
        }
        List<OpenAppCreateTemplateCategoryVO> categoryVOs = appCreateTemplateManager.queryCreateTemplateCategoryList(user, templateType);
        return new AjaxResult(categoryVOs);
    }

    /**
     * 使用创建模板创建应用.
     *
     * @param user 用户对象.
     * @return 应用创建模板list.
     */
    @RequestMapping("/createCustomAppByTemplate")
    @ResponseBody
    @Deprecated
    public AjaxResult createCustomAppByTemplate(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody CustomAppCreateForm form) {
        if (!BizCommonUtils.isAllowCreateCustomAppByTemplate(user.getEnterpriseAccount())) {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "应用中心升级，暂时无法创建应用，请谅解."); // ignoreI18n
        }
        checkFsAdmin(user);
        checkParamNotBlank(form, "请填写表单."); // ignoreI18n
        checkParamRegex(form.getAppName(), ConfigCenter.APP_NAME_CHECK_REGEX, "请填写有效的应用名称."); // ignoreI18n
        checkParamRegex(form.getAppDesc(), "^[\\s\\S]{1,300}$", "请填写有效的功能介绍."); // ignoreI18n
        checkParamNotBlank(form.getAppLogo(), "应用logo不能为空."); // ignoreI18n
        checkParamTrue(null != form.getAppAdmins() && form.getAppAdmins().length > 0, "请选择管理员."); // ignoreI18n
        for (Integer admin : form.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员不合法"); // ignoreI18n
            }
        }
        checkParamNotBlank(form.getTemplateId(), "请选择应用创建模板."); // ignoreI18n
        ArrayList<Integer> appAdminList = Lists.newArrayList(form.getAppAdmins());
        Map<String,Object> result = appCreateTemplateManager.createCustomAppByForm(user, lang, form, AppCenterEnum.AppType.CUSTOM_APP);
        result.put("isThisAppAdmin", appAdminList.contains(user.getUserId())? CommonConstant.YES : CommonConstant.NO);
        return new AjaxResult(result);
    }


    /**
     * 创建应用.
     *
     * @param user 用户对象.
     * @return 应用创建模板list.
     */
    @RequestMapping("/createCustomApp")
    @ResponseBody
    public AjaxResult createCustomApp(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody CustomAppCreateForm form) {
        checkParamNotBlank(form, "请填写表单."); // ignoreI18n
        checkParamRegex(form.getAppName(), ConfigCenter.APP_NAME_CHECK_REGEX, "请填写有效的应用名称."); // ignoreI18n
        checkParamRegex(form.getAppDesc(), "^[\\s\\S]{1,300}$", "请填写有效的功能介绍."); // ignoreI18n
        checkParamNotBlank(form.getAppLogo(), "应用logo不能为空."); // ignoreI18n
        checkParamTrue(null != form.getAppAdmins() && form.getAppAdmins().length > 0, "请选择管理员."); // ignoreI18n
        for (Integer admin : form.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员不合法"); // ignoreI18n
            }
        }
        checkParamTrue(StringUtils.isNotBlank(form.getAppLoginUrl()) || StringUtils.isNotBlank(form.getWebLoginUrl()), "请输入跳转地址."); // ignoreI18n

        checkAppCustomManageFunction(user);

        // 验证是否有重名应用
        if(appManager.existsAppName(form.getAppName(), user.getEnterpriseAccount(), AppCenterEnum.AppType.CUSTOM_APP)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "应用名已经存在"); // ignoreI18n
        }

        //使用空白应用模板创建应用.
        form.setTemplateId(CommonConstant.BLANK_APP_CREATE_TEMPLATE_ID);
        form.setServiceView(null);
        form.setOpenCustomMenu(null);
        form.setOpenAutoReply(null);
        ArrayList<Integer> appAdminList = Lists.newArrayList(form.getAppAdmins());
        Map<String,Object> result = appCreateTemplateManager.createCustomAppByForm(user, lang, form, AppCenterEnum.AppType.CUSTOM_APP);
        result.put("isThisAppAdmin", appAdminList.contains(user.getUserId())? CommonConstant.YES : CommonConstant.NO);
        return new AjaxResult(result);
    }

    /**
     * 创建服务号.
     *
     * @param user 用户对象.
     * @return 应用创建模板list.
     */
    @RequestMapping("/createServiceByTemplate")
    @ResponseBody
    public AjaxResult createServiceByTemplate(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody CustomAppCreateForm form) {
        checkParamNotBlank(form, "请填写表单."); // ignoreI18n
        checkParamRegex(form.getAppName(), ConfigCenter.SERVICE_NAME_CHECK_REGEX, "请填写有效的服务号名称."); // ignoreI18n
        checkParamRegex(form.getAppDesc(), "^[\\s\\S]{1,300}$", "请填写有效的功能介绍."); // ignoreI18n
        checkParamNotBlank(form.getAppLogo(), "服务号logo不能为空."); // ignoreI18n
        checkParamTrue(null != form.getAppAdmins() && form.getAppAdmins().length > 0, "请选择管理员."); // ignoreI18n
        for (Integer admin : form.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员不合法"); // ignoreI18n
            }
        }
        checkParamNotBlank(form.getTemplateId(), "请选择应用创建模板."); // ignoreI18n
        OpenAppCreateTemplateDO openAppCreateTemplateDO = this.appCreateTemplateManager.getTemplateById(form.getTemplateId());
        AppCenterEnum.AppType appType = AppCenterEnum.AppType.SERVICE;
        if (openAppCreateTemplateDO.getTemplateType() == OpenAppCreateTemplateTypeEnum.LINK_SERVICE.getValue()) {
            appType = AppCenterEnum.AppType.LINK_SERVICE;
        }

        if (Objects.equals(appType, AppCenterEnum.AppType.SERVICE)) {
            checkFsAdmin(user);
        } else {
            checkUpstreamLinkAdmin(user);
        }

        // 验证是否有重名服务号
        if(appManager.existsAppName(form.getAppName(), user.getEnterpriseAccount(), appType)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "服务号名称已经存在"); // ignoreI18n
        }

        form.setWebLoginUrl(null);
        form.setAppLoginUrl(null);

        // 不要修改，createCustomAppByForm 这个方法会清空appAdmins的值.
        ArrayList<Integer> appAdminList = Lists.newArrayList(form.getAppAdmins());

        Map<String,Object> result = appCreateTemplateManager.createCustomAppByForm(user, lang, form, appType);

        result.put("isThisAppAdmin", appAdminList.contains(user.getUserId())? CommonConstant.YES : CommonConstant.NO);
        return new AjaxResult(result);
    }
}
