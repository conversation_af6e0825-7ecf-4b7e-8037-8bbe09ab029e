package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.vo.OpenAppVO;
import com.facishare.open.app.center.api.model.vo.OpenLinkServiceVO;
import com.facishare.open.app.center.api.service.ServiceService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.manager.AppBindManager;
import com.facishare.open.app.center.manager.AppMessageToMqManager;
import com.facishare.open.app.center.model.AppCreateForm;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.fxiaoke.enterpriserelation.arg.GetCustomerServiceAuthStatisticsArg;
import com.fxiaoke.enterpriserelation.common.HeaderObj;
import com.fxiaoke.enterpriserelation.common.RestResult;
import com.fxiaoke.enterpriserelation.result.CustomerServiceAuthStatisticResult;
import com.fxiaoke.enterpriserelation.service.CustomerServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>企业互联服务号controller</p>
 *  外联服务号
 * @dateTime 2017/7/18 16:44
 * @<NAME_EMAIL>
 * @version 1.0 
 */
@Controller
@RequestMapping("/open/appcenter/link/service")
public class LinkServiceController extends BaseController {
    @Resource
    private AppBindManager appBindManager;
    @Resource
    private AppMessageToMqManager appMessageToMqManager;
    @Resource
    private ServiceService serviceService;
    @Autowired
    private CustomerServiceService customerServiceService;
    @Resource
    private OperationLogService operationLogService;

    /**
     * 更新互联服务号下游可见范围企业
     * @param fsUserVO
     * @return
     */
    @RequestMapping("/updateDownstreamRangeEas")
    @ResponseBody
    public AjaxResult updateDownstreamRangeEas(@ModelAttribute FsUserVO fsUserVO, @ModelAttribute String lang, @RequestBody AppCreateForm appForm) {
        checkParamNotBlank(appForm, "请填写表单."); // ignoreI18n
        String appId = appForm.getAppId();
        checkParamNotBlank(appId, "请选择应用."); // ignoreI18n
        checkLinkService(appId);
        List<String> linkServiceDownstreamEas = appForm.getLinkServiceDownstreamEas();
        if (CollectionUtils.isEmpty(linkServiceDownstreamEas)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "下游企业不能为空"); // ignoreI18n
        }

        // 校验权限
        boolean isUpstreamLinkAdmin = linkServiceManager.isUpstreamLinkAdmin(fsUserVO);
        boolean isServiceAdmin = this.isAppAdmin(fsUserVO, appId);
        if (!isUpstreamLinkAdmin && !isServiceAdmin) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
        }

        // 更新下游企业列表
        BaseResult<Void> updateLinkServiceDownstreamEasResult = serviceService.updateLinkServiceDownstreamEas(appId, linkServiceDownstreamEas);
        if (!updateLinkServiceDownstreamEasResult.isSuccess()) {
            logger.error("serviceService.updateLinkServiceDownstreamEas error. appId[{}], linkServiceDownstreamEas[{}], result[{}]",
                    appId, linkServiceDownstreamEas, updateLinkServiceDownstreamEasResult);
            throw new BizException(updateLinkServiceDownstreamEasResult);
        }

        // 修改互联服务号外部可见范围
        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(fsUserVO.getEnterpriseAccount(), appId,
                fsUserVO.getUserId(), System.currentTimeMillis()+ "", "修改互联服务号外部可见范围", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        return new AjaxResult(AjaxCode.OK);
    }

    private void checkLinkService(String appId) {
        if (!linkServiceManager.isLinkService(appId)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "不是互联服务号"); // ignoreI18n
        }
    }

    /**
     * 加载用户的可显示的企业互联服务号
     * 管理频道-企业互联-外联服务号
     * @param fsUserVO 操作用户.
     * @return 组件列表.
     */
    @RequestMapping("/queryAllLinkServiceListByAdmin")
    @ResponseBody
    public AjaxResult queryAllLinkServiceListByAdmin(@ModelAttribute FsUserVO fsUserVO) {
        //外联服务号1 是否上游企业互联管理员  调用平台组接口
        Map<String, Object> resultData = new HashMap<>();
        List<OpenAppVO> openAppVOList = null;
        List<CustomerServiceAuthStatisticResult> downstreamEaNums = null;
        //如果不是上游互联管理员，也需要显示下游互联服务号
        if(linkServiceManager.isUpstreamLinkAdmin(fsUserVO)) {
            List<Integer> appTypes = new ArrayList<>();
            appTypes.add(AppCenterEnum.AppType.LINK_SERVICE.value());
            openAppVOList = appBindManager.queryServicesByAdminAndType(fsUserVO, appTypes);
            HeaderObj headerObj = HeaderObj.newInstance(fsUserVO.getEnterpriseAccount(), null, null, null);
            // 查询服务号的开通企业数
            RestResult<List<CustomerServiceAuthStatisticResult>> result =
                    customerServiceService.getCustomerServiceAuthStatistics(headerObj, GetCustomerServiceAuthStatisticsArg.builder().upstreamEa(fsUserVO.getEnterpriseAccount()).customerServiceIds(null).build());
            if (!result.isSuccess()) {
                logger.warn("call customerServiceService.getCustomerServiceAuthStatistics to fail, fsEa[{}], result[{}]",
                        fsUserVO.getEnterpriseAccount(), result);
                throw new BizException(result.getErrCode(), result.getErrMsg());
            }
            downstreamEaNums = result.getData();
        }
        //互联服务号1 调用平台组 管理频道-外部服务号列表接口(把自己当做下游服务号传过去，获取别的上游为自己分配的互联服务号号)
        List<OpenLinkServiceVO> openLinkServiceVOList = linkServiceManager.getLinkServiceByDownstream(fsUserVO.getEnterpriseAccount());
        resultData.put("linkServiceAsUpstream",openAppVOList);
        resultData.put("downstreamEaNums",downstreamEaNums);
        resultData.put("linkServiceAsDownstream",openLinkServiceVOList);
        return new AjaxResult(resultData);
    }
}
