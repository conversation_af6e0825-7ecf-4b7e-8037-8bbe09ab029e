package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.enums.BuriedPointBizEnum;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.manager.AppMessageToMqManager;
import com.facishare.open.app.center.model.MsgAutoReplyCreateResult;
import com.facishare.open.app.center.model.MsgAutoReplyForm;
import com.facishare.open.app.center.mq.item.tags.AppCenterMqTagsConstant;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.autoreplymsg.model.*;
import com.facishare.open.autoreplymsg.result.CreateDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.CreateKeywordReplyResult;
import com.facishare.open.autoreplymsg.result.QueryDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.QueryKeywordReplyListResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.autoreplymsg.service.MsgDefaultReplyService;
import com.facishare.open.autoreplymsg.service.MsgKeywordReplyService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.material.api.model.vo.ImageTextVO;
import com.facishare.open.material.api.service.MaterialService;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.cloud.utils.JsonUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by fengyh on 2016/3/9.
 *
 *处理消息中心自动回复 : 包括默认自动回复+关键词自动回复
 *
 */

@Controller
@RequestMapping("/open/appcenter/msgcenter")
public class MsgAutoReplyController extends BaseController {

    @Resource
    private MsgAutoReplyService msgAutoReplyService;

    @Resource
    private MsgDefaultReplyService msgDefaultReplyService;

    @Resource
    private MsgKeywordReplyService msgKeywordReplyService;

    @Resource
    private MaterialService materialService;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private AppMessageToMqManager appMessageToMqManager;
    @Resource
    private OperationLogService operationLogService;


    /**
     * 查询自动回复开关是否打开
     *
     * @param user 操作者.
     * @param appID 取应用id.
     * @return 0-关闭， 1-打开
     */
    @RequestMapping("/queryAutoReplyStatus")
    @ResponseBody
    public AjaxResult queryAutoReplyStatus(@ModelAttribute FsUserVO user,
                                           @RequestParam(value = "appID", required = true) String appID) {
        checkParamNotBlank(appID, "请选择要处理的应用"); // ignoreI18n
        //要么是系统管理员，要么是应用管理员
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, appID);
        }

        // 验证服务号
        checkService(appID);

        int ret =  msgAutoReplyService.queryAutoReplySwitch(user.getEnterpriseAccount(), appID);
        return new AjaxResult(ret);
    }


    /**
     * 设置自动回复开关
     *
     * @param user   操作者.
     * @param msgAutoReplyForm
     * @return 是否设置成功， 0-成功
     */
    @RequestMapping(value = "/setAutoReplyStatus", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult setAutoReplyStatus(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {

        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        checkAppAdmin(user, msgAutoReplyForm.getAppID());
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());


        MsgBaseResult ret = msgAutoReplyService.setAutoReplySwitch(user.getEnterpriseAccount(), msgAutoReplyForm.getAppID(), msgAutoReplyForm.getReplySwitchStatus());


        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.putAll(JsonUtils.parseToMap(msgAutoReplyForm));
            map.put("appId", msgAutoReplyForm.getAppID());
            if (msgAutoReplyForm.getReplySwitchStatus() == 1) {
                map.put("action", BuriedPointBizEnum.ENABLE_AUTO_REPLY.getAction());

                //运营中心记录日志(开启自动回复)（fs-open-app-operating）,从mq改成异步调用
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), msgAutoReplyForm.getAppID(),
                        user.getUserId(), System.currentTimeMillis()+ "", "开启了自动回复", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
            } else {
                map.put("action", BuriedPointBizEnum.DISABLE_AUTO_REPLY.getAction());

                //运营中心记录日志(停用自动回复)（fs-open-app-operating）,从mq改成异步调用
                CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), msgAutoReplyForm.getAppID(),
                        user.getUserId(), System.currentTimeMillis()+ "", "停用了自动回复", OperationTypeConstant.WORKSHEET_STOP)); // ignoreI18n
            }
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{} or {}], map[{}]", BuriedPointBizEnum.DISABLE_AUTO_REPLY, BuriedPointBizEnum.ENABLE_AUTO_REPLY, map, e);
        }

        if (ret.isSuccess()) {
            return SUCCESS;
        } else   {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "设置自动回复开关失败"); // ignoreI18n
        }
    }

    /**
     * 创建某个应用的默认自动回复
     *
     * @param user  操作者.
     * @param msgAutoReplyForm
     *
     * @return 是否创建成功，0-成功。 若成功，返回自动回复编号
     */
    @RequestMapping(value ="/createDefaultReply", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult createDefaultReply(@ModelAttribute FsUserVO user,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {
        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, msgAutoReplyForm.getAppID());
        }
        checkParamNotBlank(msgAutoReplyForm.getActiveReplyType(), "未提供自动回复消息类型"); // ignoreI18n
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());

        CreateDefaultReplyVO createDefaultReplyVO = new CreateDefaultReplyVO();
        createDefaultReplyVO.setAppID(msgAutoReplyForm.getAppID());
        createDefaultReplyVO.setEnterpriseAccount(user.getEnterpriseAccount());
        createDefaultReplyVO.setActiveReplyType(msgAutoReplyForm.getActiveReplyType());
        createDefaultReplyVO.setContentImgTxtID(msgAutoReplyForm.getContentImgTxtID());
        createDefaultReplyVO.setContentTxt(msgAutoReplyForm.getContentTxt());
        CreateDefaultReplyResult createDefaultReplyResult = msgDefaultReplyService.createDefaultReply(createDefaultReplyVO);

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.putAll(JsonUtils.parseToMap(msgAutoReplyForm));
            map.put("appId", msgAutoReplyForm.getAppID());
            map.put("action", BuriedPointBizEnum.CREATE_DEFAULT_AUTO_REPLY.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CREATE_DEFAULT_AUTO_REPLY, map, e);
        }

        MsgAutoReplyCreateResult msgAutoReplyCreateResult = new MsgAutoReplyCreateResult();
        if (createDefaultReplyResult.isSuccess()) {
            msgAutoReplyCreateResult.setReplyMsgID(createDefaultReplyResult.getReplyMsgID());
            return new AjaxResult(msgAutoReplyCreateResult);
        } else {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "创建默认回复失败"); // ignoreI18n
        }
    }

    /**
     * 删除某个应用的默认自动回复
     *
     * @param user  操作者.
     * @param msgAutoReplyForm
     *
     * @return 是否删除成功， 0-成功
     */
    @RequestMapping(value ="/deleteDefaultReply", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult deleteDefaultReply(@ModelAttribute FsUserVO user,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {
        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, msgAutoReplyForm.getAppID());
        }
        checkParamNotBlank(msgAutoReplyForm.getReplyMsgID(), "未提供自动回复消息ID"); // ignoreI18n
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());

        MsgBaseResult ret = msgDefaultReplyService.deleteDefaultReply(user.getEnterpriseAccount(), msgAutoReplyForm.getAppID(), msgAutoReplyForm.getReplyMsgID());

        if (ret.isSuccess()) {
            return SUCCESS;
        } else {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "删除默认自动回复失败"); // ignoreI18n
        }
    }


    /**
     * 更新某个应用的默认自动回复
     *
     * @param user  操作者.
     * @param msgAutoReplyForm
     * @return 是否更新成功， 0-成功
     */
    @RequestMapping(value ="/updateDefaultReply", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult updateDefaultReply(@ModelAttribute FsUserVO user,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {

        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, msgAutoReplyForm.getAppID());
        }
        checkParamNotBlank(msgAutoReplyForm.getReplyMsgID(), "未提供自动回复消息ID"); // ignoreI18n
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());

        UpdateDefaultReplyVO updateDefaultReplyVO = new UpdateDefaultReplyVO();
        updateDefaultReplyVO.setAppID(msgAutoReplyForm.getAppID());
        updateDefaultReplyVO.setEnterpriseAccount(user.getEnterpriseAccount());
        updateDefaultReplyVO.setReplyMsgID(msgAutoReplyForm.getReplyMsgID());
        updateDefaultReplyVO.setActiveReplyType(msgAutoReplyForm.getActiveReplyType());
        updateDefaultReplyVO.setContentImgTxtID(msgAutoReplyForm.getContentImgTxtID());
        updateDefaultReplyVO.setContentTxt(msgAutoReplyForm.getContentTxt());
        MsgBaseResult ret = msgDefaultReplyService.updateDefaultReply(updateDefaultReplyVO);

        if (ret.isSuccess()) {
            return new AjaxResult(AjaxCode.OK);
        }  else {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "更新默认自动回复失败"); // ignoreI18n
        }
    }


    /**
     * 查询某个应用的默认自动回复
     *
     * @param user  操作者.
     * @param appID
     * @return 是否启动成功
     */
    @RequestMapping("/queryDefaultReply")
    @ResponseBody
    public AjaxResult queryDefaultReply(@ModelAttribute FsUserVO user,
                                        @RequestParam(value = "appID", required = true) String appID) {

        checkParamNotBlank(appID, "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, appID);
        }

        // 验证服务号
        checkService(appID);

        QueryDefaultReplyResult ret = msgDefaultReplyService.queryDefaultReply(user.getEnterpriseAccount(),appID);

        if (false == ret.isValid()) {
            //随便返回一个空对象
            return new AjaxResult(new HashMap<String , String>());
        }

        return new AjaxResult(ret);
    }

    /**
     * 创建某个应用的关键词自动回复
     *
     * @param user  操作者.
     * @param msgAutoReplyForm
     *
     * @return 是否创建成功， 0-成功
     */
    @RequestMapping(value ="/createKeywordReply", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult createKeywordReply(@ModelAttribute FsUserVO user,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {

        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, msgAutoReplyForm.getAppID());
        }
        checkParamNotBlank(msgAutoReplyForm.getActiveReplyType(), "未提供自动回复消息类型"); // ignoreI18n
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());

        CreateKeywordReplyVO createKeywordReplyVO = new CreateKeywordReplyVO();
        createKeywordReplyVO.setAppID(msgAutoReplyForm.getAppID());
        createKeywordReplyVO.setEnterpriseAccount(user.getEnterpriseAccount());
        createKeywordReplyVO.setActiveReplyType(msgAutoReplyForm.getActiveReplyType());
        createKeywordReplyVO.setContentImgTxtID(msgAutoReplyForm.getContentImgTxtID());
        createKeywordReplyVO.setContentTxt(msgAutoReplyForm.getContentTxt());
        createKeywordReplyVO.setRuleName(msgAutoReplyForm.getRuleName());
        createKeywordReplyVO.setKeywordList(msgAutoReplyForm.getKeywordList());

        CreateKeywordReplyResult createKeywordReplyResult = msgKeywordReplyService.createKeywordReply(createKeywordReplyVO);

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            List<KeywordTypeInfo> keywordList = msgAutoReplyForm.getKeywordList();
            msgAutoReplyForm.setKeywordList(null);
            if(!CollectionUtils.isEmpty(keywordList)){
                map.put("keywords", keywordList.stream().collect(Collectors.toMap(KeywordTypeInfo::getName,KeywordTypeInfo::getType)));
            }
            map.putAll(JsonUtils.parseToMap(msgAutoReplyForm));
            map.put("appId", msgAutoReplyForm.getAppID());
            map.put("action", BuriedPointBizEnum.CREATE_KEY_WORD_AUTO_REPLY.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CREATE_KEY_WORD_AUTO_REPLY, map, e);
        }

        MsgAutoReplyCreateResult msgAutoReplyCreateResult = new MsgAutoReplyCreateResult();
        if (createKeywordReplyResult.isSuccess()) {
            msgAutoReplyCreateResult.setReplyMsgID(createKeywordReplyResult.getReplyMsgID());
            return new AjaxResult(msgAutoReplyCreateResult);
        } else {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "创建关键词回复失败"); // ignoreI18n
        }
    }


    /**
     * 删除某个应用的关键词自动回复
     *
     * @param user  操作者.
     * @param msgAutoReplyForm
    *
     * @return 是否删除成功， 0-成功
     */
    @RequestMapping(value ="/deleteKeywordReply", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult deleteKeywordReply(@ModelAttribute FsUserVO user,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {
        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, msgAutoReplyForm.getAppID());
        }
        checkParamNotBlank(msgAutoReplyForm.getReplyMsgID(), "未提供自动回复消息ID"); // ignoreI18n
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());

        MsgBaseResult ret = msgKeywordReplyService.deleteKeywordReply(user.getEnterpriseAccount(), msgAutoReplyForm.getAppID(), msgAutoReplyForm.getReplyMsgID());

        if (ret.isSuccess()) {
            return SUCCESS;
        } else {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "删除关键词自动回复失败"); // ignoreI18n
        }
    }


    /**
     * 更新某个应用的关键词自动回复
     *
     * @param  user  操作者.
     * @param  msgAutoReplyForm
     *
     * @return 是否更新成功， 0-成功
     */
    @RequestMapping(value ="/updateKeywordReply", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult updateKeywordReply(@ModelAttribute FsUserVO user,
                                         @RequestBody MsgAutoReplyForm msgAutoReplyForm) {
        checkParamNotBlank(msgAutoReplyForm.getAppID(), "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, msgAutoReplyForm.getAppID());
        }
        checkParamNotBlank(msgAutoReplyForm.getReplyMsgID(), "未提供自动回复消息ID"); // ignoreI18n
        // 验证服务号
        checkService(msgAutoReplyForm.getAppID());

        UpdateKeywordReplyVO updateKeywordReplyVO = new UpdateKeywordReplyVO();
        updateKeywordReplyVO.setAppID(msgAutoReplyForm.getAppID());
        updateKeywordReplyVO.setEnterpriseAccount(user.getEnterpriseAccount());
        updateKeywordReplyVO.setReplyMsgID(msgAutoReplyForm.getReplyMsgID());
        updateKeywordReplyVO.setActiveReplyType(msgAutoReplyForm.getActiveReplyType());
        updateKeywordReplyVO.setContentImgTxtID(msgAutoReplyForm.getContentImgTxtID());
        updateKeywordReplyVO.setContentTxt(msgAutoReplyForm.getContentTxt());
        updateKeywordReplyVO.setRuleName(msgAutoReplyForm.getRuleName());
        updateKeywordReplyVO.setKeywordList(msgAutoReplyForm.getKeywordList());
        MsgBaseResult ret = msgKeywordReplyService.updateKeywordReply(updateKeywordReplyVO);

        if (ret.isSuccess()) {
            return SUCCESS;
        } else {
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "更新关键词自动回复失败"); // ignoreI18n
        }
    }

    /**
     * 查询某个应用的关键词自动回复
     *
     * @param user           操作者.
     * @param appID
     * @return 是否查询成功， 0-成功。若成功，返回关键词列表
     */
    @RequestMapping("/queryKeywordReplyList")
    @ResponseBody
    public AjaxResult queryKeywordReplyList(@ModelAttribute FsUserVO user,
                                            @RequestParam(value = "appID", required = true) String appID) {
        checkParamNotBlank(appID, "请选择要处理的应用"); // ignoreI18n
        if(!isFsAdmin(user)) {
            checkAppAdmin(user, appID);
        }
        // 验证服务号
        checkService(appID);

        QueryKeywordReplyListResult queryKeywordReplyListResult = msgKeywordReplyService.queryKeywordReplyList(user.getEnterpriseAccount(), appID);

        if (false == queryKeywordReplyListResult.isValid()) {
            return new AjaxResult(new int[0]);
        }

        Iterator iterator =  queryKeywordReplyListResult.getKeywordList().iterator();
        while(iterator.hasNext()) {
            KeywordReplyDO<ImageTextVO> keywordReplyDO = (KeywordReplyDO<ImageTextVO>)iterator.next();
            ImageTextVO imageTextVO = new ImageTextVO();
            try {
                if(keywordReplyDO.getContentImgTxtID().length() > 0) {
                    imageTextVO = materialService.findImageTextById(keywordReplyDO.getContentImgTxtID()).getResult();
                }
            } catch (Exception e) {
            }
            keywordReplyDO.setContentImgTxtDetail(imageTextVO);
        }
        return new AjaxResult(queryKeywordReplyListResult.getKeywordList());
    }
}
