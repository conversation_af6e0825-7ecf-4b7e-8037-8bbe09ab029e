package com.facishare.open.app.center.threadlocal;

import com.facishare.open.common.model.FsUserVO;
import org.springframework.core.NamedThreadLocal;

/**
 * user thread loacl
 *
 * <AUTHOR>
 * @date on 2016/8/18.
 */
public class UserContextHolder {

    private static final ThreadLocal<FsUserVO> fsUserVOHolder =
            new NamedThreadLocal<>("FsUserVO");

    //语言环境
    private static final ThreadLocal<String> langHolder =
            new NamedThreadLocal<>("Lang");

    public static void resetFsUserVO() {
        fsUserVOHolder.remove();
    }

    public static ThreadLocal<FsUserVO> getFsUserVOHolder() {
        return fsUserVOHolder;
    }


    public static FsUserVO getFsUserVO() {
        return fsUserVOHolder.get();
    }

    public static void resetLang() {
        langHolder.remove();
    }

    public static ThreadLocal<String> getLangHolder() {
        return langHolder;
    }


    public static String getLang() {
        return langHolder.get();
    }
}
