package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.OuterContactsManager;
import com.facishare.open.app.center.manager.OuterServiceManager;
import com.facishare.open.app.center.model.outers.OuterContactsVO;
import com.facishare.open.app.center.model.outers.args.OuterContactsQueryPagerArgs;
import com.facishare.open.app.center.model.outers.args.SaveServiceCommissionersArgs;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.proxy.constants.PermissionType;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 外联服务号中的外部联系人相关接口。
 * Created by zenglb on 2016/11/8.
 */
@RestController
@RequestMapping("/open/appcenter/outer/contracts")
public class OuterContactsController extends BaseController {

    @Resource
    private OuterContactsManager outerContactsManager;

    @Resource
    private OuterServiceManager outerServiceManager;

    /**
     * 查询所有的外部联系人.
     *
     * @param fsUserVO
     * @param args
     * @return
     */
    @RequestMapping("/queryPager")
    public AjaxResult queryPager(@ModelAttribute FsUserVO fsUserVO,
                                 @RequestBody OuterContactsQueryPagerArgs args) {
        checkParamNotBlank(args.getAppId(), "请选择一个外联服务号"); // ignoreI18n
        checkAppAdmin(fsUserVO, args.getAppId());
        checkServiceIsOn(fsUserVO, args.getAppId());
        outerServiceManager.checkWechat(fsUserVO, args.getAppId(), null, Lists.newArrayList(PermissionType.PERMISSION_USER_MANAGE));
        Pager<OuterContactsVO> outerContactsVOPager = outerContactsManager.queryPager(fsUserVO, args);
        return new AjaxResult(outerContactsVOPager);
    }

    /**
     * 设置服务专员.
     *
     * @param fsUserVO
     * @param args
     * @return
     */
    @RequestMapping("/saveServiceCommissioners")
    public AjaxResult saveServiceCommissioners(@ModelAttribute FsUserVO fsUserVO,
                                               @RequestBody SaveServiceCommissionersArgs args) {
        checkParamNotBlank(args.getAppId(), "请选择一个外联服务号"); // ignoreI18n
        checkParamNotBlank(args.getWxOpenId(), "请选择一个微信用户进行修改"); // ignoreI18n
        checkParamTrue(!CollectionUtils.isEmpty(args.getUserIds()), "必须指定至少一个有效的服务专员"); // ignoreI18n
        checkParamTrue(args.getUserIds().size() <= 20, "一个微信用户最多只能选择20个服务专员"); // ignoreI18n
        checkAppAdmin(fsUserVO, args.getAppId());
        checkServiceIsOn(fsUserVO, args.getAppId());
        outerServiceManager.checkWechat(fsUserVO, args.getAppId(), null, Lists.newArrayList(PermissionType.PERMISSION_USER_MANAGE));
        for (Integer userId : args.getUserIds()) {
            if (null == userId || userId < 1){
                throw new BizException(AjaxCode.PARAM_ERROR, "请选择有效的服务专员"); // ignoreI18n
            }
        }

        outerContactsManager.saveServiceCommissioners(fsUserVO, args);
        return SUCCESS;
    }
}
