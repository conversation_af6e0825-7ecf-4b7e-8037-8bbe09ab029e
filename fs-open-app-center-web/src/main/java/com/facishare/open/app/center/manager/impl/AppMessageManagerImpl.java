package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.facishare.open.app.center.api.model.enums.AppStatus;
import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.app.center.api.model.vo.OpenAppVO;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.result.IntegerResult;
import com.facishare.open.app.center.api.service.*;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.AppMessageManager;
import com.facishare.open.app.center.model.AppMessageVO;
import com.facishare.open.app.center.model.TemplateMessageParam;
import com.facishare.open.app.center.utils.OpenAppUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.material.api.enums.CreatorTypeEnum;
import com.facishare.open.material.api.enums.MaterialTypeEnum;
import com.facishare.open.material.api.enums.MessageSendTypeEnum;
import com.facishare.open.material.api.model.vo.AccepterVO;
import com.facishare.open.material.api.model.vo.DownstreamAccepterVO;
import com.facishare.open.material.api.model.vo.MessageVO;
import com.facishare.open.material.api.service.ArticleCategoryDetailService;
import com.facishare.open.material.api.service.MaterialMessageService;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.model.SendTemplateMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.SendMessageService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Created by xialf on 2015/8/27.
 *
 * <AUTHOR>
 */
@Service
public class AppMessageManagerImpl implements AppMessageManager {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private OpenAppAddressBookCircleService openAppAddressBookCircleService;
    @Resource
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;
    @Resource
    private SendMessageService sendMessageService;
    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Resource
    private OpenAppService openAppService;
    @Resource
    private OpenAppComponentService openAppComponentService;
    @Resource
    private MaterialMessageService materialMessageService;
    @Resource
    private ArticleCategoryDetailService articleCategoryDetailService;
    @Resource
    private AppManager appManager;
    @Resource
    private OpenAppAdminService openAppAdminService;
    @Resource
    private OpenFsUserBindAppService openFsUserBindAppService;

    private static Executor executor = Executors.newFixedThreadPool(5);

    @Override
    public AppViewDO queryTargetView(FsUserVO fsUserVO, String appId) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(appResult);
        }

        OpenAppDO app = appResult.getResult();
        if (null == app) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "应用不存在"); // ignoreI18n
        }

        //获取所有组件列表
        BaseResult<List<OpenAppComponentDO>> openAppComponentsResult =
                openAppComponentService.queryAppComponentListByAppId(fsUserVO, appId);
        if (!openAppComponentsResult.isSuccess()) {
            logger.warn("failed to call queryAppComponentListByAppId, user={}, appId={}",
                    fsUserVO, appId);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "请求应用的组件列表错误"); // ignoreI18n
        }

        AppViewDO unionViewResult = new AppViewDO();

        // 服务号的可见范围
        for (OpenAppComponentDO compDO : openAppComponentsResult.getResult()) {
            if (compDO.getComponentType() == AppComponentTypeEnum.SERVICE.getType()
                    || compDO.getComponentType() == AppComponentTypeEnum.LINK_SERVICE.getType()) {
                BaseResult<AppViewDO> appViewResult =
                        openFsUserAppViewService.loadAppViewByType(
                                fsUserVO, compDO.getComponentId(),
                                AppComponentTypeEnum.getByCode(compDO.getComponentType()));
                if (!appViewResult.isSuccess()) {
                    throw new BizException(AjaxCode.BIZ_EXCEPTION,
                            appViewResult.getErrMessage(), appViewResult.getErrDescription());
                }
                if (appViewResult.getResult() != null) {
                    unionView(unionViewResult, appViewResult.getResult());
                }
            }
        }

        //如果为空,则设置为全公司范围
        if ((unionViewResult.getMember() == null || unionViewResult.getMember().length == 0)
                && (unionViewResult.getDepartment() == null || unionViewResult.getDepartment().length == 0)) {
            unionViewResult.setDepartment(new Integer[]{CommonConstant.COMPANY_DEPARTMENT_CODE});
        }

        return unionViewResult;
    }

    @Override
    public String sendImageMessage(FsUserVO fsUserVO, AppMessageVO appMsgForm) {
        AppResult appResult = openAppService.loadOpenAppFast(appMsgForm.getAppId());
        if(!appResult.isSuccess() || null == appResult.getResult()){
            throw new BizException(AjaxCode.BIZ_EXCEPTION,appResult,"应用不存在"); // ignoreI18n
        }

        // 校验接收人范围
        checkAcceptor(fsUserVO, appResult.getResult(), appMsgForm);

        MessageVO messageVO = buildMessageVO(fsUserVO, appMsgForm, MaterialTypeEnum.IMAGE);
        MessageSendTypeEnum messageSendType = getMessageSendTypeEnum(appMsgForm);
        com.facishare.open.common.result.BaseResult<String> sendMessageResult = materialMessageService.sendMessage(messageVO, messageSendType);
        if (!sendMessageResult.isSuccess()) {
            logger.error("materialMessageService.sendMessage. messageVO=[{}], MessageSendType, [{}], sendMessageResult[{}]",
                    messageVO, messageSendType, sendMessageResult);
            throw new BizException(sendMessageResult);
        }
        return sendMessageResult.getResult();
    }

    @Override
    public String sendTextMessage(FsUserVO fsUserVO, AppMessageVO appMsgForm) {
        AppResult appResult = openAppService.loadOpenAppFast(appMsgForm.getAppId());
        if(!appResult.isSuccess() || null == appResult.getResult()){
            throw new BizException(AjaxCode.BIZ_EXCEPTION,appResult,"应用不存在"); // ignoreI18n
        }

        // 校验接收人范围
        checkAcceptor(fsUserVO, appResult.getResult(), appMsgForm);

        MessageVO messageVO = buildMessageVO(fsUserVO, appMsgForm, MaterialTypeEnum.TEXT);
        MessageSendTypeEnum messageSendType = getMessageSendTypeEnum(appMsgForm);
        com.facishare.open.common.result.BaseResult<String> sendMessageResult = materialMessageService.sendMessage(messageVO, messageSendType);
        if (!sendMessageResult.isSuccess()) {
            logger.error("materialMessageService.sendMessage. messageVO=[{}], MessageSendType, [{}], sendMessageResult[{}]",
                    messageVO, messageSendType, sendMessageResult);
            throw new BizException(sendMessageResult);
        }
        return sendMessageResult.getResult();
    }

    private void checkTargets(FsUserVO fsUserVO, AppMessageVO appMsgForm) {
        AppViewDO targets = JsonKit.json2object(appMsgForm.getTargets(), AppViewDO.class);
        MessageSendTypeEnum messageSendType = getMessageSendTypeEnum(appMsgForm);
        if (MessageSendTypeEnum.GROUP_SEND_MSG.equals(messageSendType) || MessageSendTypeEnum.ADMIN_REPLY_SEND.equals(messageSendType)) {
            checkTargets(fsUserVO, appMsgForm.getAppId(), targets);
        }
    }

    @Override
    public String sendImgTextMessage(FsUserVO fsUserVO, AppMessageVO appMsgForm) {
        MaterialTypeEnum materialTypeEnum;
        try {
            materialTypeEnum = MaterialTypeEnum.getByCode(Integer.parseInt(appMsgForm.getMaterialType()));
        } catch (Exception e) {
            throw new BizException(AjaxCode.PARAM_ERROR, "materialType illegal");
        }

        AppResult appResult = openAppService.loadOpenAppFast(appMsgForm.getAppId());
        if(!appResult.isSuccess() || null == appResult.getResult()){
            throw new BizException(AjaxCode.BIZ_EXCEPTION,appResult,"应用不存在"); // ignoreI18n
        }
        // 校验接收人
        checkAcceptor(fsUserVO, appResult.getResult(), appMsgForm);

        MessageVO messageVO = buildMessageVO(fsUserVO, appMsgForm, materialTypeEnum);
        MessageSendTypeEnum messageSendType = getMessageSendTypeEnum(appMsgForm);
        // 预览图文消息按群发消息处理
        if (Objects.equals(MessageSendTypeEnum.PREVIEW_SEND_MSG, messageSendType)) {
            messageSendType = MessageSendTypeEnum.GROUP_SEND_MSG;
        }
        com.facishare.open.common.result.BaseResult<String> sendMessageResult = materialMessageService.sendMessage(messageVO, messageSendType);
        if (!sendMessageResult.isSuccess()) {
            logger.error("materialMessageService.sendMessage. messageVO=[{}], MessageSendType, [{}], sendMessageResult[{}]",
                    messageVO, messageSendType, sendMessageResult);
            throw new BizException(sendMessageResult);
        }

        String messageId = sendMessageResult.getResult();

        // 文章分类不为空，则保存分类
        if (!org.springframework.util.CollectionUtils.isEmpty(appMsgForm.getMaterialCategoryMapping())) {
            Map<String, List<String>> mapping = Maps.newHashMap();
            appMsgForm.getMaterialCategoryMapping().forEach(materialCategoryMappingForm -> {
                mapping.put(materialCategoryMappingForm.getMaterialImageTextParamId(), materialCategoryMappingForm
                        .getCategoryIds());
            });
            com.facishare.open.common.result.BaseResult<Boolean> saveResult =
                    articleCategoryDetailService.save(fsUserVO.getEnterpriseAccount(), appMsgForm.getAppId(), messageId, mapping);
            if (!saveResult.isSuccess() || !saveResult.getResult()) {
                logger.error("articleCategoryDetailService.save error. fsEa[{}], appId=[{}], msgId=[{}], mapping=[{}], saveResult[{}]",
                        fsUserVO.getEnterpriseAccount(), appMsgForm.getAppId(), messageId, mapping, saveResult);
            }
        }
        return messageId;
    }

    private MessageVO buildMessageVO(FsUserVO fsUserVO, AppMessageVO appMsgForm, MaterialTypeEnum materialTypeEnum) {
        AppViewDO targets = JsonKit.json2object(appMsgForm.getTargets(), AppViewDO.class);
        MessageVO messageVO = new MessageVO();
        messageVO.setAppId(appMsgForm.getAppId());
        messageVO.setContent(appMsgForm.getContent());
        messageVO.setImageContent(appMsgForm.getImageContentVO());
        messageVO.setMaterialType(materialTypeEnum);
        messageVO.setMaterialId(appMsgForm.getMaterialId());
        messageVO.setCreatorTypeEnum(CreatorTypeEnum.APP_ADMIN);

        if (Objects.nonNull(appMsgForm.getIsFixedTime())) {
            messageVO.setIsFixedTime(appMsgForm.getIsFixedTime());
            messageVO.setSetTime(appMsgForm.getSetTime());
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(appMsgForm.getMaterialCategoryMapping())) {
            Map<String, List<String>> mappingArticle = Maps.newHashMap();
            appMsgForm.getMaterialCategoryMapping().forEach(materialCategoryMappingForm -> {
                mappingArticle.put(materialCategoryMappingForm.getMaterialImageTextParamId(), materialCategoryMappingForm.getCategoryIds());
            });
            messageVO.setArticleCategory(mappingArticle);
        }

        //设置发送范围
        if (Objects.nonNull(targets)) {
            AccepterVO accepterVO = new AccepterVO();
            EmployeeRange employeeRange = EmployeeRange.fromAppView(targets);
            accepterVO.setIsAllEmployees(employeeRange.includeWholeCompany());
            accepterVO.setEa(fsUserVO.getEnterpriseAccount());
            accepterVO.setDepartments(employeeRange.getDepartment());
            accepterVO.setEmployees(employeeRange.getMember());
            messageVO.setAccepterVO(accepterVO);
        }
        if (Objects.nonNull(appMsgForm.getDownstreamAccepterVO())) {
            messageVO.setDownstreamAccepterVO(appMsgForm.getDownstreamAccepterVO());
        }

        messageVO.setCreatorTypeEnum(CreatorTypeEnum.APP_ADMIN);
        messageVO.setSender(fsUserVO);

        return messageVO;
    }

    /**
     * 校验接收人
     * @param appMsgForm
     */
    private void checkAcceptor(FsUserVO fsUserVO, OpenAppDO openAppDO, AppMessageVO appMsgForm) {

        AppViewDO targets = JsonKit.json2object(appMsgForm.getTargets(), AppViewDO.class);
        boolean isLinkService = Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value());
        if (isLinkService) {
            if (Objects.equals(appMsgForm.getSendType(), MessageSendTypeEnum.GROUP_SEND_MSG.getCode())) {
                // 互联服务号群发消息必须有下游接收人
                if (Objects.isNull(appMsgForm.getDownstreamAccepterVO()) || appMsgForm.getDownstreamAccepterVO().illegalArguments()){
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "群发消息下游不能为空"); // ignoreI18n
                }
            } else if (Objects.equals(appMsgForm.getSendType(), MessageSendTypeEnum.PREVIEW_SEND_MSG.getCode())) {
                checkTargets(fsUserVO, openAppDO.getAppId(), targets);
            } else if (Objects.equals(appMsgForm.getSendType(), MessageSendTypeEnum.ADMIN_REPLY_SEND.getCode())) {
                if (Objects.isNull(targets)) {
                    checkDownstreamAcceptorVO(appMsgForm.getDownstreamAccepterVO());
                }
            }
        } else {
            checkTargets(fsUserVO, openAppDO.getAppId(), targets);
        }
    }

    private void checkDownstreamAcceptorVO(DownstreamAccepterVO downstreamAccepterVO) {
        if (Objects.isNull(downstreamAccepterVO)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "请选择消息发送目标"); // ignoreI18n
        }
        if (org.springframework.util.CollectionUtils.isEmpty(downstreamAccepterVO.getTags())
                && org.springframework.util.CollectionUtils.isEmpty(downstreamAccepterVO.getFsEas())
                && org.springframework.util.CollectionUtils.isEmpty(downstreamAccepterVO.getUserIds())) {
            throw new BizException(AjaxCode.PARAM_ERROR, "请选择消息发送目标"); // ignoreI18n
        }
    }

    /**
     * 检验发送范围
     */
    private void checkTargets(FsUserVO fsUserVO, String appId, AppViewDO targets) {
        if (Objects.isNull(targets)) {
            throw new BizException(AjaxCode.PARAM_ERROR, "请选择消息发送目标"); // ignoreI18n
        }

        AppViewDO msgTargetView = queryTargetView(fsUserVO, appId);

        Integer[] deptIdArray = msgTargetView.getDepartment();
        List<Integer> deptIdList;
        if(deptIdArray != null) {
            deptIdList = Arrays.asList(deptIdArray);
            if(deptIdList.contains(CommonConstant.COMPANY_DEPARTMENT_CODE)) {
                return;
            }
        }

        Set<Integer> viewUsers = convertToUserIds(fsUserVO, msgTargetView);
        Set<Integer> sendUsers = convertToUserIds(fsUserVO, targets);

        List<Integer> appAdminId =  queryAppAdminId(fsUserVO, appId);
        // 如果viewUsers不包含管理员ID,则增加
        viewUsers.addAll(appAdminId);

        if (!viewUsers.containsAll(sendUsers)){
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "发送范围超出服务号订阅范围."); // ignoreI18n
        }
    }

    private List<Integer> queryAppAdminId(FsUserVO fsUserVO, String appId) {
        BaseResult<List<String>> queryAppAdminIdListResult = openAppAdminService.queryAppAdminIdList(fsUserVO.getEnterpriseAccount(), appId);
        if (!queryAppAdminIdListResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询管理员列表失败"); // ignoreI18n
        }
        List<String> appAdminIdList = queryAppAdminIdListResult.getResult();
        List<Integer> appAdminId =  appAdminIdList.stream().map(fsUserId -> new FsUserVO(fsUserId).getUserId()).collect(Collectors.toList());
        return appAdminId;
    }

    private MessageSendTypeEnum getMessageSendTypeEnum(AppMessageVO appMsgForm) {
        MessageSendTypeEnum messageSendType = MessageSendTypeEnum.GROUP_SEND_MSG;
        if (null != appMsgForm.getSendType()) {
            try {
                MessageSendTypeEnum messageSendTypeEnum = MessageSendTypeEnum.getByCode(appMsgForm.getSendType());
                if (null != messageSendTypeEnum) {
                    messageSendType = messageSendTypeEnum;
                }
            } catch (Exception e) {
                logger.error("MessageSendTypeEnum parse faild , appMsgForm = {} {}", appMsgForm, e);
            }
        }
        return messageSendType;
    }

    @Override
    public void sendTemplateMessage(FsUserVO fsUserVO, TemplateMessageParam templateMessageVO) {
        //固定信息
        SendTemplateMessageVO sendTemplateMessageVO = new SendTemplateMessageVO();
        sendTemplateMessageVO.setType(MessageTypeEnum.TEMPLATE);
        sendTemplateMessageVO.setPostId(UUID.randomUUID().toString());

        //基本信息
        sendTemplateMessageVO.setAppId(templateMessageVO.getAppId());
        sendTemplateMessageVO.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        Set<Integer> userIds = convertToUserIds(fsUserVO, templateMessageVO.getTargets());
        sendTemplateMessageVO.setToUserList(new ArrayList<>(userIds));

        //模板特有信息
        sendTemplateMessageVO.setTemplateId(templateMessageVO.getTemplateId());
        sendTemplateMessageVO.setData(templateMessageVO.getData());
        sendTemplateMessageVO.setTopColor(templateMessageVO.getTopColor());
        sendTemplateMessageVO.setUrl(templateMessageVO.getUrl());

        //启动线程发送消息
        executor.execute(() -> {
            MessageResult messageResult = sendMessageService.sendTemplateMessage(sendTemplateMessageVO,
                    com.facishare.open.msg.constant.MessageSendTypeEnum.THIRD_PARTY_PUSH);
            if (!messageResult.isSuccess()) {
                logger.error("failed to send template message: user={}, templateMsg={}, result={}",
                        fsUserVO, sendTemplateMessageVO, messageResult);
            }
        });
    }

    @Override
    public String notifyColleaguesToTry(FsUserVO fsAdmin, String appId) {
        //设置发送范围
        EmployeeRange employeeRange = new EmployeeRange();
        employeeRange.getDepartment().add(CommonConstant.COMPANY_DEPARTMENT_CODE);
        final Set<Integer> userIds = convertToUserIds(fsAdmin, employeeRange);
        userIds.remove(fsAdmin.getUserId());

        employeeRange.getDepartment().clear();
        employeeRange.setMember(new ArrayList<>(userIds));
        return null;
//        return serviceMessageManager.sendTried(fsAdmin, appId, employeeRange, );
    }

    /**
     * 把EmployeeRange转化为员工集合(唯一性).
     *
     * @param fsUserVO      公司信息
     * @param employeeRange 员工范围
     * @return 员工集合
     */
    @Override
    public Set<Integer> convertToUserIds(final FsUserVO fsUserVO, final EmployeeRange employeeRange) {
        //需要把目标id放到set中以避免对同一个发送多次,因为一个人即出现在部门department,也可能出现在member
        Set<Integer> users = new HashSet<>(employeeRange.getMember());

        final List<Integer> depIds = employeeRange.getDepartment();
        if (!depIds.isEmpty()) {
            BaseResult<List<Integer>> memberIdsResult;
            if (depIds.contains(CommonConstant.COMPANY_DEPARTMENT_CODE)) {
                memberIdsResult = openAppAddressBookEmployeeService.getAllEmployeeIds(fsUserVO.getEnterpriseAccount(), fsUserVO.getUserId(), 2);
            } else {
                memberIdsResult = openAppAddressBookCircleService.getMemberIds(fsUserVO.getEnterpriseAccount(), fsUserVO.getUserId(), depIds, true);
            }

            //判断返回值
            if (!memberIdsResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, memberIdsResult, memberIdsResult.getErrDescription());
            }
            users.addAll(memberIdsResult.getResult());
        }
        return users;
    }

    @Override
    public Set<Integer> convertToUserIds(FsUserVO user, AppViewDO targets) {
        if (Objects.isNull(targets)) {
            return Sets.newHashSet();
        }
        return convertToUserIds(user,EmployeeRange.fromAppView(targets));
    }

    /**
     * 计算可见范围的并集.
     *
     * @param resultView 被并而且作为并集的结果
     * @param newView    被并的科技那范围
     */
    public void unionView(AppViewDO resultView, final AppViewDO newView) {
        Set<Integer> members = new HashSet<>();
        unionData(members, resultView.getMember());
        unionData(members, newView.getMember());

        Set<Integer> departs = new HashSet<>();
        unionData(departs, resultView.getDepartment());
        unionData(departs, newView.getDepartment());

        resultView.setMember(members.toArray(new Integer[members.size()]));
        resultView.setDepartment(departs.toArray(new Integer[departs.size()]));
    }

    private <T> void unionData(Set<T> unionData, T[] newData) {
        if (null != newData) {
            CollectionUtils.addAll(unionData, newData);
        }
    }

    @Override
    public String sendSystemMessage(String appId,AccepterVO accepterVO, String message) {
        MessageVO messageVO = new MessageVO();
        messageVO.setAppId(appId);
        messageVO.setContent(message);
        messageVO.setMaterialType(MaterialTypeEnum.TEXT);

        //设置发送范围
        messageVO.setAccepterVO(accepterVO);

        messageVO.setCreatorTypeEnum(CreatorTypeEnum.DEV);
        messageVO.setSender(null);

        MessageSendTypeEnum messageSendType = MessageSendTypeEnum.SYSTEM_MSG;

        com.facishare.open.common.result.BaseResult<String> sendMaterialMsgResult
                = materialMessageService.sendMessage(messageVO, messageSendType);
        if (!sendMaterialMsgResult.isSuccess()) {
            logger.warn("fail to call materialMessageService.sendMessage, message={}, result={}",
                    messageVO, sendMaterialMsgResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "发送消息失败"); // ignoreI18n
        }
        return sendMaterialMsgResult.getResult();
    }


    @Override
    public List<OpenAppVO> loadOpenAppByIds(List<String> appIds) {
        BaseResult<List<OpenAppDO>> baseResult = openAppService.loadOpenAppByIdsFast(appIds);
        if (!baseResult.isSuccess()) {
            throw new BizException(baseResult);
        }
        List<OpenAppDO> openAppDOs = baseResult.getResult();
        if(!org.springframework.util.CollectionUtils.isEmpty(openAppDOs)){
            //管理频道-作为下游 不需要已删除和已停用的服务号
            openAppDOs =  openAppDOs.stream().filter(appDO -> Objects.equals(appDO.getStatus(), AppStatus.ON_LINE.getStatus())
                    && Objects.equals(appDO.getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())).collect(Collectors.toList());
            openAppDOs.sort((paramDO1, paramDO2) -> paramDO2.getGmtModified().compareTo(paramDO1.getGmtModified()));
        }
        List<OpenAppVO> openAppVOS = OpenAppUtils.getOpenAppVOByDO(openAppDOs);
        if(!org.springframework.util.CollectionUtils.isEmpty(openAppVOS)){
            List<String> openAppIds = openAppDOs.stream().map(OpenAppDO::getAppId).collect(Collectors.toList());
            Map<String, String> appIdIconUrlMap = appManager.batchQueryAppIconUrl(openAppIds, IconType.WEB);

            openAppVOS.forEach(openAppVO -> {
                IntegerResult integerResult = openFsUserBindAppService.queryAppBindStatus(openAppVO.getAppCreater(), openAppVO.getAppId());
                if(!integerResult.isSuccess()){
                    logger.warn("fail to call openFsUserBindAppService.queryAppBindStatus, fsEa={}, result={}",
                            openAppVO.getAppCreater(), integerResult);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询应用绑定状态失败"); // ignoreI18n
                }
                openAppVO.setBindStatus(integerResult.getResult());
                String iconUrl = appIdIconUrlMap.get(openAppVO.getAppId());
                if (StringUtils.isNotBlank(iconUrl)) {
                    openAppVO.setLogo(iconUrl);
                }
            });
        }
        return openAppVOS.stream().filter(openAppVO -> Objects.equals(openAppVO.getBindStatus(), 1)).collect(Collectors.toList());
    }

}
