package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxImageUploadResult;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.app.center.api.model.vo.IconVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.AppIconService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.form.AssetForm;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.warehouse.api.IconService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;

@Controller("imageController")
@RequestMapping("/open/appcenter/image")
public class ImageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ImageController.class);

    @Resource
    private AppIconService appIconService;

    @Resource
    private IconService iconService;

    @Value("${fs.open.app.center.image.url}")
    private String imageUrl;

    @RequestMapping("/view")
    public void view(HttpServletResponse response, @RequestParam("appId") String appId,
                     @RequestParam("type") String type, @RequestParam("width") int width, @RequestParam("height") int height,
                     @RequestParam(value = "v", required = false) String v) throws IOException {
        try {
            if (StringUtils.isBlank(appId) || StringUtils.isBlank(type) || (width < 1) || (height < 1)) {
                throw new IllegalArgumentException("params is illegal");
            }
            IconType iconType = IconType.LOGO;
//			if(StringUtils.isBlank(v)){
//				iconType = IconType.getByMessage(type);
//			}
            BaseResult<IconVO> result = appIconService.download(appId, iconType, width, height);
            if (!result.isSuccess()) {
                logger.warn("fail to appIconService.download: appId[{}], iconType[{}], width[{}], height[{}], result[{}]",
                        appId, iconType, width, height, result);
                throw new RuntimeException(result.getErrDescription());
            }
            ServletOutputStream out = response.getOutputStream();
            response.setContentType("image/" + result.getResult().getExt());
            response.setContentLength(result.getResult().getData().length);
            out.write(result.getResult().getData());
            out.flush();
        } catch (IllegalArgumentException e) {
            response.sendError(HttpStatus.SC_NOT_FOUND, "params is illegal");
        } catch (Exception e) {
            logger.error("down load app logo error：appId[" + appId + "]type[" + type + "]width[" + width + "]height["
                    + height + "]", e);
            response.sendError(HttpStatus.SC_NOT_FOUND, "fail");
        }
    }


    @RequestMapping(value = "upload", method = RequestMethod.POST)
    @ResponseBody
    public AjaxImageUploadResult uploadImage(AssetForm assetVO)
            throws IOException {

        if (null == assetVO.getUploadFile()) {
            return new AjaxImageUploadResult(AjaxCode.PARAM_ERROR, "请选择上传的文件"); // ignoreI18n
        }
        if (assetVO.getUploadFile().isEmpty() || assetVO.getUploadFile().getSize() == 0) {
            return new AjaxImageUploadResult(AjaxCode.PARAM_ERROR, "文件不能为空"); // ignoreI18n
        }

        if (assetVO.getUploadFile().getSize() > 5242880) {
            return new AjaxImageUploadResult(AjaxCode.PARAM_ERROR, "图片大小不能超过5M。"); // ignoreI18n
        }

        CommonsMultipartFile file = assetVO.getUploadFile();

        String ext = getExtName(file.getOriginalFilename());
        BufferedImage imgBuff;
        try {
            imgBuff = ImageIO.read(file.getFileItem().getInputStream());
        } catch (IOException e) {
            logger.warn("ImageIO.read(file) is error!", e);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "无法解析您上传的图片，该图片可能采用了比较特殊的编码格式或颜色空间"); // ignoreI18n
        }
        if (imgBuff == null) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "该图片已被损坏"); // ignoreI18n
        }
        Rectangle rectangle = new Rectangle(imgBuff.getWidth(), imgBuff.getHeight());

        com.facishare.open.warehouse.result.BaseResult<String> uploadResult =
                iconService.uploadAndMake(file.getBytes(), ext, rectangle, 0.0);
        if (!uploadResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, uploadResult, "上传扫描件失败"); // ignoreI18n
        }
        final String fileId = uploadResult.getResult();
        return new AjaxImageUploadResult(imageUrl + fileId, fileId, imgBuff.getWidth(), imgBuff.getHeight());
    }

    private String getExtName(final String fileName) {
        String ext = "";
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex > -1) {
            ext = fileName.substring(lastIndex + 1);
        }
        return ext;
    }

}
