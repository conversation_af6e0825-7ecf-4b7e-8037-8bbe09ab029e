package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.manager.QuotaManager;
import com.facishare.open.app.pay.api.enums.PayStatus;
import com.facishare.open.app.pay.api.model.QuotaVo;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class QuotaManagerImpl implements QuotaManager {
    private final Logger logger = LoggerFactory.getLogger(QuotaManagerImpl.class);

    @Resource
    private QuotaService quotaService;

    @Override
    public PayStatus getPayStatus(FsUserVO fsUserVO, String appId) {
        //获取应用的付费信息, 比如"已购买, 可用20个用户, 2017.02.16到期"
        final com.facishare.open.common.result.BaseResult<QuotaVo> quotaVoResult = quotaService.queryQuotaInfo(fsUserVO.getEnterpriseAccount(), appId);
        if (!quotaVoResult.isSuccess()) {
            logger.warn("failed to call queryQuotaInfo, user=[{}], appId={}, result={}", fsUserVO, appId, quotaVoResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询应用配额信息失败"); // ignoreI18n
        }


        final Date realExpire = quotaVoResult.getResult().getExpireDateWithoutExtension();
        PayStatus payStatusForShow = quotaVoResult.getResult().getPayStatus();

        if (realExpire != null && realExpire.getTime() < System.currentTimeMillis()) {
            if (payStatusForShow == PayStatus.ON_TRIAL) {
                payStatusForShow = PayStatus.TRIAL_EXPIRED;
            } else if (payStatusForShow == PayStatus.PURCHASED) {
                payStatusForShow = PayStatus.PURCHASE_EXPIRED;
            }
        }

        return payStatusForShow;
    }
}
