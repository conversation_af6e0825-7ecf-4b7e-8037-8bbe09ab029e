package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.model.NoticeContentVO;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/**
 * @describe: 应用中心公告
 * @author: xiaowei<PERSON>
 * @date: 2016/7/4 10:14
 */
@Controller
@RequestMapping("/open/appcenter/notice")
public class AppCenterNoticeController extends BaseController {
    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedisSupport;

    /**
     * 获取公告
     */
    @RequestMapping("/getNotice")
    @ResponseBody
    public AjaxResult getNotice(@ModelAttribute FsUserVO fsUserVO) {
        //redis 中存在，则是手工关闭
        Boolean handClosedFlag = jedisSupport.sismember(ConfigCenter.NOTICE_PREFIX, fsUserVO.asStringUser());
        Boolean endFlag = System.currentTimeMillis() > ConfigCenter.NOTICE_ENDTIME.getTime();
        Map<String, Object> resultMap = Maps.newHashMap();
        //不展示公告
        if (handClosedFlag || !ConfigCenter.NOTICE_ISSHOW || endFlag || (ConfigCenter.NOTICE_ONLY_ADMIN && !(isFsAdmin
                (fsUserVO) || isAppAdmin(fsUserVO) || isServiceAdmin(fsUserVO)))){
            resultMap.put("noticeContents", "");
            return new AjaxResult(resultMap);
        }
        String[] noticeTitle = ConfigCenter.NOTICE_TITLE.split(ConfigCenter.NOTICE_SEPARATOR);
        String[] noticeUrl = ConfigCenter.NOTICE_URL.split(ConfigCenter.NOTICE_SEPARATOR);
        List<NoticeContentVO> noticeContents = Lists.newArrayList();
        for (int i = 0; i< noticeTitle.length && i< noticeUrl.length; i++){
            NoticeContentVO noticeContentVO = new NoticeContentVO();
            noticeContentVO.setTitle(noticeTitle[i]);
            noticeContentVO.setUrl(noticeUrl[i]);
            noticeContentVO.setStartTime(ConfigCenter.NOTICE_STARTTIME);
            noticeContentVO.setEndTime(ConfigCenter.NOTICE_ENDTIME);
            noticeContents.add(noticeContentVO);
        }
        resultMap.put("noticeContents", noticeContents);
        return new AjaxResult(resultMap);
    }

    /**
     * 手动关闭
     */
    @RequestMapping("/handCloseNotice")
    @ResponseBody
    public AjaxResult handCloseNotice(@ModelAttribute FsUserVO fsUserVO) {
        Boolean handClosedFlag = jedisSupport.sismember(ConfigCenter.NOTICE_PREFIX, fsUserVO.asStringUser());
        if (!handClosedFlag) {
            Boolean existsFlag = jedisSupport.exists(ConfigCenter.NOTICE_PREFIX);
            jedisSupport.sadd(ConfigCenter.NOTICE_PREFIX, fsUserVO.asStringUser());
            //不存在，表明这里是第一次添加，则设置过期时间
            if (!existsFlag){
                jedisSupport.pexpireAt(ConfigCenter.NOTICE_PREFIX, ConfigCenter.NOTICE_ENDTIME.getTime());
            }

        }
        return new AjaxResult(true);
    }

}
