package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.vo.OuterServiceWechatVO;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.OuterServiceManager;
import com.facishare.open.app.center.model.outers.OuterServiceForm;
import com.facishare.open.app.center.model.outers.OuterServiceNoticeVO;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.IdGenerateUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.warehouse.api.IconService;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.wechat.proxy.constants.PermissionType;
import com.facishare.wechat.proxy.model.auth.BindInfo;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外联服务号Controller
 * <p>
 * Created by zhouq on 2016/11/1.
 */
@Controller
@RequestMapping("/open/appcenter/outer/service/")
public class OuterServiceController extends BaseController {

    private static final String OUTER_SERVICE_REDIS_PRE = "app.center.outer.service.create";
    private static final int REDIS_WILL_EXPIRE_TTL = 5; //5秒

    @Resource
    private OuterServiceManager outerServiceManager;

    @Resource
    private AppManager appManager;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;

    @Resource
    private IconService iconService;

    /**
     * 获取微信公众号信息
     * 1：先判断下微信公众号是否绑定过外联服务号
     * 2：获取微信公众号信息
     *
     * @param wxAppId 微信公众号id
     * @return 微信公众号信息
     */
    @RequestMapping("/queryWechat")
    @ResponseBody
    public AjaxResult queryWechat(@ModelAttribute FsUserVO user,
                                  @RequestParam(value = "wxAppId", required = false) String wxAppId) {
        checkParamNotBlank(wxAppId, "微信服务号id不能为空"); // ignoreI18n
        checkFsAdmin(user);
        HashMap<String, Object> serviceExtMap = Maps.newHashMap();
        AjaxResult ajaxResult = checkWechatIsBind(user, wxAppId);
        if (null != ajaxResult){
            return ajaxResult;
        }

        BindInfo bindInfo = outerServiceManager.queryWechat(user, wxAppId);
        if (null != bindInfo) {
            if (Strings.isNullOrEmpty(bindInfo.getLogoUrl())) {
                bindInfo.setLogoUrl(ConfigCenter.WECHAT_DEFAULT_ICON);
            }
            serviceExtMap.put("bindInfo", bindInfo);
            serviceExtMap.put("defaultLogoUrl", ConfigCenter.WECHAT_DEFAULT_ICON);
        }
        return new AjaxResult(serviceExtMap);
    }

    /**
     * 创建外联服务号.
     *
     * @param user 用户对象.
     * @param form 表单对象
     * @return AjaxResult
     */
    @RequestMapping("/create")
    @ResponseBody
    public AjaxResult create(@ModelAttribute FsUserVO user, @RequestBody OuterServiceForm form) {

        checkParamNotBlank(form, "请填写表单."); // ignoreI18n
        checkParamRegex(form.getAppName(), ConfigCenter.APP_NAME_CHECK_REGEX, "请填写有效的外联服务号名称."); // ignoreI18n
        checkParamRegex(form.getAppDesc(), "^[\\s\\S]{1,300}$", "请填写有效的功能介绍."); // ignoreI18n
        checkParamNotBlank(form.getAppLogo(), "应用logo不能为空."); // ignoreI18n
        checkParamNotBlank(form.getWxAppId(), "微信公众号id不能为空."); // ignoreI18n
        checkParamTrue(null != form.getAppAdmins() && form.getAppAdmins().length > 0, "请选择管理员."); // ignoreI18n
        for (Integer admin : form.getAppAdmins()) {
            if (null == admin) {
                return new AjaxResult(AjaxCode.PARAM_ERROR, "管理员不合法"); // ignoreI18n
            }
        }
        checkFsAdmin(user);
        // 验证是否有重名外联服务号
        if(appManager.existsAppName(form.getAppName(), user.getEnterpriseAccount(), AppCenterEnum.AppType.OUT_SERVICE_APP)) {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "外联服务号名已经存在"); // ignoreI18n
        }
        //如果开启了2个创建外联服务号窗口。并发创建，只能有一个成功
        AjaxResult ajaxResult = checkWechatIsBind(user, form.getWxAppId());
        if (null != ajaxResult){
            return ajaxResult;
        }
        ArrayList<Integer> appAdminList = Lists.newArrayList(form.getAppAdmins());
        Map<String, Object> result;

        String uuid = IdGenerateUtils.generateUUID();
        String redisKey = OUTER_SERVICE_REDIS_PRE + form.getWxAppId();
        if (tryLock(redisKey, uuid, REDIS_WILL_EXPIRE_TTL)) {
            try {
                result = outerServiceManager.createOuterServiceByForm(user, form, AppCenterEnum.AppType.OUT_SERVICE_APP);
            } finally {
                unlock(redisKey, uuid);
            }
        } else {
            return new AjaxResult(AjaxCode.PARAM_ERROR, "该微信公众号正在授权给其他外联服务号！"); // ignoreI18n
        }

        result.put("isThisAppAdmin", appAdminList.contains(user.getUserId()) ? CommonConstant.YES : CommonConstant.NO);
        result.put("notice", Lists.newArrayList(ConfigCenter.OUTER_SERVICE_CREATE_SUCCESS_AUTH1, ConfigCenter.OUTER_SERVICE_CREATE_SUCCESS_AUTH2,
                                                ConfigCenter.OUTER_SERVICE_CREATE_SUCCESS_AUTH3, ConfigCenter.OUTER_SERVICE_CREATE_SUCCESS_AUTH4));
        return new AjaxResult(result);
    }

    /**
     * 微信公众号取消授权
     * 1：调用丁成接口，解绑
     * 2：数据库解绑，设置status
     *
     * @param outerServiceWechatVO entity
     * @return 微信公众号信息
     */
    @RequestMapping("/unBindWechat")
    @ResponseBody
    public AjaxResult unBindWechat(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                   @RequestBody OuterServiceWechatVO outerServiceWechatVO) {
        checkParamNotBlank(outerServiceWechatVO.getWxAppId(), "微信服务号id不能为空"); // ignoreI18n
        checkParamNotBlank(outerServiceWechatVO.getAppId(), "外联服务号id不能为空"); // ignoreI18n
        checkFsAdmin(user);
        return new AjaxResult(outerServiceManager.unBindWechat(user, lang, outerServiceWechatVO));
    }

    /**
     * 加载外联服务号信息.
     *
     * @param user  管理员或应用管理员登录.
     * @param appId 外联服务号Id
     * @return AjaxResult.
     */
    @RequestMapping("/query")
    @ResponseBody
    public AjaxResult query(@ModelAttribute FsUserVO user,
                                               @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkService(appId);
        Map<String, Object> ajaxDataMap = outerServiceManager.loadOuterServiceByAppId(user, appId);
        return new AjaxResult(ajaxDataMap);
    }

    /**
     * 加载外联服务号工作台
     *
     * @param user  用户
     * @param appId 外联服务号Id
     * @return AjaxResult
     */
    @RequestMapping("/loadOuterServiceDashboard")
    @ResponseBody
    public AjaxResult loadOuterServiceDashboard(@ModelAttribute FsUserVO user,
                                                @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "外联服务号id不能为空"); // ignoreI18n
        checkServiceIsOn(user, appId);
        checkAppAdmin(user, appId);
        Map<String, Object> dashboardMap = new HashMap<>();
        dashboardMap.put("bindInfo", null);
        dashboardMap.put("statistics", null);
        OpenAppDO appDO = appManager.loadAppBrief(appId);
        dashboardMap.put("appName", appDO.getAppName());
        try {
            OuterServiceNoticeVO outerServiceNoticeVO = new OuterServiceNoticeVO();
            OuterServiceWechatVO outerServiceWechatVO = outerServiceManager.queryOuterServiceWechat(user, null, appId, null);
            if (null != outerServiceWechatVO) {
                if (null != outerServiceWechatVO.getUnbindSource()) { //取消授权
                    if (CommonConstant.UNBIND_FROM_WECHAT == outerServiceWechatVO.getUnbindSource()) {
                        outerServiceNoticeVO.setNoticeType(CenterConstants.OUTER_SERVICE_IS_UNBIND);
                        outerServiceNoticeVO.setWechatNotice(ConfigCenter.UNBIND_FROM_WECHAT_NOTICE);
                    } else if (CommonConstant.UNBIND_FROM_OUTER_SERVICE == outerServiceWechatVO.getUnbindSource()) {
                        outerServiceNoticeVO.setNoticeType(CenterConstants.OUTER_SERVICE_IS_UNBIND);
                        outerServiceNoticeVO.setWechatNotice(ConfigCenter.UNBIND_FROM_OUTER_SERVICE_NOTICE);
                    }else if (CommonConstant.UNBIND_FROM_UPGRADE_TO_CUSTOMER_SERVICE == outerServiceWechatVO.getUnbindSource()){
                        outerServiceNoticeVO.setNoticeType(CenterConstants.UPGRADE_TO_CUSTOMER_SERVICE);
                        outerServiceNoticeVO.setWechatNotice(ConfigCenter.UPGRADE_TO_CUSTOMER_SERVICE_NOTICE);
                    }
                } else {
                    BindInfo bindInfo = outerServiceManager.queryWechat(user, outerServiceWechatVO.getWxAppId());
                    dashboardMap.put("bindInfo", bindInfo);
                    if (CommonConstant.WECHAT_SERVICE == bindInfo.getType()) { //服务号
                        if (CommonConstant.WECHAT_IS_UNAUTHERIZED == bindInfo.getVerifyType()) { //未认证的服务号
                            outerServiceNoticeVO.setNoticeType(CenterConstants.OUTER_SERVICE_IS_UNAUTHERIZED);
                            outerServiceNoticeVO.setWechatNotice(ConfigCenter.WECHAT_IS_UNAUTHERIZED_NOTICE);
                            outerServiceNoticeVO.setVerifyUrl(ConfigCenter.WECHAT_CERTIFICATION_ADDRESS);
                        }
                    }else { //订阅号
                        outerServiceNoticeVO.setNoticeType(CenterConstants.IS_SUBSCRIBE);
                        outerServiceNoticeVO.setWechatNotice(ConfigCenter.WECHAT_SUBSCRIBE_NOTICE);
                    }
                    dashboardMap.put("statistics", outerServiceManager.queryOuterServiceDashboardStatistics(user, appId, bindInfo.getWxAppId()));
                }
            }
            dashboardMap.put("outerServiceNoticeVO", outerServiceNoticeVO);
        } catch (Exception e) {
            logger.warn("failed to call queryWechat, user[{}], appId[{}]", user, appId, e);
        }

        // 服务号特性列表
        dashboardMap.put("features", outerServiceManager.queryOuterServiceFeatureList(user, appId));

        // 常见问题
        try {
            dashboardMap.put("faqs", appManager.queryServiceDashboardFAQs(ConfigCenter.SERVICE_WECHAT_FAQ_CATEGORY_ID, ConfigCenter.SERVICE_WECHAT_FAQ_COUNT));
        } catch (BizException e) {
            dashboardMap.put("faqs", Lists.newArrayList());
            logger.warn("appManager.queryServiceDashboardFAQs failed, categoryId[{}], faqCount[{}]", ConfigCenter.SERVICE_WECHAT_FAQ_CATEGORY_ID, ConfigCenter.SERVICE_WECHAT_FAQ_COUNT, e);
        }
        dashboardMap.put("faqsMore", ConfigCenter.SERVICE_FAQ_MORE_TARGET_URL);

        return new AjaxResult(dashboardMap);
    }


    /**
     * 根据不同的模块按企业来灰度
     * allowShowOuterService : 是否展示外联服务号
     * materialInsertTrainingAssistantVideo ：素材插入音视频
     * @param user 用户
     * @param modelKey 业务模块
     * @return boolean
     */
    @RequestMapping("/isAllowShowByModelKey")
    @ResponseBody
    public AjaxResult isAllowShowByModelKey(@ModelAttribute FsUserVO user, String modelKey) {
        return new AjaxResult(BizCommonUtils.isAllowShowByModelKey(modelKey, user.getEnterpriseAccount()));
    }


    /**
     * 查询外联服务号的名称和关联状态
     * 手机端查看微信客服二维码用到
     *
     * @param user  纷享用户
     * @param appId 外联服务号appId
     * @return AjaxResult.
     */
    @RequestMapping("/queryAppNameAndBindStatus")
    @ResponseBody
    public AjaxResult queryAppNameAndBindStatus(@ModelAttribute FsUserVO user,
                                                @RequestParam(value = "appId", required = false) String appId) {
        //参数
        checkParamNotBlank(appId, "请选择外联服务号"); // ignoreI18n
        //权限
        List<Integer> permissionTypes = Lists.newArrayList(
                PermissionType.PERMISSION_MESSAGE_MANAGE,
                PermissionType.PERMISSION_ACCOUNT_SERVICE,
                PermissionType.PERMISSION_MUTI_KEFU,
                PermissionType.PERMISSION_QR_CODE);
        outerServiceManager.checkWechat(user, appId, null, permissionTypes);
        checkServiceIsOn(user, appId);

        Map<String, Object> result = outerServiceManager.queryAppNameAndBindStatus(user, appId);
        return new AjaxResult(result);
    }


    /**
     * 检查微信公众号是否绑定过外联服务号
     * 一个微信公众号只能绑定一个外联服务号【全网企业】
     * @param user 用户
     * @param wxAppId 微信公众号Id
     * @return ajaxResult
     */
    private AjaxResult checkWechatIsBind(FsUserVO user, String wxAppId){
        OuterServiceWechatVO outerServiceWechatVO = outerServiceManager.queryOuterServiceWechat(new FsUserVO(null, user.getUserId()), wxAppId, null, CommonConstant.VALID);
        if (null != outerServiceWechatVO) {
            AppResult appResult = openAppService.loadOpenAppFast(outerServiceWechatVO.getAppId());
            if (!appResult.isSuccess() || null == appResult.getResult()) {
                logger.warn("failed to call loadOpenApp, user=[{}], appId={}, result={}", user, outerServiceWechatVO.getAppId(), appResult.toString());
                return new AjaxResult(AjaxCode.WECHA_IS_BIND, String.format(ConfigCenter.IS_BIND_OUTER_SERVICE_NOTICE, null, null));
            }
            String enterpriseName = null;
            GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
            arg.setEnterpriseAccount(appResult.getResult().getAppCreater());
            try {
                GetSimpleEnterpriseResult getSimpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(arg);
                if (GetSimpleEnterpriseResult.RESULT_CODE_NORMAL != getSimpleEnterpriseResult.getResultCode()) {
                    logger.warn("failed to getSimpleEnterprise, user[{}], arg={}, result={}", user, arg, getSimpleEnterpriseResult.getResultCode());
                }
                if (null != getSimpleEnterpriseResult.getSimpleEnterprise()) {
                    enterpriseName = getSimpleEnterpriseResult.getSimpleEnterprise().getEnterpriseName();
                }
            } catch (Exception e) {
                logger.error("failed to getSimpleEnterprise, arg={},", arg, e);
            }
            return new AjaxResult(AjaxCode.WECHA_IS_BIND, String.format(ConfigCenter.IS_BIND_OUTER_SERVICE_NOTICE,
                    enterpriseName, appResult.getResult().getAppName()));
        }
        return null;
    }

    private boolean tryLock(final String lock, String value, final int duration) {
        final String setResult = jedis.set(lock, value, "nx", "ex", duration);
        logger.info("tryLock lock[{}],result[{}]", lock, setResult);
        return setResult != null && "OK".equalsIgnoreCase(setResult);
    }

    private boolean unlock(final String lock, String value) {
        final String lockValue = jedis.get(lock);
        if (lockValue != null && value.equals(lockValue)) {
            jedis.del(lock);
            return true;
        }
        return false;
    }

}
