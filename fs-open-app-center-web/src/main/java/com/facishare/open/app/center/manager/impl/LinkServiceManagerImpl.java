package com.facishare.open.app.center.manager.impl;

import com.facishare.enterprise.common.model.EnterpriseSimpleVo;
import com.facishare.enterprise.common.result.Result;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.vo.OpenAppVO;
import com.facishare.open.app.center.api.model.vo.OpenLinkServiceVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.msg.model.EaConnAppSessionConfig;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.service.MsgSessionService;
import com.fxiaoke.enterpriserelation.arg.GetUserPositionArg;
import com.fxiaoke.enterpriserelation.arg.IsRelationChannelVisibleArg;
import com.fxiaoke.enterpriserelation.arg.IsUpstreamArg;
import com.fxiaoke.enterpriserelation.arg.ListDownstreamVisibleCustomerServiceArg;
import com.fxiaoke.enterpriserelation.common.HeaderObj;
import com.fxiaoke.enterpriserelation.common.RestResult;
import com.fxiaoke.enterpriserelation.result.CustomerServiceAuthResult;
import com.fxiaoke.enterpriserelation.result.EnterpriseSimpleResult;
import com.fxiaoke.enterpriserelation.result.UserPositionResult;
import com.fxiaoke.enterpriserelation.service.CustomerServiceService;
import com.fxiaoke.enterpriserelation.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation.service.LinkAppService;
import com.fxiaoke.enterpriserelation.service.PublicEmployeeService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class LinkServiceManagerImpl implements LinkServiceManager {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    @Autowired
    private PublicEmployeeService publicEmployeeService;
    @Autowired
    private LinkAppService linkAppService;
    @Autowired
    private CustomerServiceService customerServiceService;

    @Resource
    private OpenAppAdminService openAppAdminService;
    @Resource
    private AppManager appManager;
    @Resource
    private AppMessageManager appMessageManager;
    @Resource
    private MsgSessionService msgSessionService;
    @Resource
    private ServiceNumberManager serviceNumberManager;
    @Resource
    private ServiceManager serviceManager;

    @Override
    public void updateAppAdmins(FsUserVO userVO, String appId, List<Integer> admins) {
        // 更新工作台Session
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setAdmins(admins);
        serviceNumberManager.modifyServiceWorkBenchSession(userVO, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, CommonConstant.IS_LINK_SERVICE);
    }

    @Override
    public void updateAppLogo(FsUserVO user, String appId, String upstreamEa, String logoUrl) {
        // 更新下游服务号Session
        EaConnAppSessionConfig eaConnAppSessionConfig  = new EaConnAppSessionConfig();
        eaConnAppSessionConfig.setAppId(appId);
        eaConnAppSessionConfig.setUpstreamEnterprise(upstreamEa);
        eaConnAppSessionConfig.setPortraitPath(logoUrl);
        updateEaConnAppSessionConfig(eaConnAppSessionConfig);


        // 更新服务号工作台logo
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setCustomerSessionPortrait(logoUrl);
        serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, CommonConstant.IS_LINK_SERVICE);

        // 通讯用户重新拉取服务号列表
        serviceManager.notifyUserViewAsync(user, appId);
    }

    private void updateEaConnAppSessionConfig (EaConnAppSessionConfig eaConnAppSessionConfig){
        MsgBaseResult updateEaConnAppSessionConfigResult = msgSessionService.updateEaConnAppSessionConfig(eaConnAppSessionConfig);
        if (!updateEaConnAppSessionConfigResult.isSuccess()) {
            logger.error("msgSessionService.updateEaConnAppSessionConfig failed! config[{}], result[{}]",
                    eaConnAppSessionConfig, updateEaConnAppSessionConfigResult);
            throw new BizException(updateEaConnAppSessionConfigResult);
        }
    }

    @Override
    public void updateAppDesc(FsUserVO user, String appId, String upstreamEa, String appDesc) {
        // 更新下游服务号Session
        EaConnAppSessionConfig eaConnAppSessionConfig  = new EaConnAppSessionConfig();
        eaConnAppSessionConfig.setAppId(appId);
        eaConnAppSessionConfig.setUpstreamEnterprise(upstreamEa);
        eaConnAppSessionConfig.setDescription(appDesc);
        updateEaConnAppSessionConfig(eaConnAppSessionConfig);

        // 更新服务号工作台Session
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setAdminDescription(appDesc);
        platformMetaSessionVO.setCustomerDescription(appDesc);
        serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, CommonConstant.IS_LINK_SERVICE);

        // 通讯用户重新拉取服务号列表
        serviceManager.notifyUserViewAsync(user, appId);
    }

    @Override
    public void updateAppName(FsUserVO user, String appId, String upstreamEa, String appName) {
        // 更新下游服务号Session
        EaConnAppSessionConfig eaConnAppSessionConfig  = new EaConnAppSessionConfig();
        eaConnAppSessionConfig.setAppId(appId);
        eaConnAppSessionConfig.setUpstreamEnterprise(upstreamEa);
        eaConnAppSessionConfig.setName(appName);
        updateEaConnAppSessionConfig(eaConnAppSessionConfig);

        // 更新服务号工作台Session
        String workBenchName = appName + CenterConstants.SERVICE_NUMBER_MSG;
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setCustomerSessionName(workBenchName);
        serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, CommonConstant.IS_LINK_SERVICE);

        // 通讯用户重新拉取服务号列表
        serviceManager.notifyUserViewAsync(user, appId);
    }

    @Override
    public boolean isLinkService(String appId) {
        OpenAppDO appDO = appManager.loadAppBrief(appId);
        return appDO.getAppType() == AppCenterEnum.AppType.LINK_SERVICE.value();
    }

    @Override
    public UserPositionResult getUserPosition(FsUserVO user) {
        HeaderObj headerObj = HeaderObj.newInstance(user.getEnterpriseAccount(), null, null, null);
        RestResult<UserPositionResult> userPositionResult = enterpriseRelationService.getUserPosition(headerObj, GetUserPositionArg.builder().ea(user.getEa()).fsUserId(user.getUserId()).build());
        if (!userPositionResult.isSuccess() || Objects.isNull(userPositionResult.getData())) {
            logger.warn("getUserPosition error, enterpriseRelationService.getUserPosition failed , user[{}], result[{}]", user, userPositionResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "权限校验异常"); // ignoreI18n
        }
        return userPositionResult.getData();
    }

    @Override
    public boolean isUpstreamLinkAdmin(FsUserVO user) {
        UserPositionResult userPosition = getUserPosition(user);
        return userPosition == null ? false : userPosition.getIsUpEaConnAdmin();
    }

    @Override
    public boolean isDownstreamLinkAdmin(FsUserVO user) {
        UserPositionResult userPosition = getUserPosition(user);
        return userPosition == null ? false : userPosition.getIsDownEaConnAdmin();
    }

    @Override
    public boolean isLinkServiceAdmin(FsUserVO user){
        List<Integer> appTypes = new ArrayList<>();
        appTypes.add(AppCenterEnum.AppType.LINK_SERVICE.value());
        BaseResult<Boolean> serviceAdmin = openAppAdminService.isServiceAdmin(user.getEnterpriseAccount(), user.getUserId(), appTypes);
        if (!serviceAdmin.isSuccess()) {
            logger.warn("isLinkServiceAdmin error, openAppAdminService.isServiceAdmin failed, user[{}], result[{}]", user, serviceAdmin);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "权限校验异常"); // ignoreI18n
        }
        return serviceAdmin.getResult();
    }

    @Override
    public boolean isUpstream(FsUserVO user) {
        HeaderObj headerObj = HeaderObj.newInstance(user.getEnterpriseAccount(), null, null, null);
        RestResult<Boolean> upstream = enterpriseRelationService.isUpstream(headerObj, IsUpstreamArg.builder().ea(user.getEa()).build());
        if (!upstream.isSuccess()) {
            logger.warn("isUpstream error, enterpriseRelationService.isUpstream failed, user[{}], result[{}]", user, upstream);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "权限校验异常"); // ignoreI18n
        }
        return upstream.getData() == null ? false : upstream.getData();
    }
    
    @Override
    public boolean isLinkSubChannelDisplay(String fsEa, Integer userId){
        HeaderObj headerObj = HeaderObj.newInstance(fsEa, null, null, null);
        RestResult<Boolean> relationChannelVisible = linkAppService.isRelationChannelVisible(headerObj, IsRelationChannelVisibleArg.builder().ea(fsEa).fsUserId(userId).build());
        if (!relationChannelVisible.isSuccess()) {
            logger.warn("isLinkSubChannelDisplay error, linkAppService.isRelationChannelVisible failed, fsEa[{}], " +
                    "userId[{}], result[{}]", fsEa, userId, relationChannelVisible);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "权限校验异常"); // ignoreI18n
        }
        return relationChannelVisible.getData();//断用户是否有企业互联子频道(除互联服务号之外)使用权限
    }

    @Override
    public List<OpenLinkServiceVO> getLinkServiceByDownstream(String downstreamEa){
        if(StringUtils.isBlank(downstreamEa)){
            logger.warn("getLinkServiceByDownstream error, downstreamEa[{}]",downstreamEa);
            throw new BizException(AjaxCode.PARAM_ERROR, "参数异常"); // ignoreI18n
        }
        HeaderObj headerObj = new HeaderObj();
        RestResult<List<CustomerServiceAuthResult>> listResult = customerServiceService.listDownstreamVisibleCustomerService(headerObj, ListDownstreamVisibleCustomerServiceArg.builder().downstreamEa(downstreamEa).build());
        if (!listResult.isSuccess()) {
            logger.warn("customerServiceService.listDownstreamVisibleCustomerService error, downstreamEa[{}], listResult[{}]", downstreamEa, listResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "系统异常"); // ignoreI18n
        }
        List<CustomerServiceAuthResult> customerServiceAuthVoList = listResult.getData();
        List<OpenLinkServiceVO> openLinkServiceVOList = Lists.newArrayList();

        if(!CollectionUtils.isEmpty(customerServiceAuthVoList)){
            customerServiceAuthVoList.forEach(customerServiceAuthVo -> {
                OpenLinkServiceVO openLinkServiceVO = new OpenLinkServiceVO();
                List<String> appIds = customerServiceAuthVo.getCustomServiceIds();
                if(!CollectionUtils.isEmpty(appIds)){
                    List<OpenAppVO> openAppVOList = appMessageManager.loadOpenAppByIds(appIds);
                    openLinkServiceVO.setDownstreamEa(customerServiceAuthVo.getDownstreamEa());
                    openLinkServiceVO.setCustomServiceIds(customerServiceAuthVo.getCustomServiceIds());
                    openLinkServiceVO.setOpenAppVOList(openAppVOList);
                    EnterpriseSimpleResult upstreamEaInfo = customerServiceAuthVo.getUpstreamEaInfo();
                    if(!Objects.isNull(upstreamEaInfo)){
                        openLinkServiceVO.setEnterpriseAccount(upstreamEaInfo.getEnterpriseAccount());
                        openLinkServiceVO.setEnterpriseName(upstreamEaInfo.getEnterpriseName());
                        openLinkServiceVO.setEnterpriseShortName(upstreamEaInfo.getEnterpriseShortName());
                        openLinkServiceVO.setEnterpriseShortNameSpell(upstreamEaInfo.getEnterpriseShortNameSpell());
                    }
                }
                openLinkServiceVOList.add(openLinkServiceVO);
            });
        }
        logger.info("getLinkServiceByDownstream, customerServiceService.listDownstreamVisibleCustomerService.result[{}]," +
                " method return openLinkServiceVOList[{}]", listResult, openLinkServiceVOList);
        return openLinkServiceVOList;
    }

}
