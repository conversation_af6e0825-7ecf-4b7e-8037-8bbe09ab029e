package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.model.MessageStarForm;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.msg.constant.CustomerServiceMsgType;
import com.facishare.open.msg.model.QueryAllStarMessageVO;
import com.facishare.open.msg.model.StartMessagesVO;
import com.facishare.open.msg.model.UpdateMsgStarStatusVO;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Description: 消息星标
 * User: zhouq
 * Date: 2016/5/24
 */
@Controller
@RequestMapping("/open/appcenter/message/star")
public class MessageStarController extends BaseController {

    @Resource
    private MessageExhibitionService messageExhibitionService;

    /**
     * 消息新增星标和取消星标
     *
     * @param user 用户信息
     * @param form 消息集合
     * @return 状态
     */
    @RequestMapping("/updateMessageStarStatus")
    @ResponseBody
    public AjaxResult updateMessageStarStatus(@ModelAttribute FsUserVO user, @RequestBody MessageStarForm form) {
        checkParamNotBlank(form, "请选择消息"); // ignoreI18n
        checkParamNotBlank(form.getMessageId(), "请选择消息"); // ignoreI18n
        checkParamRegex("" + form.getStarStatus(), "[0-1]{1}", "请填写有效的状态"); // ignoreI18n

        AppResult appResult = openAppService.loadOpenApp(form.getAppId());
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], fsUserVO[{}], appResult[{}]", form.getAppId(), user, appResult);
            throw new BizException(appResult);
        }

        UpdateMsgStarStatusVO updateMsgStarStatusVO = new UpdateMsgStarStatusVO();
        updateMsgStarStatusVO.setUpStreamEa(user.getEnterpriseAccount());
        updateMsgStarStatusVO.setMessageId(form.getMessageId());
        updateMsgStarStatusVO.setMarkUserId(String.valueOf(user.getUserId()));
        updateMsgStarStatusVO.setStarStatus(form.getStarStatus());
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            updateMsgStarStatusVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            updateMsgStarStatusVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }

        MessageExhibitionResult<Void> result = messageExhibitionService.updateMsgStarStatus(updateMsgStarStatusVO);
        if (!result.isSuccess()) {
            logger.warn("failed to call messageExhibitionService.updateMsgStarStatus, user[{}], form[{}], updateMsgStarStatusVO, result[{}]", user, form, updateMsgStarStatusVO, result);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "消息新增星标或者取消星标失败"); // ignoreI18n
        }

        return new AjaxResult(null);
    }

    /**
     * 获取星标消息列表
     *
     * @param user 用户信息
     * @param form 消息集合
     * @return 星标消息列表
     */
    @RequestMapping("/queryMessageStars")
    @ResponseBody
    public AjaxResult queryStarMessages(@ModelAttribute FsUserVO user, @RequestBody MessageStarForm form) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ea", user.getEnterpriseAccount());
        paramMap.put("appId", form.getAppId());

        AppResult appResult = openAppService.loadOpenApp(form.getAppId());
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], fsUserVO[{}], appResult[{}]", form.getAppId(), user, appResult);
            throw new BizException(appResult);
        }

        QueryAllStarMessageVO queryAllStarMessageVO = new QueryAllStarMessageVO();
        queryAllStarMessageVO.setCurrentPage(form.getCurrentPage());
        queryAllStarMessageVO.setPageSize(form.getPageSize());
        queryAllStarMessageVO.setUpStreamEa(user.getEnterpriseAccount());
        queryAllStarMessageVO.setAppId(form.getAppId());
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            queryAllStarMessageVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            queryAllStarMessageVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }
        MessageExhibitionResult<Pager<StartMessagesVO>> result = messageExhibitionService.queryAllStarMessages(queryAllStarMessageVO);
        if (!result.isSuccess()) {
            logger.warn("failed to call messageExhibitionService.queryAllStarMessages, user[{}], form[{}], queryAllStarMessageVO, result[{}]", user, form, queryAllStarMessageVO, result);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "获取星标消息列表失败"); // ignoreI18n
        }
        return new AjaxResult(result.getData());
    }

    /**
     * 获取导出消息记录条数，大于1000，前端需要提示正在加载中
     * @param user 用户信息
     * @param form 消息集合
     * @return 总记录数
     */
    @RequestMapping("/queryCountExportMessages")
    @ResponseBody
    public AjaxResult queryCountExportMessages(@ModelAttribute FsUserVO user, @RequestBody MessageStarForm form) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ea", user.getEnterpriseAccount());
        paramMap.put("appId", form.getAppId());
        paramMap.put("starMark", form.getStarStatus());
        MessageExhibitionResult<Long> result = messageExhibitionService.countExportMessages(form.getBeginTime(), form.getEndTime(), paramMap);
        if (!result.isSuccess()) {
            logger.warn("failed to call messageExhibitionService.countExportMessages, user[{}], form[{}], result[{}]", user, form, result);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "获取导出消息记录条数失败"); // ignoreI18n
        }
        return new AjaxResult(result.getData());
    }

}
