package com.facishare.open.app.center.controller;

import com.facishare.eservice.cases.api.model.appcenter.AppCenterWorkOrderMenuModel;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.enums.BuriedPointBizEnum;
import com.facishare.open.app.center.common.AppCenterConstants;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.cons.OperationTypeConstant;
import com.facishare.open.app.center.cons.ServiceFeatureTypeEnum;
import com.facishare.open.app.center.manager.AppMessageToMqManager;
import com.facishare.open.app.center.manager.CustomMenuManager;
import com.facishare.open.app.center.manager.EserviceManager;
import com.facishare.open.app.center.manager.ServiceNumberManager;
import com.facishare.open.app.center.model.AppMenuUrlForm;
import com.facishare.open.app.center.model.CustomMenuForm;
import com.facishare.open.app.center.model.MenuFormVO;
import com.facishare.open.app.center.model.ServiceNumberForm;
import com.facishare.open.app.center.mq.item.tags.AppCenterMqTagsConstant;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.operating.center.api.service.OperationLogService;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.cloud.utils.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义菜单相关接口
 *
 * <AUTHOR>
 *         2015年12月8日
 */
@Controller
@RequestMapping("/open/appcenter/custom/menu")
public class CustomMenuController extends BaseController {

    @Resource
    private CustomMenuManager customMenuManager;

    @Resource
    private AppMessageToMqManager appMessageToMqManager;

    @Resource
    private ServiceNumberManager serviceNumberManager;

    @Resource
    private OperationLogService operationLogService;
    @Resource
    private EserviceManager eserviceManager;

    /**
     * 启动自定义菜单.
     *
     * @param user           操作者.
     * @param customMenuForm 取应用id.
     * @return 是否启动成功
     */
    @RequestMapping("/enableCustomMenu")
    @ResponseBody
    public AjaxResult enableCustomMenu(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                       @RequestBody CustomMenuForm customMenuForm) {
        checkParamNotBlank(customMenuForm, "请填写表单"); // ignoreI18n
        final String appId = customMenuForm.getAppId();
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkAppAdmin(user, customMenuForm.getAppId());
        checkService(customMenuForm.getAppId());

        customMenuManager.enableCustomMenu(user, appId);
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.putAll(JsonUtils.parseToMap(customMenuForm));
            map.put("appId", appId);
            map.put("action", BuriedPointBizEnum.CUSTOM_APP_ENABLE_MENU.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CUSTOM_APP_ENABLE_MENU, map, e);
        }
        //运营中心记录日志(自定义菜单开)（fs-open-app-operating）,从mq改成异步调用
        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                user.getUserId(), System.currentTimeMillis()+ "", "开启了自定义菜单", OperationTypeConstant.WORKSHEET_ENABLE)); // ignoreI18n
        return SUCCESS;
    }

    /**
     * 关闭自定义菜单.
     *
     * @param user           操作人.
     * @param customMenuForm 取应用id.
     * @return 是否关闭成功
     */
    @RequestMapping("/disableCustomMenu")
    @ResponseBody
    public AjaxResult disableCustomMenu(@ModelAttribute FsUserVO user, @ModelAttribute String lang, @RequestBody CustomMenuForm customMenuForm) {
        checkParamNotBlank(customMenuForm, "请填写表单"); // ignoreI18n
        final String appId = customMenuForm.getAppId();
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkAppAdmin(user, appId);
        checkService(customMenuForm.getAppId());

        customMenuManager.disableCustomMenu(user, appId);
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.putAll(JsonUtils.parseToMap(customMenuForm));
            map.put("appId", appId);
            map.put("action", BuriedPointBizEnum.CUSTOM_APP_DISABLE_MENU.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CUSTOM_APP_DISABLE_MENU, map, e);
        }

        //运营中心记录日志(自定义菜单关)（fs-open-app-operating）,从mq改成异步调用
        CommonThreadPoolUtils.getExecutor().execute(() -> operationLogService.commonSaveOperationLog(user.getEnterpriseAccount(), appId,
                        user.getUserId(), System.currentTimeMillis() + "", "停用了自定义菜单", OperationTypeConstant.WORKSHEET_STOP)); // ignoreI18n
        return SUCCESS;
    }

    /**
     * 查询应用的自定义菜单。
     *
     * @param user 操作者.
     * @return 可操作的应用列表
     */
    @RequestMapping("/loadCustomMenuListByAppId")
    @ResponseBody
    public AjaxResult loadCustomMenuListByAppId(@ModelAttribute FsUserVO user, @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkAppAdmin(user, appId);
        checkService(appId);

        return new AjaxResult(customMenuManager.loadCustomMenuListByAppId(user, appId));
    }

    /**
     * 查询应用的自定义菜单。
     *
     * @param user 操作者.
     * @return 可操作的应用列表
     */
    @RequestMapping("/saveCustomMenuList")
    @ResponseBody
    public AjaxResult saveCustomMenuList(@ModelAttribute FsUserVO user, @RequestBody CustomMenuForm customMenuForm) {
        checkParamNotBlank(customMenuForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(customMenuForm.getAppId(), "请选择应用"); // ignoreI18n
        List<MenuFormVO> menus = customMenuForm.getMenus();

//        try {
//            customMenuForm.checkParams();
//        } catch (IllegalArgumentException e) {
//            BizException exception = new BizException(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION, e);
//            exception.setErrCode(AjaxCode.PARAM_ERROR);
//            exception.setErrDesc(e.getLocalizedMessage());
//            throw exception;
//        }
        checkAppAdmin(user, customMenuForm.getAppId());
        checkService(customMenuForm.getAppId());


        customMenuManager.saveCustomMenuList(user, customMenuForm);

        Map<String, Object> map = new HashMap<>();
        try {
            map.put("fsEa", user.getEnterpriseAccount());
            map.put("userId", user.getUserId());
            map.put("menuNum", menus.size());
            map.put("appId", customMenuForm.getAppId());
            if (menus.size() > 0) {
                map.put("menuNunFirst", menus.get(0).getChildren().size());
            }

            if (menus.size() > 1) {
                map.put("menuNunSecond", menus.get(1).getChildren().size());
            }

            if (menus.size() > 2) {
                map.put("menuNunThird", menus.get(2).getChildren().size());
            }
            map.put("action", BuriedPointBizEnum.CUSTOM_APP_MENU_DATA.getAction());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), map);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.CUSTOM_APP_MENU_DATA, map, e);
        }
        return SUCCESS;
    }

    /**
     * 查询自定义菜单可以绑定的应用列表
     *
     * @param user           用户
     * @param appMenuUrlForm 菜单属性
     * @return 应用列表
     */
    @RequestMapping("/queryIsBindApps")
    @ResponseBody
    public AjaxResult queryIsBindApps(@ModelAttribute FsUserVO user, @RequestBody AppMenuUrlForm appMenuUrlForm) {

        checkParamNotBlank(appMenuUrlForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(appMenuUrlForm.getAppId(), "请选择应用"); // ignoreI18n

        List<AppMenuUrlForm> appMenuUrls = new ArrayList<>();

        if (ConfigCenter.grayToEserviceWorkOrder(user.getEa())) {
            boolean openEserviceWorkOrder = eserviceManager.isOpenEserviceWorkOrder(user.getEa(), user.getUserId(), appMenuUrlForm.getAppId());
            if (openEserviceWorkOrder) {
                List<AppMenuUrlForm> menus = eserviceManager.getEserviceWorkOrderMenu(user.getEa(), user.getUserId());
                if (CollectionUtils.isNotEmpty(menus)) {
                    AppMenuUrlForm appMenuUrl = new AppMenuUrlForm();
                    appMenuUrl.setBindAppKey(ServiceFeatureTypeEnum.ESERVICE_WORK_ORDER.getType());
                    appMenuUrl.setBindAppName(ServiceFeatureTypeEnum.ESERVICE_WORK_ORDER.getDesc());
                    appMenuUrl.setMenuUrl(ServiceFeatureTypeEnum.ESERVICE_WORK_ORDER.getType());
                    appMenuUrl.setSubMenu(menus);
                    appMenuUrls.add(appMenuUrl);
                }
            }
        }

        //2、服务工单
        Integer workOrderPaasStatus = serviceNumberManager.queryWorkOrderPaasStatus(user, new ServiceNumberForm(appMenuUrlForm.getAppId()));
        if (1 == workOrderPaasStatus){
            List<AppMenuUrlForm> workOrderAppMenuUrlForms = customMenuManager.queryWorkOrderTemplateByApp(user, appMenuUrlForm.getAppId());
            if (CollectionUtils.isNotEmpty(workOrderAppMenuUrlForms)) {
                AppMenuUrlForm appMenuUrl = new AppMenuUrlForm();
                appMenuUrl.setBindAppKey(ServiceFeatureTypeEnum.WORK_ORDER_PAAS.getType());
                appMenuUrl.setBindAppName(ServiceFeatureTypeEnum.WORK_ORDER_PAAS.getDesc());
                appMenuUrl.setMenuUrl(ServiceFeatureTypeEnum.WORK_ORDER_PAAS.getType());
                appMenuUrl.setSubMenu(workOrderAppMenuUrlForms);
                appMenuUrls.add(appMenuUrl);
            }

        }

        //3、审批单 审批单已下线
//        Integer approvalStatus = serviceNumberManager.queryApprovalStatus(user, new ServiceNumberForm(appMenuUrlForm.getAppId()));
//        if (1 == approvalStatus){
//            List<AppMenuUrlForm> appMenuUrlForms = customMenuManager.queryApprovalByApp(user, appMenuUrlForm.getAppId());
//            AppMenuUrlForm appMenuUrl = new AppMenuUrlForm();
//            appMenuUrl.setBindAppKey("approval");
//            appMenuUrl.setBindAppName("审批单");
//            appMenuUrl.setMenuUrl("approval");
//            appMenuUrl.setSubMenu(appMenuUrlForms);
//            appMenuUrls.add(appMenuUrl);
//        }
        return new AjaxResult(appMenuUrls);
    }
}
