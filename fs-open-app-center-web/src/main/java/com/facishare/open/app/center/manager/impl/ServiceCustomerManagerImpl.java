package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.Circle;
import com.facishare.open.app.center.api.model.Employee;
import com.facishare.open.app.center.api.model.EmployeeInfo;
import com.facishare.open.app.center.api.model.enums.OpenCustomerRoleEnum;
import com.facishare.open.app.center.api.model.enums.OpenCustomerSrcTypeEnum;
import com.facishare.open.app.center.api.model.vo.OpenCustomerVO;
import com.facishare.open.app.center.api.model.vo.OuterServiceWechatVO;
import com.facishare.open.app.center.api.service.OpenAppAddressBookCircleService;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.api.service.outer.OpenCustomerService;
import com.facishare.open.app.center.api.service.outer.OuterServiceWechatService;
import com.facishare.open.app.center.manager.ServiceCustomerManager;
import com.facishare.open.app.center.model.OpenCustomerForm;
import com.facishare.open.app.center.model.outers.args.OpenCustomerQueryPagerArgs;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.ConvertUtil;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.entity.FsUserBindInfo;
import com.facishare.wechat.proxy.model.vo.QrCodeVo;
import com.facishare.wechat.proxy.model.vo.QueryFsUserBindInfoVo;
import com.facishare.wechat.proxy.model.wx.IndividualQrResult;
import com.facishare.wechat.proxy.service.QrCodeService;
import com.facishare.wechat.proxy.service.WechatUserBindInfoService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 开平客服
 * Created by chenzs on 2016/11/2.
 */
@Service
public class ServiceCustomerManagerImpl implements ServiceCustomerManager {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private OpenCustomerService openCustomerService;
    @Autowired
    private OpenAppAddressBookEmployeeService openAppAddressBookEmployeeService;
    @Autowired
    private OpenAppAddressBookCircleService openAppAddressBookCircleService;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private WechatUserBindInfoService wechatUserBindInfoService;
    @Autowired
    private QrCodeService qrCodeService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Override
    public Pager<OpenCustomerVO> queryPager(FsUserVO fsUserVO, OpenCustomerQueryPagerArgs openCustomerQueryPagerArgs) {
        List<OpenCustomerVO> openCustomerVOsInCurrentPage = new ArrayList<>();  //currentPage那一页（结果页）的数据
        List<Integer> userIdsInCurrentPage;                                     //currentPage那一页（结果页）的 userId
        List<Integer> employeeIdsInSearch; //搜索范围内的employeeId
        List<EmployeeInfo> employeeInfos;  //员工信息

        Pager<OpenCustomerVO> pager = new Pager<>();                            //返回结果
        pager.setCurrentPage(openCustomerQueryPagerArgs.getCurrentPage());
        pager.setPageSize(openCustomerQueryPagerArgs.getPageSize());
        String searchText = openCustomerQueryPagerArgs.getSearchText();
        List<Integer> roleList = null;
        if (null != openCustomerQueryPagerArgs.getRole()){
            roleList = Lists.newArrayList(openCustomerQueryPagerArgs.getRole());
        }

        //1、查询开平客服信息
        BaseResult<List<OpenCustomerVO>> customersResult = openCustomerService.query(fsUserVO, fsUserVO.getEnterpriseAccount(), openCustomerQueryPagerArgs.getAppId(), roleList, CommonConstant.VALID);
        if (!customersResult.isSuccess()) {
            logger.error("openCustomerService.queryByFsEaAndRole : fsEa[{}], role[{}], status[{}], result[{}]",
                    fsUserVO.getEnterpriseAccount(), openCustomerQueryPagerArgs.getRole(), CommonConstant.VALID, customersResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询客服信息失败"); // ignoreI18n
        }
        List<OpenCustomerVO> openCustomerVOs = customersResult.getResult();

        if (CollectionUtils.isEmpty(openCustomerVOs)) {
            pager.setData(Lists.newArrayList());
            pager.setParams(null);
            return pager;
        }

        /**
         * 2、查通讯录，获取员工信息
         * 如果有搜索：a1、查通讯录，a2、过滤出搜索要的，a3、内存分页
         * 如果无搜索：b1、先分页，  b2、查通讯录
         */
        if (!Strings.isNullOrEmpty(searchText)) {
            searchText = searchText.trim();
        }
        if (!Strings.isNullOrEmpty(searchText)) { //有搜索
            //a1、查通讯录
            List<Integer> employeeIds = openCustomerVOs.stream().map(OpenCustomerVO::getUserId).collect(Collectors.toList());
            BaseResult<List<EmployeeInfo>> employeeInfosResult = openAppAddressBookEmployeeService.getEmployeeInfosNoAdminId(fsUserVO.getEnterpriseAccount(), employeeIds);
            if (!employeeInfosResult.isSuccess()) {
                logger.error("employeeService.getEmployeeInfosNoAdminId: fsEa[{}], employeeIds[{}], result[{}]",
                        fsUserVO.getEnterpriseAccount(), employeeIds, employeeInfosResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询员工信息失败"); // ignoreI18n
            }
            employeeInfos = employeeInfosResult.getResult();

            //a2、过滤出包含 searchText 的 employeeId
            String searchTextLowCase = searchText.toLowerCase();            //忽略大小写
            employeeIdsInSearch = employeeInfos.stream().filter(employeeInfo -> employeeInfo.getName().toLowerCase().indexOf(searchTextLowCase) > -1)
                    .map(Employee::getEmployeeId).collect(Collectors.toList());

            //过滤出包含 searchText 的 OpenCustomerVO
            List<Integer> employeeIdsInSearchTemp = Lists.newArrayList(employeeIdsInSearch);
            List<OpenCustomerVO> openCustomerVOsInSearch = openCustomerVOs.stream()
                    .filter(openCustomerVO -> employeeIdsInSearchTemp.contains(openCustomerVO.getUserId()))
                    .collect(Collectors.toList());

            //a3、分页信息
            pager.setRecordSize(openCustomerVOsInSearch.size());
            //取在currentPage那一页（结果页）的OpenCustomerVO，即从openCustomerVOs中取出openCustomerVOsInCurrentPage
            if (pager.getRecordSize() >= pager.getCurrentPage()) {
                int limit = pager.offset() + pager.getPageSize();
                for (int i = pager.offset(); i < limit && i < pager.getRecordSize(); i++) {
                    openCustomerVOsInCurrentPage.add(openCustomerVOsInSearch.get(i));
                }
            }
        } else {
            //b1、分页信息
            pager.setRecordSize(openCustomerVOs.size());
            //取在currentPage那一页（结果页）的OpenCustomerVO，即从openCustomerVOs中取出openCustomerVOsInCurrentPage
            if (pager.getRecordSize() >= pager.getCurrentPage()) {
                int limit = pager.offset() + pager.getPageSize();
                for (int i = pager.offset(); i < limit && i < pager.getRecordSize(); i++) {
                    openCustomerVOsInCurrentPage.add(openCustomerVOs.get(i));
                }
            }

            //b2、查通讯录
            employeeIdsInSearch = openCustomerVOsInCurrentPage.stream().map(OpenCustomerVO::getUserId).collect(Collectors.toList());
            BaseResult<List<EmployeeInfo>> employeeInfosResult = openAppAddressBookEmployeeService.getEmployeeInfosNoAdminId(fsUserVO.getEnterpriseAccount(), employeeIdsInSearch);
            if (!employeeInfosResult.isSuccess()) {
                logger.error("employeeService.getEmployeeInfosNoAdminId: fsEa[{}], employeeIds[{}], result[{}]",
                        fsUserVO.getEnterpriseAccount(), employeeIdsInSearch, employeeInfosResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询员工信息失败"); // ignoreI18n
            }
            employeeInfos = employeeInfosResult.getResult();
        }
        //客服的员工信息，转成 map<employeeId, EmployeeInfo>
        Map<Integer, EmployeeInfo> employeeInfoMap = employeeInfos.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeId, (p) -> p));

        if (CollectionUtils.isEmpty(openCustomerVOsInCurrentPage)) {
            pager.setData(Lists.newArrayList());
            pager.setParams(null);
            return pager;
        }

        //currentPage那一页（结果页）的 userId
        userIdsInCurrentPage = openCustomerVOsInCurrentPage.stream().map(OpenCustomerVO::getUserId).collect(Collectors.toList());

        //3、查询关联的微信用户数
        Map<Long, FsUserBindInfo> fsUserBindInfoResultMap;
        QueryFsUserBindInfoVo queryFsUserBindInfoVo = new QueryFsUserBindInfoVo();
        queryFsUserBindInfoVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        queryFsUserBindInfoVo.setFsUserIdList(ConvertUtil.int2long(userIdsInCurrentPage));
        queryFsUserBindInfoVo.setWxAppId(openCustomerVOs.get(0).getWxAppId());
        ModelResult<List<FsUserBindInfo>> fsUserBindInfoResult = wechatUserBindInfoService.queryWechatUserAmountByFsUsers(queryFsUserBindInfoVo);

        if (!fsUserBindInfoResult.isSuccess()) {
            logger.error("wechatUserBindInfoService.queryWechatUserAmountByFsUsers: queryFsUserBindInfoVo[{}], result[{}]", queryFsUserBindInfoVo, fsUserBindInfoResult);

            // 降级，查询失败，数量都设置为 -1
            fsUserBindInfoResultMap = new HashMap<>();
            FsUserBindInfo fsUserBindInfo;
            for (Integer userId : userIdsInCurrentPage) {
                fsUserBindInfo = new FsUserBindInfo();
                fsUserBindInfo.setAmount(-1);
                fsUserBindInfoResultMap.put(userId.longValue(), fsUserBindInfo);
            }
        } else {
            List<FsUserBindInfo> fsUserBindInfos = fsUserBindInfoResult.getResult();
            //转成map  <userId:FsUserBindInfo>
            fsUserBindInfoResultMap = fsUserBindInfos.stream().collect(Collectors.toMap(FsUserBindInfo::getFsUserId, (p) -> p));
        }

        //4、查通讯录，获取部门信息
        /**
         * 4.1、获取需要查询的部门id
         *   a、如果EmployeeInfo中有主属部门 mainCircleId ，就用主属部门 mainCircleId
         *   b、如果EmployeeInfo中没主属部门 mainCircleId ，则用allCircleIds的第一个部门id（没有就不要了）
         */
        List<Integer> departmentIds = new ArrayList<>(); //需要查询的部门id
        EmployeeInfo employeeInfo;
        List<List<Integer>> allCircleIds;
        for (OpenCustomerVO openCustomerVO : openCustomerVOsInCurrentPage) {
            employeeInfo = employeeInfoMap.get(openCustomerVO.getUserId());
            if (null != employeeInfo) {
                //a、如果EmployeeInfo中有主属部门 mainCircleId ，就用主属部门 mainCircleId  (mainCircleId=0, 代表没有主属部门)
                if (null != employeeInfo.getMainCircleId() && 0 != employeeInfo.getMainCircleId().intValue()) {
                    if (!departmentIds.contains(employeeInfo.getMainCircleId())) {//去重，否则下面查询的部门结果转Map(以部门id为key)有问题      不要和上一级if放在一起
                        departmentIds.add(employeeInfo.getMainCircleId());
                    }
                }
                //b、如果EmployeeInfo中没主属部门 mainCircleId ，则用allCircleIds的第一个部门id
                else {
                    allCircleIds = employeeInfo.getAllCircleIds();
                    if (!CollectionUtils.isEmpty(allCircleIds)
                            && !CollectionUtils.isEmpty(allCircleIds.get(0))
                            && !departmentIds.contains(allCircleIds.get(0).get(0))) {  //去重，否则下面查询的部门结果转Map(以部门id为key)有问题
                        departmentIds.add(allCircleIds.get(0).get(0));  //第一个部门id加进来
                    }
                }
            }
        }
        //4.2、获取部门信息
        Map<Integer, Circle> circlesMap;  //<部门id:部门>
        if (!CollectionUtils.isEmpty(departmentIds)) {
            BaseResult<List<Circle>> circlesResult = openAppAddressBookCircleService.getCircles(fsUserVO.getEnterpriseAccount(), fsUserVO.getUserId(), departmentIds);
            if (!circlesResult.isSuccess()) {
                logger.error("openAppAddressBookCircleService.getCircle: enterpriseAccount[{}], userId[{}], departmentIds[{}], result[{}]",
                        fsUserVO.getEnterpriseAccount(), fsUserVO.getUserId(), departmentIds, circlesResult);
//            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询部门信息失败");  //降级
            }
            circlesMap = circlesResult.getResult().stream().collect(Collectors.toMap(Circle::getCircleId, (p) -> p));
        } else {
            circlesMap = new HashMap<>();
        }

        //5、添加信息
        pager.setData(this.addInfo(openCustomerVOsInCurrentPage, employeeInfoMap, fsUserBindInfoResultMap, circlesMap));
        pager.setParams(null);
        return pager;
    }

    /**
     * 完善openCustomerVOs的信息
     *
     * @param openCustomerVOs         开平客服信息
     * @param employeeInfoMap         员工信息
     * @param fsUserBindInfoResultMap 客服人员关联的外部联系人数量
     * @param circlesMap              部门信息
     * @return
     */
    private List<OpenCustomerVO> addInfo(List<OpenCustomerVO> openCustomerVOs, Map<Integer, EmployeeInfo> employeeInfoMap, Map<Long, FsUserBindInfo> fsUserBindInfoResultMap, Map<Integer, Circle> circlesMap) {
        List<OpenCustomerVO> result = new ArrayList<>();
        FsUserBindInfo fsUserBindInfo;  //客服人员关联的外部联系人信息
        EmployeeInfo employeeInfo;
        Circle circle;                  //要显示的部门
        String departmentName = "";     //部门名称
        String job = "";                //职务
        int wxUserNum = 0;              //关联微信用户用户数

        for (OpenCustomerVO openCustomerVO : openCustomerVOs) {
            //1、获取部门name
            /**
             * 1.1、如果EmployeeInfo中有主属部门，就用主属部门
             * 1.2、如果EmployeeInfo中没主属部门，则用allCircleIds的第一个部门（没有就不要了）
             */
            departmentName = "";
            employeeInfo = employeeInfoMap.get(openCustomerVO.getUserId());
            circle = null;
            if (null != employeeInfo){
                //1.1、如果EmployeeInfo中有主属部门，就用主属部门 (mainCircleId=0, 代表没有主属部门)
                if (null != employeeInfo.getMainCircleId() && 0 != employeeInfo.getMainCircleId().intValue()) {
                    circle = circlesMap.get(employeeInfo.getMainCircleId());
                }
                //1.2、如果EmployeeInfo中没主属部门，则用allCircleIds的第一个部门（没有就不要了）
                else {
                    List<List<Integer>> allCircleIds = employeeInfo.getAllCircleIds();
                    if (!CollectionUtils.isEmpty(allCircleIds) && !CollectionUtils.isEmpty(allCircleIds.get(0))) {
                        circle = circlesMap.get(allCircleIds.get(0).get(0));
                    }
                }

                if (circle != null) {
                    departmentName = circle.getName();
                }
            }

            //2、关联微信用户数
            fsUserBindInfo = fsUserBindInfoResultMap.get(openCustomerVO.getUserId().longValue());
            wxUserNum = fsUserBindInfo == null ? 0 : fsUserBindInfo.getAmount();

            //3、职务
            job = "";
            if (employeeInfoMap.get(openCustomerVO.getUserId()) != null
                    && !Strings.isNullOrEmpty(employeeInfoMap.get(openCustomerVO.getUserId()).getPost())) {
                job = employeeInfoMap.get(openCustomerVO.getUserId()).getPost();
            }

            //4、添加信息
            openCustomerVO.setUserName(employeeInfoMap.get(openCustomerVO.getUserId()).getName());
            openCustomerVO.setDepartmentName(departmentName);
            openCustomerVO.setJob(job);
            openCustomerVO.setSrcTypeName(OpenCustomerSrcTypeEnum.getByCode(openCustomerVO.getSrcType()).getDesc());
            openCustomerVO.setWxUserNum(wxUserNum);
            openCustomerVO.setRoleName(OpenCustomerRoleEnum.getByCode(openCustomerVO.getRole()).getDesc());
            result.add(openCustomerVO);
        }
        return result;
    }

    @Override
    public Map<String, Object> addOpenCustomer(FsUserVO fsUserVO, OpenCustomerForm openCustomerForm) {
        //查询外联服务号和微信服务号的关联状态和对应的wxAppId
        OuterServiceWechatVO outerServiceWechatVO = new OuterServiceWechatVO();
        outerServiceWechatVO.setFsEa(fsUserVO.getEnterpriseAccount());
        outerServiceWechatVO.setAppId(openCustomerForm.getAppId());
        outerServiceWechatVO.setStatus(CommonConstant.VALID);
        BaseResult<OuterServiceWechatVO> outerServiceWechatVOResult = outerServiceWechatService.queryOuterServiceWechat(outerServiceWechatVO);
        if (!outerServiceWechatVOResult.isSuccess()) {
            logger.error("outerServiceWechatService.queryOuterServiceWechat : outerServiceWechatVO[{}], result[{}]",
                    outerServiceWechatVO, outerServiceWechatVOResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询外联服务号和微信公众号信息失败"); // ignoreI18n
        } else if (outerServiceWechatVOResult.getResult() == null || StringUtils.isEmpty(outerServiceWechatVOResult.getResult().getWxAppId())) {
            logger.error("outerServiceWechatService.queryOuterServiceWechat : outerServiceWechatVO[{}], result[{}]",
                    outerServiceWechatVO, outerServiceWechatVOResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "外联服务号没有关联的微信公众号"); // ignoreI18n
        }

        //查询 fsEa，在 appId上的有效的客服
        BaseResult<List<OpenCustomerVO>> customersResult = openCustomerService.queryByAppId(fsUserVO, openCustomerForm.getAppId(), CommonConstant.VALID);
        if (!customersResult.isSuccess()) {
            logger.error("openCustomerService.queryByAppId : appId[{}], status[{}], result[{}]",
                    openCustomerForm.getAppId(), CommonConstant.VALID, customersResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询客服信息失败"); // ignoreI18n
        }
        List<OpenCustomerVO> existOpenCustomerVOs = customersResult.getResult();
        List<Integer> existUserIds = existOpenCustomerVOs.stream().map(OpenCustomerVO::getUserId).collect(Collectors.toList());

        //等待添加的客服人员中，还没添加的
        List<Integer> noExistUserIds = openCustomerForm.getUserIds().stream().filter(userId -> !existUserIds.contains(userId)).collect(Collectors.toList());

        //等待添加的客服人员中，已经添加的
        List<Integer> updateExistUserIds = openCustomerForm.getUserIds().stream().filter(userId -> existUserIds.contains(userId)).collect(Collectors.toList());
        List<String> updateExistCustomerIds = existOpenCustomerVOs.stream()
                .filter(openCustomerVO -> updateExistUserIds.contains(openCustomerVO.getUserId()))
                .map(OpenCustomerVO::getCustomerId).collect(Collectors.toList());

        //已添加的客服人员：更新 gmtModified
        if (!CollectionUtils.isEmpty(updateExistCustomerIds)) {
            BaseResult<Void> updateCustomerResult = openCustomerService.updateOpenCustomersGmtModified(fsUserVO, updateExistCustomerIds);
            if (!updateCustomerResult.isSuccess()) {
                logger.error("openCustomerService.updateOpenCustomersGmtModified : fsUserVO[{}], updateExistCustomerIds[{}], result[{}]",
                        fsUserVO, updateExistCustomerIds, updateCustomerResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "批量更新客服修改时间失败"); // ignoreI18n
            }
        }

        //没添加的客服人员：添加
        if (!CollectionUtils.isEmpty(noExistUserIds)) {
            //整理要添加的数据
            OpenCustomerVO addOpenCustomerVO = new OpenCustomerVO();
            addOpenCustomerVO.setFsEa(fsUserVO.getEnterpriseAccount());
            addOpenCustomerVO.setAppId(openCustomerForm.getAppId());
            addOpenCustomerVO.setWxAppId(outerServiceWechatVOResult.getResult().getWxAppId());
            addOpenCustomerVO.setSrcType(OpenCustomerSrcTypeEnum.BY_HAND.getCode()); //手动添加
            addOpenCustomerVO.setRole(OpenCustomerRoleEnum.COMMON.getCode());        //普通客服

            //还没有添加的客服人员：添加开平客服信息
            BaseResult<Void> createCustomersResult = openCustomerService.createOpenCustomers(fsUserVO, addOpenCustomerVO, noExistUserIds);
            if (!customersResult.isSuccess()) {
                logger.error("openCustomerService.createOpenCustomer : fsUserVO[{}], addOpenCustomerVO[{}], noExistUserIds[{}], result[{}]",
                        fsUserVO, addOpenCustomerVO, noExistUserIds, createCustomersResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "添加客服信息失败"); // ignoreI18n
            }
        }

        //结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", existUserIds.size() + noExistUserIds.size());
        result.put("updateNum", CollectionUtils.isEmpty(updateExistCustomerIds) ? 0 : updateExistCustomerIds.size());
        result.put("addNum", CollectionUtils.isEmpty(noExistUserIds) ? 0 : noExistUserIds.size());
        return result;
    }

    @Override
    public void updateOpenCustomerRole(FsUserVO fsUserVO, OpenCustomerVO openCustomerVO) {
        //更新开平客服信息
        BaseResult<Void> customerResult = openCustomerService.updateOpenCustomerRole(fsUserVO, openCustomerVO);
        if (!customerResult.isSuccess()) {
            logger.error("openCustomerService.updateOpenCustomerRole : fsUserVO[{}], openCustomerVO[{}], result[{}]",
                    fsUserVO, openCustomerVO, customerResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "更新客服信息失败"); // ignoreI18n
        }
    }

    @Override
    public void checkAllowToDelete(FsUserVO fsUserVO, OpenCustomerVO openCustomerVO) {
        //查询开平客服信息
        BaseResult<OpenCustomerVO> customerResult = openCustomerService.queryByCustomerId(fsUserVO, openCustomerVO.getCustomerId(), CommonConstant.VALID);
        if (!customerResult.isSuccess()) {
            logger.error("openCustomerService.loadByCustomerId : fsUserVO[{}], customerId[{}], result[{}]",
                    fsUserVO, openCustomerVO.getCustomerId(), customerResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询客服信息失败"); // ignoreI18n
        }
        OpenCustomerVO openCustomerVOResult = customerResult.getResult();
        if (null == openCustomerVOResult) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "客服信息不存在"); // ignoreI18n
        }

        /**
         * 判断：
         * 1、手动添加的客服可以删除
         * 2、CRM同步的客服不可删除
         */
        if (OpenCustomerSrcTypeEnum.CRM.getCode() == openCustomerVOResult.getSrcType()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "CRM同步的客服不允许删除"); // ignoreI18n
        }

        //查询关联的外部联系人数量
        QueryFsUserBindInfoVo queryFsUserBindInfoVo = new QueryFsUserBindInfoVo();
        queryFsUserBindInfoVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        queryFsUserBindInfoVo.setFsUserIdList(Lists.newArrayList(openCustomerVOResult.getUserId().longValue()));
        queryFsUserBindInfoVo.setWxAppId(openCustomerVOResult.getWxAppId());
        ModelResult<List<FsUserBindInfo>> fsUserBindInfoResult = wechatUserBindInfoService.queryWechatUserAmountByFsUsers(queryFsUserBindInfoVo);
        if (!fsUserBindInfoResult.isSuccess()) {
            logger.error("wechatUserBindInfoService.queryWechatUserAmountByFsUsers: queryFsUserBindInfoVo[{}], result[{}]", queryFsUserBindInfoVo, fsUserBindInfoResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询客服绑定外部联系人关系失败"); // ignoreI18n
        }

        //有关联的外部联系人，不允许删除
        if (!CollectionUtils.isEmpty(fsUserBindInfoResult.getResult()) && fsUserBindInfoResult.getResult().get(0).getAmount() > 0) {  //没有联系人，result=[]
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "客服人员有关联的外部联系人，不允许删除"); // ignoreI18n
        }
    }

    @Override
    public void deleteOpenCustomer(FsUserVO fsUserVO, String customerId) {
        //删除开平客服信息
        BaseResult<Void> customerResult = openCustomerService.deleteOpenCustomer(fsUserVO, customerId);
        if (!customerResult.isSuccess()) {
            logger.error("openCustomerService.deleteOpenCustomer : fsUserVO[{}], customerId[{}], result[{}]",
                    fsUserVO, customerId, customerResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "删除客服信息失败"); // ignoreI18n
        }
    }

    @Override
    public Map<String, Object> viewQRCode(FsUserVO fsUserVO, String appId) {
        //1、查询外联服务号关联微信服务号的状态
        OuterServiceWechatVO outerServiceWechatVO = new OuterServiceWechatVO();
        outerServiceWechatVO.setFsEa(fsUserVO.getEnterpriseAccount());
        outerServiceWechatVO.setAppId(appId);
        outerServiceWechatVO.setStatus(CommonConstant.VALID);
        BaseResult<OuterServiceWechatVO> outerServiceWechatVOBaseResult = outerServiceWechatService.queryOuterServiceWechat(outerServiceWechatVO);
        if (!outerServiceWechatVOBaseResult.isSuccess()) {
            logger.error("outerServiceWechatService.queryOuterServiceWechat : outerServiceWechatVO[{}], result[{}]", outerServiceWechatVO, outerServiceWechatVOBaseResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询外联服务号关联微信服务号的信息失败"); // ignoreI18n
        }
        if (null == outerServiceWechatVOBaseResult.getResult()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "外联服务号没有关联的微信服务号"); // ignoreI18n
        }

        //2、查询 OpenCustomerVO（为了查到 wxAppId）
        BaseResult<List<OpenCustomerVO>> customerVOsResult = openCustomerService.queryByAppId(fsUserVO, appId, CommonConstant.VALID);
        if (!customerVOsResult.isSuccess()) {
            logger.error("openCustomerService.queryByAppId : appId[{}], status[{}], result[{}]", appId, CommonConstant.VALID, customerVOsResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询客服信息失败"); // ignoreI18n
        }
        List<OpenCustomerVO> existOpenCustomerVOs = customerVOsResult.getResult();

        if (CollectionUtils.isEmpty(existOpenCustomerVOs)) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "客服人员信息不存在"); // ignoreI18n
        }

        //3、获取公司信息
        String enterpriseName = "";
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        try {
            GetSimpleEnterpriseResult getSimpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(arg);
            if (GetSimpleEnterpriseResult.RESULT_CODE_NORMAL != getSimpleEnterpriseResult.getResultCode()) {
                logger.warn("enterpriseEditionService.getSimpleEnterprise failed, user[{}], arg[{}], result[{}]", fsUserVO, arg, getSimpleEnterpriseResult.getResultCode());
            }
            if (null != getSimpleEnterpriseResult.getSimpleEnterprise()) {
                enterpriseName = getSimpleEnterpriseResult.getSimpleEnterprise().getEnterpriseName();
            }
        } catch (Exception e) {
            logger.error("enterpriseEditionService.getSimpleEnterprise failed, arg[{}],", arg, e);
        }

        //4、获取用户信息
        BaseResult<List<Employee>> employeeResult = openAppAddressBookEmployeeService.getEmployeesNoAdminId(fsUserVO.getEnterpriseAccount(), Lists.newArrayList(fsUserVO.getUserId()));
        if (!employeeResult.isSuccess() || CollectionUtils.isEmpty(employeeResult.getResult())) {
            logger.error("employeeService.getEmployeesNoAdminId: fsEa[{}], employeeIds[{}], result[{}]",
                    fsUserVO.getEnterpriseAccount(), Lists.newArrayList(fsUserVO.getUserId()), employeeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询员工信息失败"); // ignoreI18n
        }
        Employee employee = employeeResult.getResult().get(0);

        //5、获取客服的公众号二维码
        QrCodeVo qrCodeVo = new QrCodeVo();
        qrCodeVo.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        qrCodeVo.setFsUserId(fsUserVO.getUserId().longValue());
        qrCodeVo.setWxAppId(existOpenCustomerVOs.get(0).getWxAppId());
        qrCodeVo.setExpireSeconds(ConfigCenter.OPEN_CUSTOMER_QRCODE_EXPIRE_SECONDS); 
        ModelResult<IndividualQrResult> individualQrResultModelResult = qrCodeService.getIndividualTempQr(qrCodeVo);
        if (!individualQrResultModelResult.isSuccess()) {
            logger.error("qrCodeService.getIndividualTempQr : qrCodeVo[{}], result[{}]", qrCodeVo, individualQrResultModelResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "获取客服的公众号二维码失败"); // ignoreI18n
        }

        //6、结果
        Map<String, Object> result = new HashMap<>();
        result.put("enterpriseName", enterpriseName);
        result.put("userName", employee.getName());
        result.put("url", individualQrResultModelResult.getResult().getShowUrl());
        return result;
    }

    @Override
    public List<Integer> queryCustomersUserIds(FsUserVO fsUserVO, String appId) {
        //查询 fsEa，在 appId上的有效的客服
        BaseResult<List<OpenCustomerVO>> customersResult = openCustomerService.queryByAppId(fsUserVO, appId, CommonConstant.VALID);
        if (!customersResult.isSuccess()) {
            logger.error("openCustomerService.queryByAppId : appId[{}], status[{}], result[{}]", appId, CommonConstant.VALID, customersResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询客服信息失败"); // ignoreI18n
        }

        return customersResult.getResult().stream().map(OpenCustomerVO::getUserId).collect(Collectors.toList());
    }
}
