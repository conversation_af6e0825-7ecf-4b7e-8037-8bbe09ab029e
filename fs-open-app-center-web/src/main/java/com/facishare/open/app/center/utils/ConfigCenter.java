package com.facishare.open.app.center.utils;

import com.alibaba.fastjson.JSON;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.utils.JsonKit;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 配置中心
 * <p>
 * Author: chen<PERSON><PERSON><PERSON>
 * Create Time: 16.1.12
 */
@Slf4j
public class ConfigCenter {
    private static Logger logger = LoggerFactory.getLogger(ConfigCenter.class);

    private static final String COMMON_CONFIG = "fs-open-app-center-common";
    private static IConfigFactory factory = ConfigFactory.getInstance();
    private static final String dateFormat = "yyyy-MM-dd";
    private static final String DEFAULT_APP_EXPIRED_ADMIN_MSG = "该应用的可用时间已过期，请联系纷享客服续费(联系电话4001122778)。"; // ignoreI18n
    private static final String ALL = "all";
    //灰度允许的appList
    private static final String allow_appList = "allow_appList";

    /**
     * 服务号群发图文消息统计数据测试开关
     */
    public static Boolean SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_SWITCH_ON = false;
    /**
     * 服务号群发图文消息统计数据测试服务号ID
     */
    public static String SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_APP_ID = "FSAID_bebf97f"; // 金字招牌

    /**用于控制服务号工作台数据统计日期选择: 0 不显示；1 前1天；2 前一周；3 前一月*/
    public static Integer SERVICE_DASHBOARD_STATISTICS_DATE_FLAG;
    public static String NOTIFY_ADMIN_TEMPLATE_FIRST = "你所在的企业有%s名员工期望添加以下新应用。"; // ignoreI18n
    public static Integer NOTIFY_ADMIN_LIMIT_COUNT = 5;
    public static String NOTIFY_ADMIN_TEMPLATE_ID = "201601121452596107129";
    public static String NOTIFY_ADMIN_TEMPLATE_URL = "http://www.fsfte2.com/open/h5/copyrightbk.html";
    public static String NOTIFY_ADMIN_TEMPLATE_APP_ID = "FSAID_5f5e207";
    public static String NOTIFY_ADMIN_TEMPLATE_REMARK = "你可以进入后台进行添加，点击查看添加引导"; // ignoreI18n
    public static Integer NOTIFY_ADMIN_TIMES = 3;
    public static Integer CUSTOM_APP_LIMIT = 10;
    public static Integer CUSTOM_APP_MAX = 999;
    public static String CUSTOM_APP_NOTIFY_UNPURCHASED = "抱歉，每个企业只能免费创建10个服务号！请联系纷享客服进行购买，服务热线 4001122778。"; // ignoreI18n
    public static String CUSTOM_APP_NOTIFY_EXPIRED = "您好！您公司的自定义助手已到期，已无法创建更多应用了，请联系纷享客服续费(联系电话 4001122778)。"; // ignoreI18n
    public static String APP_OFF_DIALOG_BOX = "该应用已被停用，请联系纷享客服开通(联系电话 4001122778)。给您带来不便，请谅解！"; // ignoreI18n
    public static String APP_EXPIRED_ADMIN_MSG = DEFAULT_APP_EXPIRED_ADMIN_MSG;

    public static String ENTERPRISE_WALLET_APP_ID;

    private static final String ADD_BTN_PROMPT_ADMIN_DEFAULT = "请联系纷享客服(客服热线[[4001122778]])开通此应用"; // ignoreI18n
    private static final String ADD_BTN_PROMPT_USER_DEFAULT = "请联系贵公司系统管理员%%admins%%开通此应用"; // ignoreI18n
    /**
     * 管理员点击"添加"按钮的弹窗提示(分版销售，没有试用等)
     */
    public static String ADD_BTN_PROMPT_ADMIN = ADD_BTN_PROMPT_ADMIN_DEFAULT;

    /**
     * 普通用户点击"添加"按钮的弹窗提示(分版销售，没有试用等)
     */
    public static String ADD_BTN_PROMPT_USER = ADD_BTN_PROMPT_USER_DEFAULT;

    /**
     * 纷享首页地址
     */
    public static String FXIAOKE_HOME_INDEX_URL="https://www.fxiaoke.com/XV/Home/Index";


    /**
     * 助手类应用
     */
    public static String HELPER_APP_IDS = "";
    /**
     * 应用中心公告 appCenterNotice
     */
    //公告是否展示
    public static Boolean NOTICE_ISSHOW = false;
    //公告开始时间
    public static Date NOTICE_STARTTIME;
    //公告结束时间
    public static Date NOTICE_ENDTIME;
    //公告范围，是否仅管理员
    public static Boolean NOTICE_ONLY_ADMIN = true;
    //公告内容分隔符
    public static String NOTICE_SEPARATOR = "@";
    //公告标题(多条公告之间用分隔符)
    public static String NOTICE_TITLE = "1、这里是公告one。@2、这里是公告two。"; // ignoreI18n
    //公告URL地址(多条公告之间用分隔符)
    public static String NOTICE_URL = "https://fxiaoke.com@http://oss.firstshare.cn/";
    //公告前缀
    public static String NOTICE_PREFIX = "app_center_notice_v1";

    /**
     * 服务号推广
     */
    //推广标题
    public static String SERVICE_PROMOTION_TITLE = "企业服务号"; // ignoreI18n
    //推广引导语
    public static String SERVICE_PROMOTION_LEADING_WORDS = "给企业创建第一个服务号"; // ignoreI18n
    //推广公众号名称
    public static String SERVICE_PROMOTION_SERVICE_NAME = "HR助手"; // ignoreI18n
    //图片url
    public static String SERVICE_PROMOTION_PIC_URL = "https://open.fxiaoke.com/fscdn/img?imgId=group1/M00/01/A2/rBEiBVe9F_iASWs4AAAhUTvJ-AA039.png&width=670&height=2&mode=2";
    //简介
    public static String SERVICE_PROMOTION_INTRODUCTION = "HR助手是致力于企业内部人事业务办理的服务公众号"; // ignoreI18n
    //了解更多
    public static String SERVICE_PROMOTION_LEARN_MORE = "http://open.fxiaoke.com/support.html?articleId=89";
    //去创建
    public static String SERVICE_PROMOTION_BUILD_URL = "#app/manage/servicemanage";
    //模板ID
    public static String SERVICE_PROMOTION_TEMPLATE_ID = "c029d3a06ca74491b59798c2f42112d8";

    //群发图片消息的图片最大值 20M
    public static Long GROUP_SEND_IMAGE_MESSAGE_MAX_IMAGE_SIZE = 20 * 1024 * 1024L;

    private static String DEFAULT_APP_EXPIRED_APP_ADMIN_MSG = "非常抱歉！该应用的可用时间已过期，请联系贵公司的系统管理员。"; // ignoreI18n
    private static String APP_EXPIRED_APP_ADMIN_MSG = DEFAULT_APP_EXPIRED_APP_ADMIN_MSG;
    public static String getAppExpiredAppAdminMsg() {
        return APP_EXPIRED_APP_ADMIN_MSG;
    }

    /**
     * 发送系统消息的AppID
     */
    public static String SYSTEM_MSG_APPID = "FSAID_5f5e207";

    /**
     * 玩转企业服务号(马甲企业)
     */
    public static String SYSTEM_MSG_FORSERVICE_APPID = "FSAID_bebd185";

    /**
     * 添加服务号管理员权限时系统消息
     */
    public static String NOTIFY_SERVICE_ADMIN_RIGHT_ADD = "恭喜您成为%s的管理员，请登录网页端，在[应用]-[服务号管理]中配置您的服务号。"; // ignoreI18n

    /**
     * 删除服务号管理员权限时系统消息
     */
    public static String NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE = "您好！您的%s管理权限被取消，请知悉！"; // ignoreI18n
    /**
     * 添加应用管理员权限时系统消息
     */
    public static String NOTIFY_APP_ADMIN_RIGHT_ADD = "恭喜您成为%s的管理员，请登录网页端，在[应用]-[应用管理]中配置您的应用。"; // ignoreI18n
    /**
     * 删除应用管理员权限时系统消息
     */
    public static String NOTIFY_APP_ADMIN_RIGHT_REMOVE = "您好！您的%s管理权限被取消，请知悉！"; // ignoreI18n
    //移动客服默认头像
    public static String CUSTOMER_DEFAULT_ICON;

    //工单默认头像
    public static String WORK_ORDER_DEFAULT_ICON;

    //问卷默认头像
    public static String QUESTIONNAIRE_DEFAULT_ICON;

    //使用新版适用范围的应用
    public static Set<String> useNewScopeAppList;

    /**
     * 服务号常见问题文章分类CategoryId
     * FTE2=13
     */
    @Deprecated
    public static int SERVICE_FAQ_CATEGORY_ID = 13;
    /**
     * 服务号常见问题文章列表
     */
    public static String SERVICE_FAQ = "[{\"title\":\"创建服务号后员工在哪里可以查看服务号？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fservice/src/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%90%8E%E5%91%98%E5%B7%A5%E5%9C%A8%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E6%9F%A5%E7%9C%8B%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%90%8E%E5%91%98%E5%B7%A5%E5%9C%A8%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E6%9F%A5%E7%9C%8B%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F.html\"},{\"title\":\"服务号创建后能否修改或删除？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fservice/src/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%88%9B%E5%BB%BA%E5%90%8E%E8%83%BD%E5%90%A6%E4%BF%AE%E6%94%B9%E6%88%96%E5%88%A0%E9%99%A4%EF%BC%9F/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%88%9B%E5%BB%BA%E5%90%8E%E8%83%BD%E5%90%A6%E4%BF%AE%E6%94%B9%E6%88%96%E5%88%A0%E9%99%A4%EF%BC%9F.html\"},{\"title\":\"群发消息的范围能否选取全公司？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fservice/src/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E7%BE%A4%E5%8F%91%E6%B6%88%E6%81%AF%E7%9A%84%E8%8C%83%E5%9B%B4%E8%83%BD%E5%90%A6%E9%80%89%E5%8F%96%E5%85%A8%E5%85%AC%E5%8F%B8%EF%BC%9F/%E7%BE%A4%E5%8F%91%E6%B6%88%E6%81%AF%E7%9A%84%E8%8C%83%E5%9B%B4%E8%83%BD%E5%90%A6%E9%80%89%E5%8F%96%E5%85%A8%E5%85%AC%E5%8F%B8%EF%BC%9F.html\"},{\"title\":\"群发消息时，为什么选择不到全公司的人员？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fservice/src/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E7%BE%A4%E5%8F%91%E6%B6%88%E6%81%AF%E6%97%B6%EF%BC%8C%E4%B8%BA%E4%BB%80%E4%B9%88%E9%80%89%E6%8B%A9%E4%B8%8D%E5%88%B0%E5%85%A8%E5%85%AC%E5%8F%B8%E7%9A%84%E4%BA%BA%E5%91%98%EF%BC%9F/%E7%BE%A4%E5%8F%91%E6%B6%88%E6%81%AF%E6%97%B6%EF%BC%8C%E4%B8%BA%E4%BB%80%E4%B9%88%E9%80%89%E6%8B%A9%E4%B8%8D%E5%88%B0%E5%85%A8%E5%85%AC%E5%8F%B8%E7%9A%84%E4%BA%BA%E5%91%98%EF%BC%9F.html\"},{\"title\":\"如何设置关键词自动回复\",\"url\":\"https://www.fxiaoke.com/mob/guide/fservice/src/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%A6%82%E4%BD%95%E8%AE%BE%E7%BD%AE%E5%85%B3%E9%94%AE%E8%AF%8D%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D%EF%BC%9F/%E5%A6%82%E4%BD%95%E8%AE%BE%E7%BD%AE%E5%85%B3%E9%94%AE%E8%AF%8D%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D%EF%BC%9F.html\"}]";

    /**
     * 服务号常见问题文章列表
     */
    public static String LINK_SERVICE_FAQ = "[{\"title\":\"如何给合作伙伴授权服务号使用权限？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fconnect/service/%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%A6%82%E4%BD%95%E7%BB%99%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E6%8E%88%E6%9D%83%E6%9C%8D%E5%8A%A1%E5%8F%B7%E4%BD%BF%E7%94%A8%E6%9D%83%E9%99%90%EF%BC%9F/%E5%A6%82%E4%BD%95%E7%BB%99%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E6%8E%88%E6%9D%83%E6%9C%8D%E5%8A%A1%E5%8F%B7%E4%BD%BF%E7%94%A8%E6%9D%83%E9%99%90%EF%BC%9F.html\"},{\"title\":\"企业内部人员是否可以使用互联服务号？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fconnect/service/%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E4%BC%81%E4%B8%9A%E5%86%85%E9%83%A8%E4%BA%BA%E5%91%98%E6%98%AF%E5%90%A6%E5%8F%AF%E4%BB%A5%E4%BD%BF%E7%94%A8%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F/%E4%BC%81%E4%B8%9A%E5%86%85%E9%83%A8%E4%BA%BA%E5%91%98%E6%98%AF%E5%90%A6%E5%8F%AF%E4%BB%A5%E4%BD%BF%E7%94%A8%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F.html\"},{\"title\":\"\u200B如何建设对合作伙伴的知识服务窗口？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fconnect/service/%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%A6%82%E4%BD%95%E5%BB%BA%E8%AE%BE%E5%AF%B9%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E7%9A%84%E7%9F%A5%E8%AF%86%E6%9C%8D%E5%8A%A1%E7%AA%97%E5%8F%A3%EF%BC%9F/%E5%A6%82%E4%BD%95%E5%BB%BA%E8%AE%BE%E5%AF%B9%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E7%9A%84%E7%9F%A5%E8%AF%86%E6%9C%8D%E5%8A%A1%E7%AA%97%E5%8F%A3%EF%BC%9F.html\"},{\"title\":\"如何提供对合作伙伴的客服服务？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fconnect/service/%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%A6%82%E4%BD%95%E6%8F%90%E4%BE%9B%E5%AF%B9%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E7%9A%84%E5%AE%A2%E6%9C%8D%E6%9C%8D%E5%8A%A1%EF%BC%9F/%E5%A6%82%E4%BD%95%E6%8F%90%E4%BE%9B%E5%AF%B9%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E7%9A%84%E5%AE%A2%E6%9C%8D%E6%9C%8D%E5%8A%A1%EF%BC%9F.html\"},{\"title\":\"授权的外部可见企业中，哪些人能使用互联服务号？\",\"url\":\"https://www.fxiaoke.com/mob/guide/fconnect/service/%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E6%8E%88%E6%9D%83%E7%9A%84%E5%A4%96%E9%83%A8%E5%8F%AF%E8%A7%81%E4%BC%81%E4%B8%9A%E4%B8%AD%EF%BC%8C%E5%93%AA%E4%BA%9B%E4%BA%BA%E8%83%BD%E4%BD%BF%E7%94%A8%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F/%E6%8E%88%E6%9D%83%E7%9A%84%E5%A4%96%E9%83%A8%E5%8F%AF%E8%A7%81%E4%BC%81%E4%B8%9A%E4%B8%AD%EF%BC%8C%E5%93%AA%E4%BA%9B%E4%BA%BA%E8%83%BD%E4%BD%BF%E7%94%A8%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F.html\"}]";


    /**
     * 互联服务号
     * 常见问题文章分类CategoryId
     */
    @Deprecated
    public static int LINK_SERVICE_FAQ_CATEGORY_ID = 53;

    /**
     * 运行创建的最大互联服务数
     */
    public static int LINK_SERVICE_MAX_NUMBER = 5;

    /**
     * 服务号常见问题文章条数
     */
    public static int SERVICE_FAQ_COUNT = 5;
    /**
     * 外联服务号常见问题文章分类CategoryId
     */
    public static int SERVICE_WECHAT_FAQ_CATEGORY_ID = 5;

    /**
     * 外联服务号常见问题文章条数
     */
    public static int SERVICE_WECHAT_FAQ_COUNT = 5;
    /**
     * 帮助中心文章跳转地址模板.包含（服务号常见问题）.
     */
    public static String SUPPORT_ARTICLE_TARGET_URL_FORMAT = "https://open.fsfte2.com/support.html?articleId=%s";
    /**
     * 系统公告文章跳转地址模板
     */
    public static String SYSTEM_NOTICE_ARTICLE_TARGET_URL_FORMAT = "https://open.fsfte2.com/support.html?articleId=%s";
    /**
     * 系统公告更多地址
     */
    public static String SYSTEM_NOTICE_MORE_TARGET_URL = "http://open.fxiaoke.com/support.html?categoryId=11&categoryName=%E6%9C%80%E6%96%B0%E5%85%AC%E5%91%8A&categoryPId=10";
    /**
     * 服务号常见问题更多地址
     */
    public static String SERVICE_FAQ_MORE_TARGET_URL = "https://www.fxiaoke.com/mob/guide/fservice/src/%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%90%8E%E5%91%98%E5%B7%A5%E5%9C%A8%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E6%9F%A5%E7%9C%8B%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%90%8E%E5%91%98%E5%B7%A5%E5%9C%A8%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E6%9F%A5%E7%9C%8B%E6%9C%8D%E5%8A%A1%E5%8F%B7%EF%BC%9F.html";
    /**
     * 互联服务号
     * 服务号常见问题更多地址
     */
    public static String LINK_SERVICE_FAQ_MORE_TARGET_URL = "https://www.fxiaoke.com/mob/guide/fconnect/service/%E4%BA%92%E8%81%94%E6%9C%8D%E5%8A%A1%E5%8F%B7%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%A6%82%E4%BD%95%E7%BB%99%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E6%8E%88%E6%9D%83%E6%9C%8D%E5%8A%A1%E5%8F%B7%E4%BD%BF%E7%94%A8%E6%9D%83%E9%99%90%EF%BC%9F/%E5%A6%82%E4%BD%95%E7%BB%99%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E6%8E%88%E6%9D%83%E6%9C%8D%E5%8A%A1%E5%8F%B7%E4%BD%BF%E7%94%A8%E6%9D%83%E9%99%90%EF%BC%9F.html";
    /**
     * 多客服开启
     */
    public static String SYSTEM_NOTICE_SERVICE_NUMBER_ON = "您好！管理员已启用%s多客服功能，请继续您的移动客服处理！"; // ignoreI18n
    /**
     * 多客服停用
     */
    public static String SYSTEM_NOTICE_SERVICE_NUMBER_OFF = "您好！管理员已停用%s多客服功能，请知悉！"; // ignoreI18n
    /**
     * 服务号创建
     */
    public static String SYSTEM_NOTICE_APP_SERVICE_CREATE = "恭喜您成功创建%s，您可群发文本、图文消息，收集员工反馈，提供自助服务等，请进入Web端【应用】-【服务号管理】中进行设置，开启您的服务号探索之旅吧！"; // ignoreI18n
    /**
     * 自定义应用创建
     */
    public static String SYSTEM_NOTICE_APP_CUSTOM_CREATE = "恭喜您成功创建%s，请进入网页端【应用】-【应用管理】中进行设置，开启您的自建应用探索之旅吧！"; // ignoreI18n
    private static String DEFAULT_CRM_VIEW_SETTING_DESC = "CRM启动了同步功能，请在CRM-[CRM管理]-[角色设置]分配可使用的人员。"; // ignoreI18n

    public static String CRM_VIEW_SETTING_DESC = DEFAULT_CRM_VIEW_SETTING_DESC;
    //服务号5.4，不需要重复恢复的旧服务号数据，根据企业id来控制
    public static String IS_NOT_SET_OLD_SERVICE_FSEA;

    //不需要身份验证的url
    public static String UN_CHECK_LOGIN_URL = "/open/appcenter/image/view;/open/appcenter/image/upload;/open/appcenter/wx/link/notice/queryPagerByUser;/support/;/fs-open-app-center-web/support/";

    /**
     * 过滤方法的前缀.
     */
    public static String[] FILTER_METHOD_PRE = {"query"};

    /**
     * 企业互联的应用id.
     */
    public static List<String> E_LINK_APP_IDS = new ArrayList<>();
    /**
     * 微信百川的应用id，比如通知.
     */
    public static List<String> WX_LINK_APP_IDS = new ArrayList<>();

    /**
     * 【特殊逻辑】查询 "应用管理"，不能管理的appId (比如企业互联，企业互联-微信的应用）
     */
    public static List<String> APP_IDS_UNABLE_TO_MANAGE = new ArrayList<>();

    /**
     *  微信百川通知最大附件数
     */
    public static Integer WX_LINK_NOTICE_ATTACHMENT_MAX_SIZE = 5;

    /**
     * 微信百川通知附件的文件最大值
     */
    public static Long WX_LINK_NOTICE_ATTACHMENT_MAX_FILE_SIZE = 50 * 1024 * 1024L; // 默认50M

    /**
     * 培训助手应用ID
     */
    public static String TRAINING_ASSISTANT_APP_ID = "FSAID_5f5e52c";

    /**
     * 服务号工作台指引Key-全量
     */
    public static List<String> SERVICE_DASHBOARD_GUIDE_KEYS_ALL = new ArrayList<>();
    /**
     * 服务号工作台指引Key-最后升级
     */
    public static List<String> SERVICE_DASHBOARD_GUIDE_KEYS_LATEST_UPGRADE = new ArrayList<>();
    /**
     * 服务号工作台最新升级事件标识
     */
    public static String SERVICE_DASHBOARD_LATEST_UPGRADE_EVENT_FLAG;

    //服务号申请创建标题
    public static String SERVICE_APPROVAL_CREATE_TITLE = "服务号申请成功"; // ignoreI18n
    //服务号申请审批标题
    public static String SERVICE_APPROVAL_TITLE = "服务号申请审批"; // ignoreI18n
    //服务号申请创建标题
    public static String SERVICE_APPROVAL_COPY_TITLE = "服务号申请知会"; // ignoreI18n

    //新的图片url（用到北京的图片系统）
    public static String BJ_IMAGE_VIEW_URL  = "https://open.fxiaoke.com/fscdn/bj/imgTxtView?imgId=";

    /**
     * 服务号申请创建通知模板
     */
    public static String SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE = "{'title':{'content':'%s','time':'%s'}," +
            "'infos':[{'label':'服务号名称','value':'%s'},{'label':'功能介绍','value':'%s'},{'label':'管理员','value':'%s'}," + // ignoreI18n
            "{'label':'订阅范围','value':'%s'},{'label':'申请人','value':'%s'}],'button':{'title':'查看详情','url':'%s'}}"; // ignoreI18n

    /**
     * 服务号申请审批通过通知模板
     */
    public static String SERVICE_APPROVAL_NOTICE_TEMPLATE = "{'title':{'content':'服务号申请通过','time':'%s'}," + // ignoreI18n
            "'first':{'content':'恭喜，您的服务号创建申请已通过，点击进入服务号！'}," + // ignoreI18n
            "'infos':[{'label':'服务号名称','value':'%s'},{'label':'功能介绍','value':'%s'},{'label':'管理员','value':'%s'}," + // ignoreI18n
            "{'label':'订阅范围','value':'%s'}],'button':{'title':'查看详情','url':'%s'}}"; // ignoreI18n

    /**
     * 服务号申请拒绝通知模板
     */
    public static String SERVICE_REJECT_NOTICE_TEMPLATE = "{'title':{'content':'服务号申请未通过','time':'%s'}," + // ignoreI18n
            "'first':{'content':'您的服务号创建申请未通过，请调整您的申请信息！'}," + // ignoreI18n
            "'infos':[{'label':'服务号名称','value':'%s'},{'label':'功能介绍','value':'%s'},{'label':'管理员','value':'%s'}," + // ignoreI18n
            "{'label':'订阅范围','value':'%s'},{'label':'未通过原因','value':'%s'}],'button':{'title':'查看详情','url':'%s'}}"; // ignoreI18n
    //服务号申请H5页面
    public static String SERVICE_APPROVAL_URL = "https://www.fxiaoke.com/open/mprequest/?approvalId=%s";

    //默认文章分类开关ON的服务号模版
    public static String ARTICLE_CATEGORY_ON_FOR_TEMPLATE = "8f2b55c03e6d4fed842c811ee160b07b;" +
            "930489f6ed064403b566625a5cfebdc3;e1afef533c7d4bcaac948da778295bd9";

    //文章分类的H5地址
    public static String ARTICLE_CATEGORY_H5 = "https://www.fxiaoke.com/open/aggregatearticle/index.html?fsEa=%s&appId=%s";

    /**
     * 服务号首页相关文档json，注：下载文件名需encode，否则IE下载文件名会乱码
     */
    //2017-06-26 5.7版本服务号优化  服务号产品功能手册下载链接替换为跳转链接，链接地址为https://www.fxiaoke.com/mob/guide/fservice/，按钮文字改为“服务号产品手册”；
    public static String SERVICE_RELATED_DOCUMENTS = "[{'title':'企业服务号产品介绍V1.0','downloadUrl':'http://www.fsfte2.com/FSC/EM/File/DownloadByPath?path=A_201610_24_e14636c91cd34707a7f6f3b4cf3804be.pdf&name=%E6%9C%8D%E5%8A%A1%E5%8F%B7%E4%BA%A7%E5%93%81%E4%BB%8B%E7%BB%8DV1.0.pdf'}]";

    /**
     * 外联服务号。微信公众号授权，已经授权过的提示语
     */
    public static String IS_BIND_OUTER_SERVICE_NOTICE = "如果您是该公众号的管理员，建议：\n" +
            "联系%s公司的%s服务号管理员，添加自己为管理员或取消授权，创建新的外联服务号"; // ignoreI18n

    /**
     * 微信未认证公众号的提示语
     */
    public static String WECHAT_IS_UNAUTHERIZED_NOTICE = "由于服务号未认证，外联服务号获取不到微信用户信息，无法进行消息回复和业务处理，请尽快 去官方认证\n";

    /**
     * 微信未认证公众号的弹窗提示
     */
    public static String WECHAT_IS_UNAUTHERIZED_TOOLTIP = "由于微信公众号未认证，该功能不可用！"; // ignoreI18n

    /**
     * 微信已认证订阅号提示语
     */
    public static String WECHAT_SUBSCRIBE_NOTICE = "对于订阅号，外联服务号获取不到微信用户信息，无法进行消息回复和业务处理，请使用认证的服务号授权"; // ignoreI18n

    /**
     * 微信已认证订阅号的弹窗提示
     */
    public static String WECHAT_SUBSCRIBE_TOOLTIP = "暂不支持微信订阅号，请使用认证的服务号重新创建外联服务号！"; // ignoreI18n

    /**
     * 通过微信公众号取消授权的提示语
     */
    public static String UNBIND_FROM_WECHAT_NOTICE = "抱歉，由于微信公众号取消了授权，自定义菜单/微信客服/外部联系人等功能无法操作，请知悉！"; // ignoreI18n

    /**
     * 通过微信公众号取消授权的弹窗提示
     */
    public static String UNBIND_FROM_WECHAT_TOOLTIP = "由于微信公众号管理员取消了授权，该功能不可用！"; // ignoreI18n

    /**
     * 通过外联服务号取消授权的提示语
     */
    public static String UNBIND_FROM_OUTER_SERVICE_NOTICE = "抱歉，由于管理员取消了微信公众号的授权，群发消息/自定义菜单/微信客服/外部联系人/等功能无法操作，请知悉！"; // ignoreI18n

    /**
     * 通过外联服务号取消授权的弹窗提示
     */
    public static String UNBIND_FROM_OUTER_SERVICE_TOOLTIP = "由于管理员取消了微信公众号的授权，该功能不可用！"; // ignoreI18n

    /**
     * 升级为专业客服系统导致取消授权的提示语
     */
    public static String UPGRADE_TO_CUSTOMER_SERVICE_NOTICE = "由于微信消息已经转接到CRM客服系统，本工作台不再接收新的微信消息，请知悉！"; // ignoreI18n

    /**
     * 通过微信公众号取消某个功能权限的弹窗提示
     */
    public static String WECHAT_UNBIND_PERMISSON_TOOLTIP = "由于微信公众号管理员取消该功能的权限，该功能不可用！"; // ignoreI18n

    //微信公众号默认头像
    public static String WECHAT_DEFAULT_ICON;

    //微信认证地址
    public static String WECHAT_CERTIFICATION_ADDRESS = "https://mp.weixin.qq.com";

    //外联服务号创建成功的权限1
    public static String OUTER_SERVICE_CREATE_SUCCESS_AUTH1 = "回复微信用户的消息"; // ignoreI18n
    //外联服务号创建成功的权限2
    public static String OUTER_SERVICE_CREATE_SUCCESS_AUTH2 = "指定客服人员，一对一服务微信用户"; // ignoreI18n
    //外联服务号创建成功的权限3
    public static String OUTER_SERVICE_CREATE_SUCCESS_AUTH3 = "微信用户绑定CRM联系人，在微信公众号中可直接下单"; // ignoreI18n
    //外联服务号创建成功的权限4
    public static String OUTER_SERVICE_CREATE_SUCCESS_AUTH4 = "配置自定义菜单，在微信公众号中可直接工单"; // ignoreI18n

    //添加为微信客服二级session icon url
    public static String ADD_OPEN_CUSTOMER_ICON = "https://open.fxiaoke.com/fscdn/img?imgId=group1/M00/03/D1/rBEiBVhEy_WAE-CyAAAHCzsaqXs514.png";
    //添加微信客服的通知
    public static String ADD_OPEN_CUSTOMER_NOTICE_URL = "https://www.fxiaoke.com/open/mpouterservice/index.html?fs_nav_title=&fs_nav_fsmenu=false#!/?appId=%s";
    // 微信客服二维码失效时间(秒）
    public static int OPEN_CUSTOMER_QRCODE_EXPIRE_SECONDS =604800;  //7天   一天 86400秒*7=604800秒

    //绑定应用url后缀
    public static String BIND_APP_URL_SUFFIX = "/fsh5/ithelpdesk/5.4/index.html?fs_nav_fsmenu=false#/empty?appId=";
    //手机微客服工作台的外部联系人icon。
    public static String OUTER_CONTACTS_ICON = "https://open.fxiaoke.com/fscdn/img?imgId=group1/M00/03/D9/rBEiBlhFKvSAKOirAAAFACqI1oI417.png";
    public static String OUTER_CONTACTS_LAST_SUMMARY = "%1$s成为您的VIP客户"; // ignoreI18n

    //图片服务地址
    public static String FS_OPEN_ASSETS_IMAGE_URL = "https://open.fxiaoke.com/fscdn/img?imgId=";
    //服务号停用的提示文案
    public static String SERIVCE_OFF_NOTIC_PER = "(已停用)"; // ignoreI18n

    public static String WX_LINK_NOTICE_APP_ID;
    public static String WX_LINK_NOTICE_WEB_COMPONENT_ID;
    //服务工单访问地址
    public static String WORK_ORDER_PAAS_URL = "http://www.ceshi113.com/open/order/build/index.html#/metadatas/%s?api_name=%s&bizId=%s&bizType=%s&isfetchdata=1&fs_nav_fsmenu=false&embed=0&server_name=WorkOrderPaas";
    //审批单访问地址
    public static String APPROVAL_URL = "http://www.ceshi113.com/open/formpro/#form?bizType=2&bizId=%s&formDescribeId=%s";

    public static Integer SERVICE_ARTICLE_CATEGORY_MAX_SIZE = 20;

    /**
     * 强制下线的应用ID列表用于应用管理
     */
    public static List<String> FORCE_OFFLINE_APP_IDS_FOR_APP_MANAGE = new ArrayList<>();

    /**
     * 钉钉版应用黑名单
     */
    public static List<String> BACK_APP_IDS_OF_DING_TALK = new ArrayList<>();
    /**
     * 钉钉版License版本，默认：dingtalk_standard_edition,dingtalk_standardpro_edition
     */
    public static List<String> LICENSE_VERSION_OF_DING_TALK_LIST = new ArrayList<>();


    public static Map<String,Object> FS_EA_LINK_COMPONENT = new HashMap<>();
    public static Map<String,Object> WX_LINK_COMPONENT = new HashMap<>();

    /**
     * 支付钱包分组
     */
    public static Map<String,Object> PAY_WALLET_COMPONENT = new HashMap<>();
    public static String PAY_WALLET_APP_ID = "";

    /**
     * 永辉超市企业号
     */
    public static String YH_EA="449541";

    /**
     * 培训助手组件id
     */
    public static List<String> TRAIN_APP_COMPONENT_ID = new ArrayList<>();

    /**
     * 培训助手应用id
     */
    public static String TRAIN_APP_ID="";

    /**
     * 永辉企业下培训助手应用的名称
     */
    public static String YH_TRAIN_NAME="永辉微学院"; // ignoreI18n

    /**
     * 永辉企业下培训助手应用的LOGO
     */
    public static String TRAIN_APP_LOGO = "https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201705_04_a46ed5a757e44129976c70b4a5213a82.jpg&size=150_150&ea=appCenter";

    /**
     * 服务号名称校验正则表达式
     */
    public static String SERVICE_NAME_CHECK_REGEX = "^.{2,16}$";

    /**
     * 应用名称校验正则表达式
     */
    public static String APP_NAME_CHECK_REGEX = "^.{2,10}$";

    /**
     * 通过模板创建服务号不设置自动回复及关健字回复
     */
    public static boolean CREATE_SERVICE_BY_TEMPLATE_NOT_INIT_AUTO_KEY_REPLY = true;

    /**
     * 后门操作用户帐户
     */
    public static List<String> MIGRATE_OP_USER_ACCOUNTS = new ArrayList<>(0);

    public static String SERVICE_CUSTOMER_DEFAULT_NAME = "人工客服"; // ignoreI18n

    /**
     * 自建应用权限组
     */
    public static String CUSTOM_APP_SCOPES = "app_get_address_book,app_jsapi_util,app_jsapi_pay,app_approval_mgr_group,app_jsapi_contact,app_pay_operation_group,app_get_special_address_book,app_material_mgr_group,app_jsapi_share,app_message_group,app_jsapi_device,app_jsapi_base_group,app_mgr_address_book";

    /**
     * 自建服务号权限组
     */
    public static String CUSTOM_SERVICE_SCOPES = "jsapi_share,mgr_address_book,jsapi_pay,send_message,jsapi_device,approval_mgr_group,material_mgr_group,jsapi_util,get_special_address_book,jsapi_contact,get_address_book,pay_operation_group,jsapi_base_group";

    /**
     * 服务通工单 灰度配置
     */
    public static String ESERVICE_WORK_ORDER_GRAY_EAS = "74164,fktest289";

    /**
     * 智能机器人 灰度配置
     */
    public static String ONLINE_SERVICE_ASSISTANT_GRAY_EAS = "82846,82313";

    public static String systemIconRule = "^https:\\/\\/a[0-9]\\.fspage\\.com\\/FSR\\/fs-qixin\\/static.*";

    public static String CRM_APP_ID;

    /**
     * 不允许停用的应用ID列表
     */
    public static String NOT_ALLOW_BIND_OFF_APP_IDS = "";
    /**
     * 独立部署才会判断这个前缀，其他云校验都是不通过的
     */
    public static String SYS_ICON_START = "不存在前缀"; // ignoreI18n

    /**
     * 不允许更新应用管理员的应用ID列表，多个以逗号隔开
     */
    public static String NOT_ALLOW_UPDATE_APP_ADMIN_APP_IDS = "";
    /**
     * 不允许更新应用组件可见范围的应用ID列表，多个以逗号隔开
     */
    public static String NOT_ALLOW_UPDATE_COMPONENT_VIEW_APP_IDS = "";

    public static Map<String, List<String>> FORCE_OFFLINE_APP_ID_TO_EA_BLACK_LIST = Maps.newHashMap();

    static {
        factory.getConfig("fs-open-app-center-biz", config -> {
            FORCE_OFFLINE_APP_ID_TO_EA_BLACK_LIST = getForceOfflineAppIdToEaBlackList(config.get("FORCE_OFFLINE_APP_ID_TO_EA_BLACK_LIST"));
            GROUP_SEND_IMAGE_MESSAGE_MAX_IMAGE_SIZE = config.getLong("GROUP_SEND_IMAGE_MESSAGE_MAX_IMAGE_SIZE", GROUP_SEND_IMAGE_MESSAGE_MAX_IMAGE_SIZE);
            SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_SWITCH_ON = config.getBool("SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_SWITCH_ON", SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_SWITCH_ON);
            SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_APP_ID = config.get("SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_APP_ID", SAS_SERVICE_GROUP_SEND_STATISTICS_TEST_APP_ID);
            SERVICE_DASHBOARD_STATISTICS_DATE_FLAG = config.getInt("SERVICE_DASHBOARD_STATISTICS_DATE_FLAG", 1);
            SERVICE_CUSTOMER_DEFAULT_NAME = config.get("SERVICE_CUSTOMER_DEFAULT_NAME", SERVICE_CUSTOMER_DEFAULT_NAME);
            MIGRATE_OP_USER_ACCOUNTS = stringToList(config.get("MIGRATE_OP_USER_ACCOUNTS"));
            CREATE_SERVICE_BY_TEMPLATE_NOT_INIT_AUTO_KEY_REPLY = config.getBool("CREATE_SERVICE_BY_TEMPLATE_NOT_INIT_AUTO_KEY_REPLY", CREATE_SERVICE_BY_TEMPLATE_NOT_INIT_AUTO_KEY_REPLY);
            SERVICE_NAME_CHECK_REGEX = config.get("SERVICE_NAME_CHECK_REGEX", SERVICE_NAME_CHECK_REGEX);
            APP_NAME_CHECK_REGEX = config.get("APP_NAME_CHECK_REGEX", APP_NAME_CHECK_REGEX);
            NOTIFY_ADMIN_TEMPLATE_FIRST = config.get("web.notify.admin.template.first", "你所在的企业有%s名员工期望添加以下新应用。"); // ignoreI18n
            NOTIFY_ADMIN_LIMIT_COUNT = config.getInt("web.notify.admin.limit.count", 5);
            NOTIFY_ADMIN_TEMPLATE_ID = config.get("web.notify.admin.template.id", "201601121452596107129");
            NOTIFY_ADMIN_TEMPLATE_URL = config.get("web.notify.admin.template.url", "http://www.fsfte2.com/open/h5/copyrightbk.html");
            NOTIFY_ADMIN_TEMPLATE_APP_ID = config.get("web.notify.admin.template.app.id", "FSAID_5f5e207");
            NOTIFY_ADMIN_TEMPLATE_REMARK = config.get("web.notify.admin.template.remark", "你可以进入后台进行添加，点击查看添加引导"); // ignoreI18n
            NOTIFY_ADMIN_TIMES = config.getInt("web.notify.admin.times", 3);
            CUSTOM_APP_LIMIT = config.getInt("CUSTOM_APP_LIMIT", 10);
            CUSTOM_APP_MAX = config.getInt("CUSTOM_APP_MAX", 999);
            CUSTOM_APP_NOTIFY_UNPURCHASED = config.get("CUSTOM_APP_NOTIFY_UNPURCHASED", "抱歉，每个企业只能免费创建10个服务号！请联系纷享客服进行购买，服务热线 4001122778。"); // ignoreI18n
            CUSTOM_APP_NOTIFY_EXPIRED = config.get("CUSTOM_APP_NOTIFY_EXPIRED", "您好！您公司的自定义助手已到期，已无法创建更多应用了，请联系纷享客服续费(联系电话 4001122778)。"); // ignoreI18n
            APP_OFF_DIALOG_BOX = config.get("APP_OFF_DIALOG_BOX", "该应用已被停用，请联系纷享客服开通(联系电话 4001122778)。给您带来不便，请谅解！"); // ignoreI18n
            APP_EXPIRED_ADMIN_MSG = config.get("APP_EXPIRED_ADMIN_MSG", DEFAULT_APP_EXPIRED_ADMIN_MSG);
            APP_EXPIRED_APP_ADMIN_MSG = config.get("APP_EXPIRED_APP_ADMIN_MSG", DEFAULT_APP_EXPIRED_APP_ADMIN_MSG);
            SYSTEM_MSG_APPID = config.get("SYSTEM_MSG_APPID", "FSAID_5f5e207");
            SYSTEM_MSG_FORSERVICE_APPID = config.get("SYSTEM_MSG_FORSERVICE_APPID", "FSAID_5f5e207");
            NOTIFY_APP_ADMIN_RIGHT_ADD = config.get("web.notify.admin.right.template.add", "恭喜您成为%s的管理员，请登录网页端，在[应用]-[应用管理]中配置您的应用。"); // ignoreI18n
            NOTIFY_APP_ADMIN_RIGHT_REMOVE = config.get("web.notify.admin.right.template.remove", "您好！您的%s管理权限被取消，请知悉！"); // ignoreI18n
            CUSTOMER_DEFAULT_ICON = config.get("CUSTOMER_DEFAULT_ICON");
            WORK_ORDER_DEFAULT_ICON = config.get("WORK_ORDER_DEFAULT_ICON");
            QUESTIONNAIRE_DEFAULT_ICON = config.get("QUESTIONNAIRE_DEFAULT_ICON");
            NOTIFY_SERVICE_ADMIN_RIGHT_ADD = config.get("NOTIFY_SERVICE_ADMIN_RIGHT_ADD", "恭喜您成为%s的管理员，请登录网页端，在[应用]-[服务号管理]中配置您的服务号。"); // ignoreI18n
            NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE = config.get("NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE", "您好！您的%s管理权限被取消，请知悉！"); // ignoreI18n
            SYSTEM_NOTICE_SERVICE_NUMBER_ON = config.get("SYSTEM_NOTICE_SERVICE_NUMBER_ON", "您好！管理员已启用%s多客服功能，请继续您的移动客服处理！"); // ignoreI18n
            SYSTEM_NOTICE_SERVICE_NUMBER_OFF = config.get("SYSTEM_NOTICE_SERVICE_NUMBER_OFF", "您好！管理员已停用%s多客服功能，请知悉！"); // ignoreI18n
            SYSTEM_NOTICE_APP_SERVICE_CREATE = config.get("SYSTEM_NOTICE_APP_SERVICE_CREATE", "恭喜您成功创建%s，您可群发文本、图文消息，收集员工反馈，提供自助服务等，请进入Web端【应用】-【服务号管理】中进行设置，开启您的服务号探索之旅吧！"); // ignoreI18n
            SYSTEM_NOTICE_APP_CUSTOM_CREATE = config.get("SYSTEM_NOTICE_APP_CUSTOM_CREATE", "恭喜您成功创建%s，请进入网页端【应用】-【应用管理】中进行设置，开启您的自建应用探索之旅吧！"); // ignoreI18n

            LINK_SERVICE_MAX_NUMBER = config.getInt("LINK_SERVICE_MAX_NUMBER", LINK_SERVICE_MAX_NUMBER);

            SUPPORT_ARTICLE_TARGET_URL_FORMAT = config.get("SUPPORT_ARTICLE_TARGET_URL_FORMAT", SUPPORT_ARTICLE_TARGET_URL_FORMAT);
            SYSTEM_NOTICE_ARTICLE_TARGET_URL_FORMAT = config.get("SYSTEM_NOTICE_ARTICLE_TARGET_URL_FORMAT", SYSTEM_NOTICE_ARTICLE_TARGET_URL_FORMAT);
            SYSTEM_NOTICE_MORE_TARGET_URL = config.get("SYSTEM_NOTICE_MORE_TARGET_URL", SYSTEM_NOTICE_MORE_TARGET_URL);
            SERVICE_FAQ_MORE_TARGET_URL = config.get("SERVICE_FAQ_MORE_TARGET_URL", SERVICE_FAQ_MORE_TARGET_URL);
            LINK_SERVICE_FAQ_MORE_TARGET_URL = config.get("LINK_SERVICE_FAQ_MORE_TARGET_URL", LINK_SERVICE_FAQ_MORE_TARGET_URL);
            SERVICE_FAQ_CATEGORY_ID = config.getInt("SERVICE_FAQ_CATEGORY_ID", SERVICE_FAQ_CATEGORY_ID);
            SERVICE_FAQ = config.get("SERVICE_FAQ", SERVICE_FAQ);
            LINK_SERVICE_FAQ = config.get("LINK_SERVICE_FAQ", LINK_SERVICE_FAQ);
            LINK_SERVICE_FAQ_CATEGORY_ID = config.getInt("LINK_SERVICE_FAQ_CATEGORY_ID", LINK_SERVICE_FAQ_CATEGORY_ID);
            SERVICE_FAQ_COUNT = config.getInt("SERVICE_FAQ_COUNT", SERVICE_FAQ_COUNT);
            SERVICE_WECHAT_FAQ_CATEGORY_ID = config.getInt("SERVICE_WECHAT_FAQ_CATEGORY_ID", SERVICE_WECHAT_FAQ_CATEGORY_ID);
            SERVICE_WECHAT_FAQ_COUNT = config.getInt("SERVICE_WECHAT_FAQ_COUNT", SERVICE_WECHAT_FAQ_COUNT);

            SERVICE_PROMOTION_TITLE = config.get("SERVICE_PROMOTION_TITLE", SERVICE_PROMOTION_TITLE);
            SERVICE_PROMOTION_LEADING_WORDS = config.get("SERVICE_PROMOTION_LEADING_WORDS", SERVICE_PROMOTION_LEADING_WORDS);
            SERVICE_PROMOTION_SERVICE_NAME = config.get("SERVICE_PROMOTION_SERVICE_NAME", SERVICE_PROMOTION_SERVICE_NAME);
            SERVICE_PROMOTION_PIC_URL = config.get("SERVICE_PROMOTION_PIC_URL", SERVICE_PROMOTION_PIC_URL);
            SERVICE_PROMOTION_LEARN_MORE = config.get("SERVICE_PROMOTION_LEARN_MORE", SERVICE_PROMOTION_LEARN_MORE);
            SERVICE_PROMOTION_BUILD_URL = config.get("SERVICE_PROMOTION_BUILD_URL", SERVICE_PROMOTION_BUILD_URL);
            SERVICE_PROMOTION_INTRODUCTION = config.get("SERVICE_PROMOTION_INTRODUCTION", SERVICE_PROMOTION_INTRODUCTION);
            SERVICE_PROMOTION_TEMPLATE_ID = config.get("SERVICE_PROMOTION_TEMPLATE_ID", SERVICE_PROMOTION_TEMPLATE_ID);
            UN_CHECK_LOGIN_URL = config.get("UN_CHECK_LOGIN_URL", UN_CHECK_LOGIN_URL);

            //应用中心公告
            NOTICE_PREFIX = config.get("NOTICE_PREFIX", "app_center_notice_v1");
            NOTICE_ISSHOW = config.getBool("NOTICE_ISSHOW", false);
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
                NOTICE_STARTTIME = sdf.parse(config.get("NOTICE_STARTTIME", "2015-07-04"));
                NOTICE_ENDTIME = sdf.parse(config.get("NOTICE_ENDTIME", "2015-07-31"));
            } catch (ParseException e) {
                logger.error("parse NOTICE_ENDTIME failed. NOTICE_ENDTIME[{}], e[{}]", ConfigCenter.NOTICE_ENDTIME, e);
            }
            NOTICE_ONLY_ADMIN = config.getBool("NOTICE_ONLY_ADMIN", true);
            NOTICE_SEPARATOR = config.get("NOTICE_SEPARATOR", "");
            NOTICE_TITLE = config.get("NOTICE_TITLE", "");
            NOTICE_URL = config.get("NOTICE_URL", "");

            CRM_VIEW_SETTING_DESC = config.get("CRM_VIEW_SETTING_DESC", DEFAULT_CRM_VIEW_SETTING_DESC);

            IS_NOT_SET_OLD_SERVICE_FSEA = config.get("IS_NOT_SET_OLD_SERVICE_FSEA");

            FILTER_METHOD_PRE = config.get("FILTER_METHOD_PRE", "query").split(",");
            logger.info("set FILTER_METHOD_PRE = {}", Arrays.toString(FILTER_METHOD_PRE));

            SERVICE_DASHBOARD_GUIDE_KEYS_ALL =
                    Arrays.asList(config.get("SERVICE_DASHBOARD_GUIDE_KEYS_ALL", "").split(","));
            SERVICE_DASHBOARD_GUIDE_KEYS_LATEST_UPGRADE =
                    Arrays.asList(config.get("SERVICE_DASHBOARD_GUIDE_KEYS_LATEST_UPGRADE", "").split(","));
            SERVICE_DASHBOARD_LATEST_UPGRADE_EVENT_FLAG = config.get("SERVICE_DASHBOARD_LATEST_UPGRADE_EVENT_FLAG");
            SERVICE_RELATED_DOCUMENTS = config.get("SERVICE_RELATED_DOCUMENTS", SERVICE_RELATED_DOCUMENTS);

            //服务号申请
            SERVICE_APPROVAL_CREATE_TITLE = config.get("SERVICE_APPROVAL_CREATE_TITLE",
                    SERVICE_APPROVAL_CREATE_TITLE);
            SERVICE_APPROVAL_TITLE = config.get("SERVICE_APPROVAL_TITLE",
                    SERVICE_APPROVAL_TITLE);
            SERVICE_APPROVAL_COPY_TITLE = config.get("SERVICE_APPROVAL_COPY_TITLE",
                    SERVICE_APPROVAL_COPY_TITLE);

            SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE = config.get("SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE",
                    SERVICE_APPROVAL_CREATE_NOTICE_TEMPLATE);
            SERVICE_APPROVAL_NOTICE_TEMPLATE = config.get("SERVICE_APPROVAL_NOTICE_TEMPLATE",
                    SERVICE_APPROVAL_NOTICE_TEMPLATE);
            SERVICE_REJECT_NOTICE_TEMPLATE = config.get("SERVICE_REJECT_NOTICE_TEMPLATE",
                    SERVICE_REJECT_NOTICE_TEMPLATE);

            SERVICE_APPROVAL_URL = config.get("SERVICE_APPROVAL_URL",
                    SERVICE_APPROVAL_URL);

            ARTICLE_CATEGORY_H5 = config.get("ARTICLE_CATEGORY_H5",
                    ARTICLE_CATEGORY_H5);

            ADD_OPEN_CUSTOMER_ICON = config.get("ADD_OPEN_CUSTOMER_ICON", ADD_OPEN_CUSTOMER_ICON);
            ADD_OPEN_CUSTOMER_NOTICE_URL = config.get("ADD_OPEN_CUSTOMER_NOTICE_URL", ADD_OPEN_CUSTOMER_NOTICE_URL);
            OPEN_CUSTOMER_QRCODE_EXPIRE_SECONDS = config.getInt("OPEN_CUSTOMER_QRCODE_EXPIRE_SECONDS", OPEN_CUSTOMER_QRCODE_EXPIRE_SECONDS);

            OUTER_CONTACTS_ICON = config.get("OUTER_CONTACTS_ICON", OUTER_CONTACTS_ICON);
            OUTER_CONTACTS_LAST_SUMMARY = config.get("OUTER_CONTACTS_LAST_SUMMARY", OUTER_CONTACTS_LAST_SUMMARY);

            ADD_BTN_PROMPT_ADMIN = config.get("ADD_BTN_PROMPT_ADMIN", ADD_BTN_PROMPT_ADMIN_DEFAULT);
            ADD_BTN_PROMPT_USER = config.get("ADD_BTN_PROMPT_USER", ADD_BTN_PROMPT_USER_DEFAULT);
            FORCE_OFFLINE_APP_IDS_FOR_APP_MANAGE = stringToList(config.get("FORCE_OFFLINE_APP_IDS_FOR_APP_MANAGE"));
            BACK_APP_IDS_OF_DING_TALK = stringToList(config.get("BACK_APP_IDS_OF_DING_TALK"));
            LICENSE_VERSION_OF_DING_TALK_LIST = stringToList(config.get("LICENSE_VERSION_OF_DING_TALK_LIST", "dingtalk_standard_edition,dingtalk_standardpro_edition"));
            FS_EA_LINK_COMPONENT.put("componentId", config.get("FS_EA_LINK_COMPONENT_COMPONENT_ID","FSAID_E_LINK"));
            FS_EA_LINK_COMPONENT.put("componentName", config.get("FS_EA_LINK_COMPONENT_COMPONENT_NAME","企业互联")); // ignoreI18n
            FS_EA_LINK_COMPONENT.put("loginAddress", config.get("FS_EA_LINK_COMPONENT_LOGIN_ADDRESS","--"));
            FS_EA_LINK_COMPONENT.put("imageUrl", config.get("FS_EA_LINK_COMPONENT_IMAGE_URL","http://open.fsfte2.com/fscdn/img?imgId=group1/M00/00/DE/rB9ndlaUw--AH989AAAKe4iOlI4960.png"));
            FS_EA_LINK_COMPONENT.put("appType", config.getInt("FS_EA_LINK_COMPONENT_APP_TYPE", AppCenterEnum.AppType.BASE_APP.value()));
            FS_EA_LINK_COMPONENT.put("isFsApp", config.getInt("FS_EA_LINK_COMPONENT_IS_FS_APP",CommonConstant.YES));
            FS_EA_LINK_COMPONENT.put("appId", config.get("FS_EA_LINK_COMPONENT_COMPONENT_ID","FSAID_E_LINK"));
            FS_EA_LINK_COMPONENT.put("isNew", config.getInt("FS_EA_LINK_COMPONENT_IS_NEW",CommonConstant.NO));
            FS_EA_LINK_COMPONENT.put("isELink", config.getInt("FS_EA_LINK_COMPONENT_IS_E_LINK",CommonConstant.NO));
            FS_EA_LINK_COMPONENT.put("position", config.getInt("FS_EA_LINK_COMPONENT_POSITION",2));
            FS_EA_LINK_COMPONENT.put("parentId", config.get("FS_EA_LINK_COMPONENT_PARENT_ID",""));

            WX_LINK_COMPONENT.put("componentId", config.get("WX_LINK_COMPONENT_COMPONENT_ID","FSAID_WX_LINK"));
            WX_LINK_COMPONENT.put("componentName", config.get("WX_LINK_COMPONENT_COMPONENT_NAME","企业互联-微信")); // ignoreI18n
            WX_LINK_COMPONENT.put("loginAddress", config.get("WX_LINK_COMPONENT_LOGIN_ADDRESS","--"));
            WX_LINK_COMPONENT.put("imageUrl", config.get("WX_LINK_COMPONENT_IMAGE_URL","http://open.fsfte2.com/fscdn/img?imgId=group1/M00/00/DE/rB9ndlaUw--AH989AAAKe4iOlI4960.png"));
            WX_LINK_COMPONENT.put("appType", config.getInt("WX_LINK_COMPONENT_APP_TYPE", AppCenterEnum.AppType.BASE_APP.value()));
            WX_LINK_COMPONENT.put("isFsApp", config.getInt("WX_LINK_COMPONENT_IS_FS_APP",CommonConstant.YES));
            WX_LINK_COMPONENT.put("appId", config.get("WX_LINK_COMPONENT_COMPONENT_ID","FSAID_WX_LINK"));
            WX_LINK_COMPONENT.put("isNew", config.getInt("WX_LINK_COMPONENT_IS_NEW",CommonConstant.NO));
            WX_LINK_COMPONENT.put("isELink", config.getInt("WX_LINK_COMPONENT_IS_E_LINK",CommonConstant.NO));
            WX_LINK_COMPONENT.put("position", config.getInt("WX_LINK_COMPONENT_POSITION",2));
            WX_LINK_COMPONENT.put("parentId", config.get("WX_LINK_COMPONENT_PARENT_ID",""));

            PAY_WALLET_COMPONENT.put("componentId", config.get("PAY_WALLET_COMPONENT_COMPONENT_ID","FSAID_PW_LINK"));
            PAY_WALLET_COMPONENT.put("componentName", config.get("PAY_WALLET_COMPONENT_COMPONENT_NAME","企业支付")); // ignoreI18n
            PAY_WALLET_COMPONENT.put("loginAddress", config.get("PAY_WALLET_COMPONENT_LOGIN_ADDRESS","--"));
            PAY_WALLET_COMPONENT.put("imageUrl", config.get("PAY_WALLET_COMPONENT_IMAGE_URL",
                    "https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201703_22_718ff215bbde417480581d17f2ab219c.png&size=150_150&ea=appCenter&type=WEB&width=80&height=80"));
            PAY_WALLET_COMPONENT.put("appType", config.getInt("PAY_WALLET_COMPONENT_APP_TYPE", AppCenterEnum.AppType.BASE_APP.value()));
            PAY_WALLET_COMPONENT.put("isFsApp", config.getInt("PAY_WALLET_COMPONENT_IS_FS_APP",CommonConstant.YES));
            PAY_WALLET_COMPONENT.put("appId", config.get("PAY_WALLET_COMPONENT_COMPONENT_ID","FSAID_PW_LINK"));
            PAY_WALLET_COMPONENT.put("isNew", config.getInt("PAY_WALLET_COMPONENT_IS_NEW",CommonConstant.NO));
            PAY_WALLET_COMPONENT.put("isELink", config.getInt("PAY_WALLET_COMPONENT_IS_E_LINK",CommonConstant.NO));
            PAY_WALLET_COMPONENT.put("position", config.getInt("PAY_WALLET_COMPONENT_POSITION",2));
            PAY_WALLET_COMPONENT.put("parentId", config.get("PAY_WALLET_COMPONENT_PARENT_ID",""));
        });
        factory.getConfig(COMMON_CONFIG, (config -> {
            NOT_ALLOW_BIND_OFF_APP_IDS = config.get("NOT_ALLOW_BIND_OFF_APP_IDS", NOT_ALLOW_BIND_OFF_APP_IDS);
            NOT_ALLOW_UPDATE_APP_ADMIN_APP_IDS = config.get("NOT_ALLOW_UPDATE_APP_ADMIN_APP_IDS", NOT_ALLOW_UPDATE_APP_ADMIN_APP_IDS);
            NOT_ALLOW_UPDATE_COMPONENT_VIEW_APP_IDS = config.get("NOT_ALLOW_UPDATE_COMPONENT_VIEW_APP_IDS", NOT_ALLOW_UPDATE_COMPONENT_VIEW_APP_IDS);
            CRM_APP_ID = config.get("CRM_APP_ID");
            ENTERPRISE_WALLET_APP_ID = config.get("ENTERPRISE_WALLET_APP_ID");
            HELPER_APP_IDS = config.get("HELPER_APP_IDS");

            APP_IDS_UNABLE_TO_MANAGE = Arrays.asList(config.get("APP_IDS_UNABLE_TO_MANAGE", "").split(","));
            E_LINK_APP_IDS = Arrays.asList(config.get("E_LINK_APP_IDS", "").split(","));
            WX_LINK_APP_IDS = Arrays.asList(config.get("WX_LINK_APP_IDS", "").split(","));
            WX_LINK_NOTICE_ATTACHMENT_MAX_SIZE = config.getInt("WX_LINK_NOTICE_ATTACHMENT_MAX_SIZE", WX_LINK_NOTICE_ATTACHMENT_MAX_SIZE);
            WX_LINK_NOTICE_ATTACHMENT_MAX_FILE_SIZE = config.getLong("WX_LINK_NOTICE_ATTACHMENT_MAX_FILE_SIZE", WX_LINK_NOTICE_ATTACHMENT_MAX_FILE_SIZE);
            IS_BIND_OUTER_SERVICE_NOTICE = config.get("IS_BIND_OUTER_SERVICE_NOTICE", IS_BIND_OUTER_SERVICE_NOTICE);
            WECHAT_IS_UNAUTHERIZED_NOTICE = config.get("WECHAT_IS_UNAUTHERIZED_NOTICE", WECHAT_IS_UNAUTHERIZED_NOTICE);
            WECHAT_SUBSCRIBE_NOTICE = config.get("WECHAT_SUBSCRIBE_NOTICE", WECHAT_SUBSCRIBE_NOTICE);
            WECHAT_DEFAULT_ICON = config.get("WECHAT_DEFAULT_ICON");
            UNBIND_FROM_WECHAT_NOTICE = config.get("UNBIND_FROM_WECHAT_NOTICE", UNBIND_FROM_WECHAT_NOTICE);
            UNBIND_FROM_OUTER_SERVICE_NOTICE = config.get("UNBIND_FROM_OUTER_SERVICE_NOTICE", UNBIND_FROM_OUTER_SERVICE_NOTICE);
            WECHAT_CERTIFICATION_ADDRESS = config.get("WECHAT_CERTIFICATION_ADDRESS", WECHAT_CERTIFICATION_ADDRESS);

            WECHAT_IS_UNAUTHERIZED_TOOLTIP = config.get("WECHAT_IS_UNAUTHERIZED_TOOLTIP", WECHAT_IS_UNAUTHERIZED_TOOLTIP);
            WECHAT_SUBSCRIBE_TOOLTIP = config.get("WECHAT_SUBSCRIBE_TOOLTIP", WECHAT_SUBSCRIBE_TOOLTIP);
            UNBIND_FROM_WECHAT_TOOLTIP = config.get("UNBIND_FROM_WECHAT_TOOLTIP", UNBIND_FROM_WECHAT_TOOLTIP);
            UNBIND_FROM_OUTER_SERVICE_TOOLTIP = config.get("UNBIND_FROM_OUTER_SERVICE_TOOLTIP", UNBIND_FROM_OUTER_SERVICE_TOOLTIP);
            UPGRADE_TO_CUSTOMER_SERVICE_NOTICE = config.get("UPGRADE_TO_CUSTOMER_SERVICE_NOTICE", UPGRADE_TO_CUSTOMER_SERVICE_NOTICE);
            WECHAT_UNBIND_PERMISSON_TOOLTIP = config.get("WECHAT_UNBIND_PERMISSON_TOOLTIP", WECHAT_UNBIND_PERMISSON_TOOLTIP);

            OUTER_SERVICE_CREATE_SUCCESS_AUTH1 = config.get("OUTER_SERVICE_CREATE_SUCCESS_AUTH1", OUTER_SERVICE_CREATE_SUCCESS_AUTH1);
            OUTER_SERVICE_CREATE_SUCCESS_AUTH2 = config.get("OUTER_SERVICE_CREATE_SUCCESS_AUTH2", OUTER_SERVICE_CREATE_SUCCESS_AUTH2);
            OUTER_SERVICE_CREATE_SUCCESS_AUTH3 = config.get("OUTER_SERVICE_CREATE_SUCCESS_AUTH3", OUTER_SERVICE_CREATE_SUCCESS_AUTH3);
            OUTER_SERVICE_CREATE_SUCCESS_AUTH4 = config.get("OUTER_SERVICE_CREATE_SUCCESS_AUTH4", OUTER_SERVICE_CREATE_SUCCESS_AUTH4);

            BIND_APP_URL_SUFFIX = config.get("BIND_APP_URL_SUFFIX", BIND_APP_URL_SUFFIX);
            FS_OPEN_ASSETS_IMAGE_URL = config.get("FS_OPEN_ASSETS_IMAGE_URL", FS_OPEN_ASSETS_IMAGE_URL);
            SERIVCE_OFF_NOTIC_PER = config.get("SERIVCE_OFF_NOTIC_PER", SERIVCE_OFF_NOTIC_PER);
            BJ_IMAGE_VIEW_URL = config.get("BJ_IMAGE_VIEW_URL", BJ_IMAGE_VIEW_URL);
            TRAINING_ASSISTANT_APP_ID = config.get("TRAINING_ASSISTANT_APP_ID", TRAINING_ASSISTANT_APP_ID);
            FXIAOKE_HOME_INDEX_URL = config.get("FXIAOKE_HOME_INDEX_URL", FXIAOKE_HOME_INDEX_URL);
            //通知业务
            WX_LINK_NOTICE_APP_ID = config.get("WX_LINK_NOTICE_APP_ID", WX_LINK_NOTICE_APP_ID);
            WX_LINK_NOTICE_WEB_COMPONENT_ID = config.get("WX_LINK_NOTICE_WEB_COMPONENT_ID", WX_LINK_NOTICE_WEB_COMPONENT_ID);
            WORK_ORDER_PAAS_URL = config.get("WORK_ORDER_PAAS_URL", WORK_ORDER_PAAS_URL);
            APPROVAL_URL = config.get("APPROVAL_URL", APPROVAL_URL);
             //支付应用id
            PAY_WALLET_APP_ID = config.get("PAY_WALLET_APP_ID", "");

            YH_EA = config.get("YH_EA", "");
            TRAIN_APP_COMPONENT_ID = stringToList(config.get("TRAIN_APP_COMPONENT_ID"));
            TRAIN_APP_ID = config.get("TRAIN_APP_ID","");
            YH_TRAIN_NAME = config.get("YH_TRAIN_NAME","永辉微学院"); // ignoreI18n
            TRAIN_APP_LOGO = config.get("TRAIN_APP_LOGO","https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201705_04_a46ed5a757e44129976c70b4a5213a82.jpg&size=150_150&ea=appCenter");

            CUSTOM_APP_SCOPES = config.get("CUSTOM_APP_SCOPES", CUSTOM_APP_SCOPES);
            CUSTOM_SERVICE_SCOPES = config.get("CUSTOM_SERVICE_SCOPES", CUSTOM_SERVICE_SCOPES);
            ESERVICE_WORK_ORDER_GRAY_EAS = config.get("ESERVICE_WORK_ORDER_GRAY_EAS", ESERVICE_WORK_ORDER_GRAY_EAS);
            ONLINE_SERVICE_ASSISTANT_GRAY_EAS = config.get("ONLINE_SERVICE_ASSISTANT_GRAY_EAS", ONLINE_SERVICE_ASSISTANT_GRAY_EAS);
            useNewScopeAppList = Sets.newHashSet(JSON.parseArray(config.get("allow_appList", "[]"), String.class));
            SYS_ICON_START = config.get("SYS_ICON_START", SYS_ICON_START);
        }));
    }

    public static Map<String, List<String>> getForceOfflineAppIdToEaBlackList(String s) {
        try {
            if (StringUtils.isBlank(s)) {
                return Maps.newHashMap();
            }
            return JsonKit.fromJson(s, new TypeToken<Map<String, List<String>>>() {
            }.getType());
        } catch (Exception e) {
            log.error("getForceOfflineAppIdToWhiteList error. s[{}]", s, e);
            return new HashMap<>();
        }
    }

    private static List<String> stringToList(String s) {
        if (StringUtils.isBlank(s)) {
            return new ArrayList<>();
        }
        String[] strArray = StringUtils.split(s, ",");
        return Arrays.asList(strArray);
    }

    public static boolean grayToEserviceWorkOrder(String s) {
        return inConfigs(ESERVICE_WORK_ORDER_GRAY_EAS, s);
    }
    
    public static boolean grayToOnlineServiceAssistant(String s) {
        return inConfigs(ONLINE_SERVICE_ASSISTANT_GRAY_EAS, s);
    }

    public static boolean inConfigs(String value, String s) {
        List<String> list = stringToList(value);
        return list.contains(s) || list.contains(ALL);
    }
    public static boolean grayNewScopeApp(String appId){
        if(CollectionUtils.isNotEmpty(useNewScopeAppList)){
            return useNewScopeAppList.contains(appId);
        }
        return false;
    }
}
