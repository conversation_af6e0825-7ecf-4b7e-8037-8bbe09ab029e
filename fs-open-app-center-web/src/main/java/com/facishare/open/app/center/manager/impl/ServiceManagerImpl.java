package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.ad.api.enums.ModuleKeyEnum;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.facishare.open.app.center.api.model.vo.NotifyUserViewVO;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.AppViewResult;
import com.facishare.open.app.center.api.service.OpenAppComponentService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.manager.OuterServiceManager;
import com.facishare.open.app.center.manager.ServiceManager;
import com.facishare.open.app.center.manager.ServiceNumberManager;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.material.api.service.MessageCommentService;
import com.facishare.open.msg.constant.CustomerServiceMsgType;
import com.facishare.open.msg.model.QueryUnReadUserSessionVO;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.facishare.open.msg.service.MsgSessionService;
import com.facishare.open.operating.center.api.service.CloseableEventService;
import com.facishare.open.operating.center.api.service.FirstTimeEventService;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.IntStream;

import static com.facishare.open.operating.center.api.model.enums.CloseableEvent.SERVICE_DESC_BANNER;

/**
 * Created by liqiulin on 2016/9/2.
 */
@Service
public class ServiceManagerImpl implements ServiceManager {
    private static final Logger logger = LoggerFactory.getLogger(MaterialManagerImpl.class);

    @Resource
    MessageCommentService messageCommentService;

    @Resource
    private FirstTimeEventService firstTimeEventService;

    @Resource
    private MessageExhibitionService messageExhibitionService;

    @Resource
    private MsgSessionService msgSessionService;

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private ServiceNumberManager serviceNumberManager;

    @Resource
    private OuterServiceManager outerServiceManager;

    @Resource
    private OpenAppComponentService openAppComponentService;


    @Override
    public boolean hasUnreadMsg(String appId, String fsEa) {

        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("call openAppService.loadOpenApp fail.appId[{}], fsEa[{}], appResult[{}]", appId, fsEa, appResult);
            throw new BizException(appResult);
        }

        QueryUnReadUserSessionVO queryUnReadUserSessionVO = new QueryUnReadUserSessionVO();
        queryUnReadUserSessionVO.setUpStreamEa(fsEa);
        queryUnReadUserSessionVO.setAppIds(Arrays.asList(appId));
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            queryUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            queryUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }

        MessageExhibitionResult<Map<String, Boolean>> unReadStatusResult = messageExhibitionService.queryUnReadUserSessions(queryUnReadUserSessionVO);
        if (!unReadStatusResult.isSuccess()) {
            logger.warn("messageExhibitionService.queryUnReadUserSessions failed. QueryUnReadUserSessionVO[{}], result[{}]", queryUnReadUserSessionVO, unReadStatusResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, unReadStatusResult, "查询是否存在未读上行消息失败"); // ignoreI18n
        }
        if (!CollectionUtils.isEmpty(unReadStatusResult.getData())) {
            return unReadStatusResult.getData().get(appId);
        }
        return false;
    }

    @Resource
    private CloseableEventService closeableEventService;

    @Override
    public boolean hasUnreadComment(String appId, String fsEa) {
        BaseResult<Boolean> result = messageCommentService.hasUnreadComment(appId, fsEa);
        if(!result.isSuccess()) {
            logger.warn("failed to call messageCommentService.hasUnreadComment, appId[{}], fsEa[{}], result[{}]",
                    appId, fsEa, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "查询是否存在未读评论失败"); // ignoreI18n
        }
        return result.getResult();
    }

    @Override
    public void clearUnreadCommentCount(String appId, String fsEa, Integer userId) {
        BaseResult<Void> result = messageCommentService.clearUnreadCommentCount(appId, fsEa, userId);
        if(!result.isSuccess()) {
            logger.warn("failed to call messageCommentService.clearUnreadCommentCount, appId[{}], fsEa[{}], result[{}]",
                    appId, fsEa, result);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, result, "清除文章未读评论数失败"); // ignoreI18n
        }
    }

    @Override
    public List<String> queryDashBoardGuide(FsUserVO fsUserVO) {
        boolean needLatestUpgradeGuide = needLatestUpgradeGuide(fsUserVO);
        if (needLatestUpgradeGuide) {
            boolean needAllGuide = needDashBoardAllGuide(fsUserVO, false);
            if (needAllGuide) {
                return ConfigCenter.SERVICE_DASHBOARD_GUIDE_KEYS_ALL;
            } else {
                return ConfigCenter.SERVICE_DASHBOARD_GUIDE_KEYS_LATEST_UPGRADE;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean isShowBanner(FsUserVO fsUserVO, String appId) {
        String eventExecutor = FsUserVO.toFsUserString(fsUserVO) + "." + appId;
        BaseResult<Boolean> isFirstTimeResult = closeableEventService.isClosedEvent(SERVICE_DESC_BANNER, eventExecutor);
        if (!isFirstTimeResult.isSuccess()) {
            logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                            "eventFlag[{}], eventExecutor[{}], isFirstTimeResult[{}]",
                    SERVICE_DESC_BANNER, eventExecutor, isFirstTimeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isFirstTimeResult, "判断是够显示推荐服务号提示失败"); // ignoreI18n
        }
        return !isFirstTimeResult.getResult();
    }

    @Override
    public void closeBanner(FsUserVO fsUserVO, String appId) {
        String eventExecutor = FsUserVO.toFsUserString(fsUserVO) + "." + appId;
        BaseResult<Void> isFirstTimeResult = closeableEventService.closeEvent(SERVICE_DESC_BANNER, eventExecutor);
        if (!isFirstTimeResult.isSuccess()) {
            logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                            "eventFlag[{}], eventExecutor[{}], isFirstTimeResult[{}]",
                    SERVICE_DESC_BANNER, eventExecutor, isFirstTimeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isFirstTimeResult, "关闭推荐服务号提示失败"); // ignoreI18n
        }
    }

    private boolean needLatestUpgradeGuide(FsUserVO fsUserVO) {
        String eventFlag = ConfigCenter.SERVICE_DASHBOARD_LATEST_UPGRADE_EVENT_FLAG;
        String eventExecutor = FsUserVO.toFsUserString(fsUserVO);
        BaseResult<Boolean> isFirstTimeResult =
                firstTimeEventService.isFirstTime(eventFlag, eventExecutor);
        if (!isFirstTimeResult.isSuccess()) {
            logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                            "eventFlag[{}], eventExecutor[{}], isFirstTimeResult[{}]",
                    eventFlag, eventExecutor, isFirstTimeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isFirstTimeResult, "判断第一次事件失败"); // ignoreI18n
        }
        return isFirstTimeResult.getResult();
    }

    @Override
    public boolean needDashBoardAllGuide(FsUserVO fsUserVO, boolean onlyQuery) {
        String eventFlag = CenterConstants.FIRST_TIME_EVENT_FLAG_SERVICE_DASHBOARD_GUIDE_ALL;
        String eventExecutor = FsUserVO.toFsUserString(fsUserVO);
        BaseResult<Boolean> isFirstTimeResult =
                firstTimeEventService.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        if (!isFirstTimeResult.isSuccess()) {
            logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                            "eventFlag[{}], eventExecutor[{}], isFirstTimeResult[{}]",
                    eventFlag, eventExecutor, isFirstTimeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isFirstTimeResult, "判断第一次事件失败"); // ignoreI18n
        }
        return isFirstTimeResult.getResult();
    }

    @Override
    public void updateSessionNameLogoDesc(FsUserVO user, String appId, String serviceName, String logoUrl, String desc) {
        try {
            AppResult appResult = openAppService.loadOpenAppFast(appId);
            if (!appResult.isSuccess() || null == appResult.getResult()) {
                logger.error("updateSessionNameLogoDesc failed to loadOpenAppFast:  appId={}, result={}", appId, appResult);
                return;
            }
            if (IntStream.of(AppCenterEnum.AppType.SERVICE.value(), AppCenterEnum.AppType.OUT_SERVICE_APP.value(),
                    AppCenterEnum.AppType.CUSTOM_APP.value())
                    .noneMatch(t -> t == appResult.getResult().getAppType())) {
                return;
            }

            if(AppCenterEnum.AppType.CUSTOM_APP.value() == appResult.getResult().getAppType()){
                if(StringUtils.isNotEmpty(serviceName) || StringUtils.isNotEmpty(logoUrl)){
                    MessageResult messageResult = msgSessionService.updateUniversalSessionDefinition(appId, serviceName, logoUrl);
                    if (!messageResult.isSuccess()) {
                        logger.warn("CUSTOM_APP updateSessionNameLogoDesc failed to updateUniversalSessionDefinition: appId={}, serviceName={}, logourl={}, result={}",
                                appId, serviceName, logoUrl, messageResult);
                    }
                }
                return;
            }

            Integer serviceType = null;
            boolean isService = false; //外联服务号没有服务号session 则不需要更新
            if (AppCenterEnum.AppType.SERVICE.value() == appResult.getResult().getAppType()){
                serviceType = CommonConstant.IS_SERVICE;
                isService = true;
                updateComponentNameOrDesc(user, appId, serviceName, desc);
            }else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType()){
                serviceType = CommonConstant.IS_OUTER_SERVICE;
            }

            //更新名称
            if (StringUtils.isNotEmpty(serviceName)) {
                if (isService){
                    MessageResult messageResult = msgSessionService.updateUniversalSessionDefinition(appId, serviceName, logoUrl);
                    if (!messageResult.isSuccess()) {
                        logger.warn("updateSessionNameLogoDesc failed to updateUniversalSessionDefinition: appId={}, serviceName={}, logourl={}, result={}",
                                appId, serviceName, logoUrl, messageResult);
                    }
                }
                String workBenchName = serviceName + CenterConstants.SERVICE_NUMBER_MSG;
                PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
                platformMetaSessionVO.setCustomerSessionName(workBenchName);
                serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, serviceType);
            }

            //更新头像
            if (StringUtils.isNotEmpty(logoUrl)) {
                if (isService) {
                    MessageResult messageResult = msgSessionService.updateUniversalSessionDefinition(appId, serviceName, logoUrl);
                    if (!messageResult.isSuccess()) {
                        logger.warn("updateSessionNameLogoDesc failed to updateUniversalSessionDefinition: appId={}, serviceName={}, logourl={}, result={}",
                                appId, serviceName, logoUrl, messageResult);
                    }
                }
                PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
                platformMetaSessionVO.setCustomerSessionPortrait(logoUrl);
                serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, serviceType);
            }

            //更新描述
            if (StringUtils.isNotEmpty(desc)) {
                if (isService) {
                    MessageResult descResult = msgSessionService.updateSessionInfo(appId, desc, false, true, null);
                    if (!descResult.isSuccess()) {
                        logger.warn("updateSessionNameLogoDesc failed to updateSessionInfo: appId={}, desc={}, result={}",
                                appId, desc, descResult);
                    }
                }
                PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
                platformMetaSessionVO.setAdminDescription(desc);
                platformMetaSessionVO.setCustomerDescription(desc);
                serviceNumberManager.modifyServiceWorkBenchSession(user, appId, platformMetaSessionVO, ServiceTypeEnum.UPDATE, serviceType);
            }

            if (AppCenterEnum.AppType.SERVICE.value() == appResult.getResult().getAppType()) {
                notifyUserViewAsync(user, appId);
            } else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == appResult.getResult().getAppType()) {
                outerServiceManager.notifyOuterServiceModified(user, appId);
            }
        } catch (Exception e) {
            logger.error("failed to updateSessionNameLogoDesc: appId={}, serviceName={}, logourl={}, result={}"
                    + appId, serviceName, logoUrl, e);
        }
    }

    @Override
    public void notifyUserViewAsync(FsUserVO user, String appId) {
        CommonThreadPoolUtils.getExecutor().execute(() -> {
            try {
                com.facishare.open.app.center.api.result.BaseResult<List<OpenAppComponentDO>> result = openAppComponentService.queryAppComponentListByAppId(user, appId);
                if (!result.isSuccess()) {
                    logger.warn("queryAppComponentListByAppId failed,appId[{}], result[{}]", appId, result);
                    return;
                }
                if (!CollectionUtils.isEmpty(result.getResult())) {
                    result.getResult().forEach(componentDo -> {
                        NotifyUserViewVO notifyUserViewVO = new NotifyUserViewVO();
                        notifyUserViewVO.setFsEa(user.getEnterpriseAccount());
                        notifyUserViewVO.setAppId(appId);
                        AppComponentTypeEnum componentTypeEnum = AppComponentTypeEnum.getByCode(componentDo.getComponentType());
                        AppViewResult appViewResult =
                                (AppViewResult) openFsUserAppViewService.loadAppViewByType(user, componentDo.getComponentId(), componentTypeEnum);
                        if (!appViewResult.isSuccess()) {
                            logger.warn("loadAppViewByType failed,appId[{}], user[{}], result={}", appId, user, appViewResult);
                        }
                        AppViewDO appViewDO = appViewResult.getResult();
                        if (null != appViewDO) {
                            notifyUserViewVO.setView(EmployeeRange.fromAppView(appViewDO));
                        }
                        notifyUserViewVO.setCheckAppUpdatedKey(ModuleKeyEnum.SERVICE_NUMBER.getModuleKey());
                        notifyUserViewVO.setMsgSessionKey(CommonConstant.UPDATE_USER_PROPERTIES_SERVICE_NUMBER_MODIFIED);
                        openFsUserAppViewService.notifyUserViewAsync(user, notifyUserViewVO);
                    });
                }
            } catch (Exception e) {
                logger.warn("failed to call checkAppUpdatedService.resetTagByServiceNumberModuleKey, user[{}], moduleKeyEnum={}", user, ModuleKeyEnum.SERVICE_NUMBER, e);
            }
        });
    }

    /**
     * 修改服务号的name,desc需要同步更新组件的那那name, desc
     *
     * @param user        用户
     * @param appId       服务号Id
     * @param serviceName 名称
     * @param desc        描述
     */
    private void updateComponentNameOrDesc(FsUserVO user, String appId, String serviceName, String desc) {
        if (Strings.isNullOrEmpty(serviceName) && Strings.isNullOrEmpty(desc)){
            return;
        }
        com.facishare.open.app.center.api.result.BaseResult<List<OpenAppComponentDO>> result = openAppComponentService.queryAppComponentListByAppId(user, appId);
        if (!result.isSuccess()) {
            logger.warn("queryAppComponentListByAppId failed,appId[{}], result[{}]", appId, result);
            return;
        }

        if (!CollectionUtils.isEmpty(result.getResult())) {
            result.getResult().forEach(componentDo -> {
                OpenAppComponentDO entity = new OpenAppComponentDO();
                entity.setComponentId(componentDo.getComponentId());
                entity.setComponentName(serviceName);
                entity.setComponentDesc(desc);
                entity.setGmtModified(new Date());
                com.facishare.open.app.center.api.result.BaseResult<Void> baseResult = openAppComponentService.updateOpenAppComponent(user, entity);
                if (!baseResult.isSuccess()) {
                    logger.warn("createCustomComponent failed,appId[{}],openAppComponentVO [{}] result[{}]", appId, entity, baseResult);
                }
            });
        }
    }
}
