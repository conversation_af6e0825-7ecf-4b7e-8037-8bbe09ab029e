package com.facishare.open.app.center.controller;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.ad.api.service.NewComponentService;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxComponentViewResult;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.form.CancelNewStateForm;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.I18NUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户控制是否显示app
 *
 * <AUTHOR>
 * @date 2015年8月3日
 */
@Controller
@RequestMapping("/open/appcenter/app/view")
public class AppViewController extends BaseController {

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Resource
    private NewComponentService newComponentService;
    @Resource
    private EIEAConverter eieaConverter;

    /**
     * 加载用户的可显示的组件
     *
     * @param fsUserVO 操作用户.
     * @return 组件列表.
     */
    @RequestMapping("/queryComponentListByFsUser")
    @ResponseBody
    public AjaxComponentViewResult queryComponentListByFsUser(@ModelAttribute FsUserVO fsUserVO, @ModelAttribute String lang) {
        //1.获取展示应用列表
        BaseResult<List<UserCanViewListVO>> listResult = openFsUserAppViewService
                .queryComponentsByFsUser(fsUserVO, AppAccessTypeEnum.WEB);
        if (!listResult.isSuccess()) {
            logger.warn(
                    "failed to call openFsUserAppViewService.queryComponentsByFsUser, user={}, accessType={}, result={}",
                    fsUserVO, AppAccessTypeEnum.WEB, listResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, listResult, listResult.getErrDescription());
        }
        //2.应用列表根据创建时间倒序
        List<Map<String, Object>> data = new ArrayList<>();
        if (null != listResult.getResult() && !listResult.getResult().isEmpty()) {
            List<UserCanViewListVO> lstCompVO = listResult.getResult();
            Collections.sort(lstCompVO, (UserCanViewListVO o1, UserCanViewListVO o2) -> {
                        long a = null == o1.getBindTime() ? 0 : o1.getBindTime().getTime();
                        long b = null == o2.getBindTime() ? 0 : o2.getBindTime().getTime();
                        return a == b ? 0 : (a > b ? 1 : -1);
                    }
            );

            List<String> componentIds = lstCompVO.stream().map(UserCanViewListVO::getComponentId).collect(Collectors.toList());
            com.facishare.open.common.result.BaseResult<List<Integer>> newStatusResult = newComponentService.checkNewStatus(fsUserVO, componentIds);
            List<Integer> newStatusList = newStatusResult.getResult();
            if (!newStatusResult.isSuccess()) {
                logger.warn("checkNewStatus failed, user[{}]", fsUserVO);
            }
            int i = 0;
            boolean isNewStatusListEmpty = CollectionUtils.isEmpty(newStatusList);

            //3.前端数据封装
            Map<String, Object> compData = null;
            List<Map<String, Object>> fsELink = new ArrayList<>();
            List<Map<String, Object>> wxELink = new ArrayList<>();
            //支付
            List<Map<String, Object>> pw = new ArrayList<>();
            int isNewFsELink = CommonConstant.NO;
            int isNewWxLink = CommonConstant.NO;
            int isNewPw = CommonConstant.NO;

            int ei = eieaConverter.enterpriseAccountToId(fsUserVO.getEa());
            Map<String, String> componentNameMap = I18NUtils.batchGetValues(ei, I18NUtils.FORMAT_COMPONENT_NAME_STRING_KEY, componentIds, lang);
            for (UserCanViewListVO comp : lstCompVO) {
                compData = new HashMap<>();
                compData.put("componentId", comp.getComponentId());
                //多语言替换
                String componentName = componentNameMap.get(String.format(I18NUtils.FORMAT_COMPONENT_NAME_STRING_KEY, comp.getComponentId()));
                componentName = StringUtils.isBlank(componentName) ? comp.getComponentName() : componentName;
                compData.put("componentName", componentName);
                compData.put("loginAddress", comp.getCallBackUrl());
                compData.put("imageUrl", comp.getImageUrl());
                compData.put("appType", comp.getAppType());
                compData.put("isFsApp", comp.getIsFsApp());
                compData.put("appId", comp.getAppId());
                compData.put("isNew", CommonConstant.NO);
                if (!isNewStatusListEmpty) {
                    compData.put("isNew", newStatusList.get(i++));
                }
                //是否企业互联.
                compData.put("isELink", CommonConstant.NO);
                compData.put("position", 1);
                compData.put("parentId", "");

                if (BizCommonUtils.isELinkApp(comp.getAppId())) {
                    compData.put("isELink", CommonConstant.YES);
                    compData.put("position", 3);
                    compData.put("parentId", ConfigCenter.FS_EA_LINK_COMPONENT.get("componentId"));
                    if (CommonConstant.YES == (int)compData.get("isNew")){
                        isNewFsELink = CommonConstant.YES;
                    }
                    fsELink.add(compData);
                    continue;
                }
                if (ConfigCenter.WX_LINK_NOTICE_APP_ID.contains(comp.getAppId())) {
                    compData.put("isELink", CommonConstant.YES);
                    compData.put("position", 3);
                    compData.put("parentId", ConfigCenter.WX_LINK_COMPONENT.get("componentId"));
                    if (CommonConstant.YES == (int)compData.get("isNew")){
                        isNewWxLink = CommonConstant.YES;
                    }
                    wxELink.add(compData);
                    continue;
                }

                if (ConfigCenter.PAY_WALLET_APP_ID.contains(comp.getAppId())) {
                    compData.put("position", 3);
                    compData.put("parentId", ConfigCenter.PAY_WALLET_COMPONENT.get("componentId"));
                    if (CommonConstant.YES == (int)compData.get("isNew")){
                        isNewPw = CommonConstant.YES;
                    }
                    pw.add(compData);
                    continue;
                }


                data.add(compData);
            }
            if (!CollectionUtils.isEmpty(fsELink)) {
                HashMap<String, Object> map = Maps.newHashMap(ConfigCenter.FS_EA_LINK_COMPONENT);
                map.put("isNew", isNewFsELink);
                //替换多语言
                modifyComponentNameByLang(ei, map, lang);

                data.add(map);
                data.addAll(fsELink);
            }

            if (!CollectionUtils.isEmpty(wxELink)) {
                HashMap<String, Object> map = Maps.newHashMap(ConfigCenter.WX_LINK_COMPONENT);
                map.put("isNew", isNewWxLink);
                //替换多语言
                modifyComponentNameByLang(ei, map, lang);
                data.add(map);
                data.addAll(wxELink);
            }

            if (!CollectionUtils.isEmpty(pw)) {
                HashMap<String, Object> map = Maps.newHashMap(ConfigCenter.PAY_WALLET_COMPONENT);
                map.put("isNew", isNewPw);
                //替换多语言
                modifyComponentNameByLang(ei, map, lang);
                data.add(map);
                data.addAll(pw);
            }
        }

        //用户是否免费,目前逻辑已经清理,设置为false,. 后期可以设置为new AjaxResult(data) 回去.
        AjaxComponentViewResult ajaxComponentViewResult = new AjaxComponentViewResult(data);
        ajaxComponentViewResult.setIsFreeUser(CommonConstant.NO);
        ajaxComponentViewResult.setCanFreeTrial(CommonConstant.NO);

        return ajaxComponentViewResult;
    }

    private void modifyComponentNameByLang(int ei, Map<String, Object> map, String lang) {
        String componentId = (String)map.get("componentId");
        if (StringUtils.isNotBlank(componentId)) {
            Map<String, String> componentNameMap = I18NUtils.batchGetValues(ei, I18NUtils.FORMAT_COMPONENT_NAME_STRING_KEY, Arrays.asList(componentId), lang);
            String componentName = componentNameMap.get(String.format(I18NUtils.FORMAT_COMPONENT_NAME_STRING_KEY, componentId));
            if (StringUtils.isNotBlank(componentName)) {
                map.put("componentName", componentName);
            }
        }
    }

    /**
     * 加载用户的可显示的组件.
     *
     * @param fsUserVO 操作用户.
     * @return 组件列表.
     */
    @RequestMapping(value = "/cancelNewState", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult cancelNewState(@ModelAttribute FsUserVO fsUserVO,
                                     @RequestBody CancelNewStateForm cancelNewStateForm) {
        String componentId = cancelNewStateForm.getComponentId();
        checkParamNotBlank(componentId, "请输入组件id"); // ignoreI18n
        com.facishare.open.common.result.BaseResult<Void> cancelNewStateResult = newComponentService.cancelNewState(fsUserVO, componentId);
        if (!cancelNewStateResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, cancelNewStateResult, "取消标新状态失败."); // ignoreI18n
        }
        return SUCCESS;
    }
}
