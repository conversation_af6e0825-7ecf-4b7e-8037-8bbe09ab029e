package com.facishare.open.app.center.manager.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.CallBackBindDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.*;
import com.facishare.open.app.center.api.model.property.OpenAppProperties;
import com.facishare.open.app.center.api.model.vo.OpenAppVO;
import com.facishare.open.app.center.api.result.AppListResult;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.result.StatusResult;
import com.facishare.open.app.center.api.service.*;
import com.facishare.open.app.center.common.utils.BeanUtil;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.center.model.ComponentParam;
import com.facishare.open.app.center.model.QueryAppsVO;
import com.facishare.open.app.center.model.enums.SystemFunctionCodeEnum;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.I18NUtils;
import com.facishare.open.app.center.utils.OpenAppUtils;
import com.facishare.open.app.pay.api.enums.PayStatus;
import com.facishare.open.app.pay.api.model.QuotaRecordVo;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType.*;

/**
 * Created by xialf on 10/19/15.
 *
 * <AUTHOR>
 */
@Service
public class AppBindManagerImpl implements AppBindManager {
    private final Logger logger = LoggerFactory.getLogger(AppBindManagerImpl.class);

    @Resource
    private OpenAppService openAppService;

    @Resource
    private OpenAppAdminService openAppAdminService;

    @Resource
    private OpenFsUserBindAppService openFsUserBindAppService;

    @Resource
    private OpenAppComponentService openAppComponentService;

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;

    @Resource
    private TryStatusService tryStatusService;

    @Resource
    private AppCallBackService appCallBackService;

    @Resource
    private QuotaService quotaService;

//    @Resource
//    private TrialService trialService;

    @Resource
    private WebAuthManager webAuthManager;

    @Resource
    private AppManager appManager;

    @Value("${fs.open.app.center.AppBindController.nearbyCustomersAppId}")
    private String nearbyCustomersAppId;

    @Resource
    private AppIconManager appIconManager;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private QuotaManager quotaManager;
    @Autowired
    private DingTalkAppManager dingTalkAppManager;


    @Override
    public Map<String, Object> addAppByFsAdmin(FsUserVO user, String appId, Collection<Integer> appAdminNumIds) {

        StatusResult statusResult = openFsUserBindAppService.addDevApp(FsUserVO.toFsUserString(user), appId, appAdminNumIds);
        if (!statusResult.isSuccess()) {
            logger.error("failed to call openFsUserBindAppService.addDevApp,user={},appId={},appAdminNumIds={}, result={}",
                    FsUserVO.toFsUserString(user), appId, appAdminNumIds, statusResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "添加托管应用不成功"); // ignoreI18n
        }

        return queryComponentsUrl(user, appId);
    }

    /**
     * 获取应用的所有组件的跳转地址.
     *
     * @param user  纷享用户
     * @param appId 应用id
     * @return
     */
    private Map<String, Object> queryComponentsUrl(final FsUserVO user, final String appId) {
        // 获取组件的跳转地址 - 获取组件列表
        final BaseResult<List<OpenAppComponentDO>> componentsResult =
                openAppComponentService.queryAppComponentListByAppId(user, appId);
        if (!componentsResult.isSuccess()) {
            logger.warn("fail to call openAppComponentService.queryAppComponentListByAppId, user={}, appId={}, result={}",
                    user, appId, componentsResult);
            throw new BizException(componentsResult);
        }
        List<Map<String, Object>> componentsData = new ArrayList<>();
        for (final OpenAppComponentDO componentDO : componentsResult.getResult()) {
            final String componentId = componentDO.getComponentId();
            final AppComponentTypeEnum componentTypeEnum = AppComponentTypeEnum.getByCode(componentDO.getComponentType());
            final AppViewTypeEnum viewTypeEnum =
                    componentTypeEnum == AppComponentTypeEnum.APP ? AppViewTypeEnum.APP : AppViewTypeEnum.WEB;

            //获取跳转地址
            CallBackBindDO args = new CallBackBindDO();
            args.setAppOrComponentId(componentId);
            args.setFsUserId(user.asStringUser());
            args.setAppType(viewTypeEnum);
            BaseResult bindResult = appCallBackService.getAppCallBackBindParam(args);
            if (!bindResult.isSuccess()) {
                logger.warn("fail to callBackBizService.getCallBackBindParam, args={}, result={}",
                        args, bindResult);
                throw new BizException(bindResult);
            }

            Map<String, Object> componentData = new HashMap<>();
            componentData.put("componentId", componentId);
            componentData.put("componentType", componentDO.getComponentType());
            componentData.put("callbackUrl", bindResult.getResult());
            componentsData.add(componentData);
        }

        if (appId.equals(BizCommonUtils.getCrmAppId())) { //CRM做特殊处理, 因为其没有web组件, 但是又需要跳转地址
            Map<String, Object> componentData = new HashMap<>();
            componentData.put("componentType", AppComponentTypeEnum.WEB.getType());
            componentData.put("callbackUrl", "#crmindex/=/source-app");

            componentsData.add(componentData);
        }

        Map<String, Object> ajaxMapData = new HashMap<>();
        ajaxMapData.put("appId", appId);
        ajaxMapData.put("components", componentsData);
        return ajaxMapData;
    }

    @Override
    public Map<String, Object> addAppByUser(FsUserVO user, String appId, Collection<Integer> appAdminNumIds, List<ComponentParam> componentParams) {
        //付费规则: 根据状态来判断下一步动作
        final AppResult appResult = openAppService.loadOpenApp(appId);
        if (!appResult.isSuccess()) {
            logger.warn("fail to load app, appId={}, result={}",
                    appId, appResult);
            throw new BizException(appResult);
        }

        if (Integer.valueOf(1).equals(appResult.getResult().getPayType())) { //扩展应用->付费应用（付费应用都属于扩展应用）
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "请联系贵公司管理员开通此应用"); // ignoreI18n
        }

        final boolean isFsAdmin = webAuthManager.isFsAdmin(user);

        final com.facishare.open.common.result.BaseResult<TryStatusEnum> tryStatusEnumBaseResult =
                tryStatusService.queryTryStatus(appResult.getResult(), user);
        if (!tryStatusEnumBaseResult.isSuccess()) {
            logger.warn("fail to tryStatusService.queryTryStatus, appId={}, user={}, result={}",
                    appId, user, tryStatusEnumBaseResult);
            throw new BizException(tryStatusEnumBaseResult);
        }
        final TryStatusEnum tryStatusEnum = tryStatusEnumBaseResult.getResult();

        // 加载统计信息.
        Map<String, Object> statMap = new HashMap<>();
        try {
            statMap.put("fsEa", user.getEnterpriseAccount());
            statMap.put("userId", user.getUserId());
            statMap.put("appId", appId);
            statMap.put("appAdminNumIds", appAdminNumIds);
            statMap.put("componentParams", componentParams);
        } catch (Exception e) {
            logger.error("BuriedPoint failed , serviceName[{} or {} or {}], map[{}]", BuriedPointBizEnum.ADMIN_ADD_EXTEND_APP, BuriedPointBizEnum.ADMIN_TRY_EXTEND_APP, BuriedPointBizEnum.USER_TRY_EXTEND_APP, statMap, e);
        }

        //只支持第三方免费扩展应用的添加
//        if (!Sets.newHashSet(TryStatusEnum.ADMIN_ADDED, TryStatusEnum.ADMIN_ABLE_TO_ADD).contains(tryStatusEnum)) {
//            throw new BizException(AjaxCode.BIZ_EXCEPTION, webAuthManager.isFsAdmin(user) ? "请联系纷享客服添加该应用": "请联系系统管理员添加该应用");
//        }

        //实现不同的动作
        Map<String, Object> componentsUrl;
        switch (tryStatusEnum) {
            case ADMIN_ADDED:
                return queryComponentsUrl(user, appId);
            case ADMIN_ABLE_TO_ADD: //公司添加
                //管理员添加时: 如果没有设置应用管理员, 则设置默认的应用管理员为当前系统管理员
                if (isFsAdmin && CollectionUtils.isEmpty(appAdminNumIds)) {
                    appAdminNumIds = Lists.newArrayList(user.getUserId());
                }

                //设置组件可见范围(管理员添加时, 如果可见为空, 则默认为自己)
                componentParams = getComponentParams(isFsAdmin, componentParams, user, appId);
                //获取该应用下组件的跳转地址
                componentsUrl = addAppByFsAdmin(user, appId, appAdminNumIds);
                //保存组件可见范围
                componentParams.forEach(
                        componentParam -> setViewForComponent(user, componentParam.getComponentId(), componentParam.getView())
                );

                statMap.put("action", BuriedPointBizEnum.ADMIN_ADD_EXTEND_APP.getAction());
                DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), statMap);
                return componentsUrl;
//            case ADMIN_THIRD_APP_ABLE_TO_TRY: //第三方应用试用,与官方应用试用后台的处理逻辑相同(前台会有所不同, 因为官方应用会自动授权)
//            case ADMIN_ABLE_TO_TRY: //公司试用
//
//                //管理员添加时: 如果没有设置应用管理员, 则设置默认的应用管理员为当前系统管理员
//                if (isFsAdmin && CollectionUtils.isEmpty(appAdminNumIds)) {
//                    appAdminNumIds = Lists.newArrayList(user.getUserId());
//                }
//                //设置组件可见范围可见范围(管理员添加时, 如果可见为空, 则默认为自己)
//                componentParams = getComponentParams(isFsAdmin, componentParams, user, appId);
//                //获取该应用下组件的跳转地址
//                //CRM特殊处理逻辑: CRM中添加应用管理员,但是不允许设置可见范围,可见范围的设置是有CRM来设置(CRM会监听应用管理员变更的MQ)
//                if (appId.equals(BizCommonUtils.getCrmAppId())) {
//                    for (final ComponentParam componentParam : componentParams) {
//                        final EmployeeRange view = componentParam.getView();
//                        if (!view.isEmpty()) {
//                            logger.warn("There should be no view for CRM, but view is: department[{}], member[{}]",
//                                    view.getDepartment(), view.getMember());
//                            view.getDepartment().clear();
//                            view.getMember().clear();
//                        }
//                    }
//                    //注意: CRM不可以设置应用管理员, 所以有以上
//                    appAdminNumIds.clear();
//                }
//
//                Map<String, EmployeeRange> componentViews = componentParams.stream()
//                        .collect(Collectors.toMap(ComponentParam::getComponentId, ComponentParam::getView));
//                final com.facishare.open.common.result.BaseResult<Void> applyResult =
//                        trialService.applyEnterpriseTrial(user, appId, appAdminNumIds, componentViews);
//                if (!applyResult.isSuccess()) {
//                    logger.warn("fail to trialService.applyEnterpriseTrial, user={}, appId={}, appAdmin={}, view={}, result={}",
//                            user, appId, appAdminNumIds, componentViews);
//                    throw new BizException(applyResult);
//                }
//                statMap.put("action", BuriedPointBizEnum.ADMIN_TRY_EXTEND_APP.getAction());
//                DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), statMap);
//                if (appId.equals(BizCommonUtils.getCrmAppId())) {
//                    //CRM必须在试用之后, 再单独设置应用管理员
//                    final BaseResult<Void> updateAppAdminIdsResult =
//                            openAppAdminService.updateAppAdminIds(user, appId, Lists.newArrayList(user.asStringUser()));
//                    if (!updateAppAdminIdsResult.isSuccess()) {
//                        logger.warn("fail to updateAppAdminIds, user[{}], appId[{}], appAdminIds[{}], result[{}]",
//                                user, appId, user, updateAppAdminIdsResult);
//                        throw new BizException(updateAppAdminIdsResult);
//                    }
//                }
//                return queryComponentsUrl(user, appId);

            case ADMIN_NOT_ABLE_TO_TRY:
                throw new BizException(AppCenterCodeEnum.TRIAL_CHANCE_OVER);
            case ADMIN_NOT_TRY_APP:
                throw new BizException(AppCenterCodeEnum.TRIAL_NOT_ALLOWED);
            case ADMIN_TRYING:
                throw new BizException(AppCenterCodeEnum.APP_AUTH_ALREADY_EXIST);
            case BOUGHT:
                throw new BizException(AppCenterCodeEnum.APP_AUTH_ALREADY_EXIST);
            case PURCHASE_EXPIRED:
                throw new BizException(AppCenterCodeEnum.TRIAL_CHANCE_OVER);
            case ENTERPRISE_TRIAL_EXPIRED:
                throw new BizException(AppCenterCodeEnum.TRIAL_CHANCE_OVER);
            case EMPLOYEE_ABLE_TO_ADD:
                throw new BizException(AppCenterCodeEnum.APP_IS_NOT_AUTHED);
//            case EMPLOYEE_ABLE_TO_TRY: //员工试用
//                final com.facishare.open.common.result.BaseResult<Void> applyTrialResult =
//                        trialService.applyEmployeeTrial(user, appId);
//                if (!applyTrialResult.isSuccess()) {
//                    logger.warn("fail to trialService.applyEmployeeTrial, user={}, appId={}, result={}",
//                            user, appId, applyTrialResult);
//                    throw new BizException(applyTrialResult);
//                }
//                statMap.put("action", BuriedPointBizEnum.USER_TRY_EXTEND_APP.getAction());
//                DataPersistor.asyncLog(BuriedPointBizEnum.serviceName(), statMap);
//                return queryComponentsUrl(user, appId);
            case EMPLOYEE_NOT_ABLE_TO_TRY:
                throw new BizException(AppCenterCodeEnum.TRIAL_CHANCE_OVER);
            case EMPLOYEE_NOT_TRY_APP:
                throw new BizException(AppCenterCodeEnum.TRIAL_NOT_ALLOWED);
            case EMPLOYEE_TRYING:
                return queryComponentsUrl(user, appId);
            case EMPLOYEE_THIRD_APP_NOT_ABLE_TO_TRY:
                throw new BizException(AppCenterCodeEnum.TRIAL_NOT_ALLOWED);
            case EMPLOYEE_DENIED_BY_ENTERPRISE_EXPIRED:
                throw new BizException(AppCenterCodeEnum.TRIAL_DENIED_BY_ENTERPRISE_EXPIRED);
            case EMPLOYEE_TRIAL_EXPIRED:
                throw new BizException(AppCenterCodeEnum.TRIAL_CHANCE_OVER);
            default:
                logger.error("there is no handler for enum={} of TryStatusEnum",
                        tryStatusEnum);
                throw new BizException(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    private List<ComponentParam> getComponentParams(boolean isFsAdmin, List<ComponentParam> componentParams, FsUserVO user, String appId) {
        boolean viewIsEmpty = false;
        if (isFsAdmin && (CollectionUtils.isEmpty(componentParams)
                || componentParams.stream().allMatch(componentParam -> componentParam.getView().isEmpty()))) {
            viewIsEmpty = true;
            final BaseResult<List<OpenAppComponentDO>> componentsResult =
                    openAppComponentService.queryAppComponentListByAppId(user, appId);
            if (!componentsResult.isSuccess()) {
                logger.warn("fail to queryAppComponentListByAppId, user={}, appId={}, result={}",
                        user, appId, componentsResult);
                throw new BizException(componentsResult);
            }
            EmployeeRange view = new EmployeeRange();
            view.getMember().add(user.getUserId());

            componentParams = new ArrayList<>();
            for (final OpenAppComponentDO componentDO : componentsResult.getResult()) {
                ComponentParam componentParam = new ComponentParam();
                componentParam.setComponentId(componentDO.getComponentId());
                componentParam.setView(view);
                componentParams.add(componentParam);
            }
        }

        /*
        * 付费助手类应用在试用时，全公司可见。
         */
        if (Arrays.stream(ConfigCenter.HELPER_APP_IDS.split(";")).anyMatch(s -> s.equals(appId))) {
            //目前web端无法设置可见范围，web默认全公司可见；app重构后可以设置可见范围
            if (viewIsEmpty) {
                for (ComponentParam componentParam : componentParams) {
                    EmployeeRange view = componentParam.getView();
                    view.getMember().clear();
                    view.setDepartment(Collections.singletonList(CommonConstant.COMPANY_DEPARTMENT_CODE));
                }
            }
        }

        return componentParams;
    }

    private void setViewForComponent(final FsUserVO user, final String componentId, final EmployeeRange view) {
        final BaseResult<OpenAppComponentDO> componentDoResult = openAppComponentService.loadOpenAppComponentById(user, componentId);
        if (!componentDoResult.isSuccess()) {
            logger.warn("fail to openAppComponentService.loadOpenAppComponentById, user={}, componentId={}",
                    user, componentId);
            throw new BizException(componentDoResult);
        }
        final OpenAppComponentDO componentDO = componentDoResult.getResult();
        final BaseResult<Void> componentViewResult = openFsUserAppViewService.saveFsUserAppViewList(user, componentId,
                AppComponentTypeEnum.getByCode(componentDO.getComponentType()),
                view.toAppView());
        if (!componentViewResult.isSuccess()) {
            logger.warn("fail to openFsUserAppViewService.saveFsUserAppViewList, user={}, componentId={}, view={} result={}",
                    user, componentDO.getComponentId(), view, componentViewResult);
            throw new BizException(componentViewResult);
        }
    }

    @Override
    public List<OpenAppDO> queryAppsByFsAdmin(FsUserVO user) {
        AppListResult appList = openFsUserBindAppService.queryAppListByFsEnterpriseAccount(user,
                user.getEnterpriseAccount());

        if (!appList.isSuccess()) {
            logger.warn("failed to call queryAppListByFsEnterpriseAccount, user={}, result={}", user, appList);
            throw new BizException(appList);
        }

        if (null == appList.getResult()) { //确保返回不会是null,不方便后续处理
            return new ArrayList<>();
        }

        List<OpenAppDO> appDOs = new ArrayList<>();
        final Set<String> enterpriseAppIds = queryAppOfPurchaseOrEnterpriseTrial(user.getEnterpriseAccount());
        for (final OpenAppDO appDO : appList.getResult()) {
            if (appDO.getPayType() == PayTypeEnum.CHARGE.getPayType()                         //付费
                    && appDO.getAppType() == DEV_APP.value()            //扩展应用
                    && !enterpriseAppIds.contains(appDO.getAppId())) { //如果扩展应用中的收费应用, 且不是公司试用或购买, 则不返回
                continue;
            }
            appDOs.add(appDO);
        }

        return appDOs;
    }

    private Set<String> queryAppOfPurchaseOrEnterpriseTrial(final String fsEa) {
        final com.facishare.open.common.result.BaseResult<List<QuotaRecordVo>> quotaRecordsResult =
                quotaService.queryQuotaRecords(fsEa, null);
        if (!quotaRecordsResult.isSuccess()) {
            logger.warn("fail to call quotaService.queryQuotaRecords, fsEa={}, result={}",
                    fsEa, quotaRecordsResult);
            throw new BizException(quotaRecordsResult);
        }
        return quotaRecordsResult.getResult().stream()
                .map(QuotaRecordVo::getAppId)
                .collect(Collectors.toSet());
    }

    @Override
    public List<OpenAppDO> queryAppsByFsAppAdmin(FsUserVO user) {

        //3.获取企业已经授权的应用（应用list根据应用创建的时间倒序）
        AppListResult appListResult = openFsUserBindAppService.queryAppListByFsEnterpriseAccount(user,
                user.getEnterpriseAccount());

        if (!appListResult.isSuccess()) {
            throw new BizException(appListResult);
        }

        //4.获取应用管理员为user的应用列表
        BaseResult<List<String>> appsByAppAdminResult = openAppAdminService.queryAppIdList(user.asStringUser());
        if (!appsByAppAdminResult.isSuccess()) {
            throw new BizException(appsByAppAdminResult);
        }

        //5. 获取已授权并处于已启动状态(lambo, albert)
        Set<String> validAppIds = new HashSet<>(appsByAppAdminResult.getResult());

        return appListResult.getResult().stream()
                .filter(appDO -> validAppIds.contains(appDO.getAppId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<OpenAppDO> queryServicesByFsAppAdmin(FsUserVO user, List<Integer> appTypes) {
        BaseResult<List<OpenAppDO>> baseResult = openAppAdminService.queryServicesByServiceAdmin(user, appTypes);
        if (!baseResult.isSuccess()) {
            logger.warn("fail to openAppAdminService.queryServicesByServiceAdmin, user={}, appTypes={}, result={}",
                    user, appTypes, baseResult);
            throw new BizException(baseResult);
        }
        return baseResult.getResult();
    }

    @Override
    public boolean hasServiceDashboard(FsUserVO fsUserVO) {
        List<OpenAppDO> serviceList = queryServicesByFsAppAdmin(fsUserVO, OpenAppUtils.getAllAppTypesForService());
        if (CollectionUtils.isEmpty(serviceList)) {
            return false;
        }
        serviceList = serviceList.stream().filter((appDO) -> (appDO.getBindStatus() == CommonConstant.APP_BIND_STATUS_ON
                && !nearbyCustomersAppId.equals(appDO.getAppId()))).
                collect(Collectors.toList());
        return serviceList.size() > 0;
    }

    @Override
    public List<OpenAppDO> queryAppListByAppName(FsUserVO user, String appName, boolean isFsAdmin, boolean isAppAdmin) {
        List<OpenAppDO> appDOs = queryAppAdminManageList(user, isFsAdmin, isAppAdmin);
        if (StringUtils.isEmpty(appName)) {
            return  appDOs;
        } else {
            String appNameLowCase = appName.toLowerCase();        //忽略大小写，这里都转成小写来比较
            return appDOs.stream().filter(appDO -> appDO.getAppName().toLowerCase().indexOf(appNameLowCase) > -1).
                    collect(Collectors.toList());
        }
    }

    /**
     * 查询管理员可管理的应用列表
     * @param user
     * @param isFsAdmin
     * @param isAppAdmin
     */
    private List<OpenAppDO> queryAppAdminManageList(FsUserVO user, boolean isFsAdmin, boolean isAppAdmin) {
        List<OpenAppDO> appDOs;
        if (isFsAdmin) {
            appDOs = queryAppsByFsAdmin(user);
            // 过滤掉服务号应用
            appDOs = appDOs.stream().filter(openAppDO -> IntStream.of(BASE_APP.value(), DEV_APP.value(),
                    CUSTOM_APP.value()).anyMatch(t -> t == openAppDO.getAppType())).collect(Collectors.toList());

        } else if (isAppAdmin) {
            appDOs = queryAppsByFsAppAdmin(user);
        } else {
            appDOs = new ArrayList<>();
        }

        // 过滤强制下线应用
        if (!CollectionUtils.isEmpty(appDOs)) {
            appDOs = appDOs.stream()
                    .filter(openAppDO -> !ConfigCenter.FORCE_OFFLINE_APP_IDS_FOR_APP_MANAGE.contains(openAppDO.getAppId()))
                    .collect(Collectors.toList());
        }

        // 过滤掉钉钉版黑名单应用
        appDOs = dingTalkAppManager.filterAppDOForDingTalkVersion(user.getEa(), appDOs);

        // 过滤黑名单应用
        appDOs = filterOfflineAppWithBlackEaList(user.getEa(), appDOs);

        Iterator<OpenAppDO> iterator = appDOs.iterator();
        OpenAppDO openAppDO;
        while (iterator.hasNext()){
            openAppDO = iterator.next();
            if(BizCommonUtils.isUnableToManage(openAppDO.getAppId())) {  //排除不能被管理的，比如：企业互联中"纷享百川"的应用 和 "微信百川"的应用
                iterator.remove();
            }
        }

//        // 过滤云之家企业非CRM应用
//        boolean isYunZhiJiaEa = isYunZhiJiaEa(user.getEnterpriseAccount());
//        logger.info("isYunZhiJiaEa[{}], ea[{}]", isYunZhiJiaEa, user.getEa());
//        if (isYunZhiJiaEa(user.getEnterpriseAccount())) {
//            appDOs = appDOs.stream().filter(appDO -> Objects.equals(ConfigCenter.CRM_APP_ID, appDO.getAppId())).collect(Collectors.toList());
//        }

        return appDOs;
    }

    private List<OpenAppDO> filterOfflineAppWithBlackEaList(String ea, List<OpenAppDO> appDOs) {
        Map<String, List<String>> forceOfflineAppIdToEaClackList = ConfigCenter.FORCE_OFFLINE_APP_ID_TO_EA_BLACK_LIST;
        appDOs = appDOs.stream()
                .filter(appDO -> {
                    if (forceOfflineAppIdToEaClackList.containsKey(appDO.getAppId())) {
                        List<String> blackEaList = forceOfflineAppIdToEaClackList.getOrDefault(appDO.getAppId(), Lists.newArrayList());
                        return !blackEaList.contains(ea);
                    } else {
                        return true;
                    }
                })
                .collect(Collectors.toList());
        return appDOs;
    }

    @Override
    public Pager<OpenAppDO> queryAppListByAdmin(FsUserVO user, Integer currentPage, Integer pageSize, boolean isFsAdmin, boolean isAppAdmin) {
        List<OpenAppDO> dataList = queryAppAdminManageList(user, isFsAdmin, isAppAdmin);
        Pager<OpenAppDO> pager = new Pager<>();
        pager.setCurrentPage(currentPage);
        pager.setPageSize(pageSize);
        pager.setRecordSize(dataList.size());

        if (pager.getPageTotal() >= currentPage) {
            List<OpenAppDO> data = pager.getData();
            int limit = pager.offset() + pageSize;
            for (int i = pager.offset(); i < limit; i++) {
                if (i < dataList.size()) {
                    data.add(dataList.get(i));
                }
            }
        }
        return pager;
    }

    @Override
    public List<QueryAppsVO> queryApps(FsUserVO user, String lang, List<Integer> appTypes) {
        List<QueryAppsVO> result = new ArrayList<>();

        //查所有应用
        List<OpenAppDO> openAppDOS = queryAppAdminManageList(user, true, false);

        //过滤掉不要的类型
        openAppDOS = openAppDOS.stream().filter(app -> appTypes.contains(app.getAppType())).collect(Collectors.toList());

        //如果没有平台的权限，只能看自己是应用管理员的
        openAppDOS = filterAppsByPermission(user, openAppDOS, appTypes);
        //自建应用按照创建时间排序appTypes=[1]（升序）
        if (appTypes.size() == 1 & appTypes.contains(AppCenterEnum.AppType.CUSTOM_APP.value())) {
            Collections.sort(openAppDOS, (OpenAppDO o1, OpenAppDO o2) -> {
                long a = null == o1.getGmtCreate() ? 0 : o1.getGmtCreate().getTime();
                long b = null == o2.getGmtCreate() ? 0 : o2.getGmtCreate().getTime();
                return a == b ? 0 : (a > b ? 1 : -1);
            });
        }//基础应用和扩展应用按照指定字段固定排序（升序）
        else if (appTypes.size() == 2 & appTypes.contains(AppCenterEnum.AppType.BASE_APP.value()) & appTypes.contains(AppCenterEnum.AppType.DEV_APP.value())) {
            Collections.sort(openAppDOS, (OpenAppDO o1, OpenAppDO o2) -> {
                OpenAppProperties openAppProperties1 = OpenAppProperties.fromJson(o1.getProperties());
                OpenAppProperties openAppProperties2 = OpenAppProperties.fromJson(o2.getProperties());
                if (openAppProperties1 == null) {
                    openAppProperties1 = new OpenAppProperties();
                }
                if (openAppProperties2 == null) {
                    openAppProperties2 = new OpenAppProperties();
                }
                Integer a = null == openAppProperties1.getOrderNumber() ? 0 : openAppProperties1.getOrderNumber();
                Integer b = null == openAppProperties2.getOrderNumber() ? 0 : openAppProperties2.getOrderNumber();
                return a == b ? 0 : (a > b ? 1 : -1);
            });
        }

        //多语言
        int ei = eieaConverter.enterpriseAccountToId(user.getEa());
        openAppDOS = I18NUtils.modifyOpenAppDOByLang(ei, openAppDOS, lang);

        result =  openAppDOS.stream().map(app -> BeanUtil.copyProperties(QueryAppsVO.class, app)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        //appLogoUrl
        result.forEach(queryAppsVO -> {
            queryAppsVO.setAppLogoUrl(appIconManager.queryAppIconUrl(queryAppsVO.getAppId(), IconType.WEB));
        });

        //付费应用
        addPayStatus(user, result);

        return result;
    }

    /**
     * 付费应用，加上payStatus
     */
    private void addPayStatus(FsUserVO user, List<QueryAppsVO> queryAppsVOS) {
        if (CollectionUtils.isEmpty(queryAppsVOS)) {
            return;
        }
        for (QueryAppsVO queryAppsVO : queryAppsVOS) {
            if (!Objects.equals(queryAppsVO.getPayType(), PayTypeEnum.CHARGE.getPayType())) {
                continue;
            }
            PayStatus payStatusForShow = quotaManager.getPayStatus(user, queryAppsVO.getAppId());
            queryAppsVO.setPayStatus(payStatusForShow.getCode());
        }
    }

    /**
     * 过滤掉没有权限的应用
     *     如果没有平台的权限，就只能返回自己是应用管理员的应用列表
     */
    private List<OpenAppDO> filterAppsByPermission(FsUserVO user, List<OpenAppDO> openAppDOS, List<Integer> appTypes) {
        //查权限列表
        List<String> functionCodes = webAuthManager.getSystemFunctionCodes(user, true);

        if (functionCodes.contains(SystemFunctionCodeEnum.APP_BASE_MANAGE.getCode())
            && functionCodes.contains(SystemFunctionCodeEnum.APP_CUSTOM_MANAGE.getCode())) {
            return openAppDOS;
        }

        List<Integer> needDeleteTypes = new ArrayList<>();
        if (appTypes.contains(AppCenterEnum.AppType.BASE_APP.value()) || appTypes.contains(AppCenterEnum.AppType.DEV_APP.value())) {
            if (!functionCodes.contains(SystemFunctionCodeEnum.APP_BASE_MANAGE.getCode())) {
                //过滤掉自己不是应用管理员的'基础应用'和'扩展应用'
                needDeleteTypes.add(AppCenterEnum.AppType.BASE_APP.value());
                needDeleteTypes.add(AppCenterEnum.AppType.DEV_APP.value());
            }
        }

        if (appTypes.contains(AppCenterEnum.AppType.CUSTOM_APP.value()) && !functionCodes.contains(SystemFunctionCodeEnum.APP_CUSTOM_MANAGE.getCode())) {
            //过滤掉自己不是应用管理员的'自建应用'
            needDeleteTypes.add(AppCenterEnum.AppType.CUSTOM_APP.value());
        }

        if (CollectionUtils.isEmpty(needDeleteTypes)) {
            return openAppDOS;
        }

        //查询用户作为应用管理员的所有应用id列表(不包括服务号)
        BaseResult<List<String>> listBaseResult = openAppAdminService.queryAppIdList(user.asStringUser());
        if (!listBaseResult.isSuccess()) {
            logger.warn("openAppAdminService.queryAppIdList failed, fsUserId[{}]", user.asStringUser());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询用户作为应用管理员的所有应用id列表失败"); // ignoreI18n
        }
        List<String> appIds = listBaseResult.getResult();
        return openAppDOS.stream().filter(app -> !needDeleteTypes.contains(app.getAppType()) || appIds.contains(app.getAppId())).collect(Collectors.toList());
    }

//    /**
//     * 判断是否去之家企业
//     */
//    private boolean isYunZhiJiaEa(String ea) {
//        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
//        arg.setEnterpriseAccount(ea);
//        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
//        return result.getEnterpriseData().getSource() == 7;
//    }

    @Override
    public List<OpenAppDO> queryServiceListByAppName(FsUserVO user, String appName, boolean isFsAdmin, boolean isServiceAdmin,
                                                     boolean isUpstreamLinkAdmin, boolean isLinkServiceAdmin) {
        //分开查询内部服务号和互联服务号
        List<OpenAppDO> appDOs = Lists.newArrayList();
        List<OpenAppDO> innerAppDOs = queryServiceAdminManageList(user, isFsAdmin, isServiceAdmin,
                OpenAppUtils.getInnerAndOutAppTypesForService());//内部服务号+外联服务号
        List<OpenAppDO> linkAppDOs = queryServiceAdminManageList(user, isUpstreamLinkAdmin, isLinkServiceAdmin,
                OpenAppUtils.getAppTypesForLinkService());
        appDOs.addAll(innerAppDOs);
        appDOs.addAll(linkAppDOs);
        //搜索所有服务号并根据bindTime排序
        if(CollectionUtils.isEmpty(appDOs)){
            return  appDOs;
        }
        if (StringUtils.isEmpty(appName)) {
            return  appDOs;
        } else {
            String appNameLowCase = appName.toLowerCase();        //忽略大小写，这里都转成小写来比较
            return appDOs.stream().filter(appDO -> appDO.getAppName().toLowerCase().indexOf(appNameLowCase) > -1).
                    collect(Collectors.toList());
        }
    }

    /**
     * 查询管理员可管理的服务号列表
     * @param user
     * @param isGetAll
     * @param isServiceAdmin
     * @param appTypes
     */
    private List<OpenAppDO> queryServiceAdminManageList(FsUserVO user, boolean isGetAll, boolean isServiceAdmin, List<Integer> appTypes) {
        List<OpenAppDO> appDOs;
        if (isGetAll) {
            AppListResult result = openAppAdminService.queryServicesByAdminAndType(user, appTypes);
            if (!result.isSuccess()) {
                logger.warn("failed to call queryAppListByFsEnterpriseAccount, user={}, result={}", user, result);
                throw new BizException(result);
            }
            appDOs = result.getResult();
        } else if (isServiceAdmin) {
            appDOs = queryServicesByFsAppAdmin(user, appTypes);
        } else {
            appDOs = new ArrayList<>();
        }
        return appDOs;
    }

    @Override
    public Pager<OpenAppDO> queryServiceListByAdmin(FsUserVO user, Integer currentPage, Integer pageSize, boolean isGetAll,
                                                    boolean isServiceAdmin, int serviceType) {
        List<Integer> appTypes;
        if (Objects.equals(serviceType, AppCenterEnum.AppType.LINK_SERVICE.value())) {
            appTypes = OpenAppUtils.getAppTypesForLinkService();
        } else if (Objects.equals(serviceType, AppCenterEnum.AppType.OUT_SERVICE_APP.value())) {
            appTypes = OpenAppUtils.getOutAppTypesForService();
        } else {
            //灰度
            if (GrayRelease.isAllow("app-center", "OnlyInnerAppType", user.getEa())) {
                appTypes = OpenAppUtils.getInnerAppTypesForService();
            } else {
                appTypes = OpenAppUtils.getInnerAndOutAppTypesForService();
            }
        }
        List<OpenAppDO> dataList = queryServiceAdminManageList(user, isGetAll, isServiceAdmin, appTypes);
        Pager<OpenAppDO> pager = new Pager<>();
        pager.setCurrentPage(currentPage);
        pager.setPageSize(pageSize);
        pager.setRecordSize(dataList.size());

        if (pager.getPageTotal() >= currentPage) {
            List<OpenAppDO> data = pager.getData();
            int limit = pager.offset() + pageSize;
            for (int i = pager.offset(); i < limit; i++) {
                if (i < dataList.size()) {
                    data.add(dataList.get(i));
                }
            }
        }
        return pager;
    }

    @Override
    public List<OpenAppVO> queryServicesByAdminAndType(FsUserVO user, List<Integer> appTypes){
        BaseResult<List<OpenAppDO>> baseResult = openAppAdminService.queryServicesByAdminAndType(user, appTypes);
        if (!baseResult.isSuccess()) {
            throw new BizException(baseResult);
        }
        return queryOpenAppVOByDO(baseResult.getResult());
    }

    /**
     * OpenAppDO转VO
     * @param openAppDOList
     * @return
     */
    private List<OpenAppVO> queryOpenAppVOByDO(List<OpenAppDO> openAppDOList){
        List<OpenAppVO> openAppVOS = OpenAppUtils.getOpenAppVOByDO(openAppDOList);
        if(!CollectionUtils.isEmpty(openAppVOS)){
            List<String> appIds = openAppVOS.stream().map(OpenAppVO::getAppId).collect(Collectors.toList());
            Map<String, String> appIdIconUrlMap = appManager.batchQueryAppIconUrl(appIds, IconType.WEB);
            for (OpenAppVO openAppVO : openAppVOS) {
                String iconUrl = appIdIconUrlMap.get(openAppVO.getAppId());
                if (StringUtils.isNotBlank(iconUrl)) {
                    openAppVO.setLogo(iconUrl);
                }
            }
        }
        return openAppVOS;
    }
}
