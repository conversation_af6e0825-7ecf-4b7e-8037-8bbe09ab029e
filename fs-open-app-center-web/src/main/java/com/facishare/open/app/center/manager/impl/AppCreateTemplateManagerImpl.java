package com.facishare.open.app.center.manager.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.*;
import com.facishare.open.app.center.api.model.enums.*;
import com.facishare.open.app.center.api.model.property.AppCreateTemplateProperties;
import com.facishare.open.app.center.api.model.vo.custom.create.template.AppCreateTemplateExecuteJsonVO;
import com.facishare.open.app.center.api.model.vo.custom.create.template.CustomMenuTemplateJsonVO;
import com.facishare.open.app.center.api.service.OpenAppCreateTemplateCategoryService;
import com.facishare.open.app.center.api.service.OpenAppCreateTemplateService;
import com.facishare.open.app.center.api.service.OpenDemoAppService;
import com.facishare.open.app.center.cons.CenterConstants;
import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.center.model.*;
import com.facishare.open.app.center.mq.item.AppOrServiceCreateItem;
import com.facishare.open.app.center.utils.CommonThreadPoolUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.I18NUtils;
import com.facishare.open.autoreplymsg.model.CreateDefaultReplyVO;
import com.facishare.open.autoreplymsg.model.CreateKeywordReplyVO;
import com.facishare.open.autoreplymsg.model.KeywordTypeInfo;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.autoreplymsg.result.CreateDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.CreateKeywordReplyResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.autoreplymsg.service.MsgDefaultReplyService;
import com.facishare.open.autoreplymsg.service.MsgKeywordReplyService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.custom.menu.api.enums.CustomMenuTypeEnum;
import com.facishare.open.material.api.enums.ArticleCategorySwitchEnum;
import com.facishare.open.material.api.enums.ImageTextTypeEnum;
import com.facishare.open.material.api.model.vo.ImageTextParam;
import com.facishare.open.material.api.model.vo.MaterialAccessPermissionVO;
import com.facishare.open.material.api.service.ArticleCategorySwitchService;
import com.facishare.open.material.api.service.MaterialAccessPermissionService;
import com.facishare.open.msg.model.EaConnAppSessionConfig;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.service.MsgSessionService;
import com.facishare.open.operating.center.api.service.OperatingAppMessageService;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.enterpriserelation.arg.BatchCreateCustomerServiceAuthArg;
import com.fxiaoke.enterpriserelation.common.HeaderObj;
import com.fxiaoke.enterpriserelation.service.CustomerServiceService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * impl.
 * Created by zenglb on 2016/3/22.
 */
@Service
public class AppCreateTemplateManagerImpl implements AppCreateTemplateManager {

    private Logger logger = LoggerFactory.getLogger(getClass());

    //mq日志分开打印
    private Logger MQ_LOG = LoggerFactory.getLogger("CENTER_MQ_LOG");

    @Resource
    private AppManager appManager;
    @Resource
    private OpenAppCreateTemplateService openAppCreateTemplateService;
    @Resource
    private OpenAppCreateTemplateCategoryService openAppCreateTemplateCategoryService;
    @Resource
    private CustomMenuManager customMenuManager;
    @Resource
    private MaterialManager materialManager;
    @Resource
    private MsgAutoReplyService msgAutoReplyService;
    @Resource
    private MsgDefaultReplyService msgDefaultReplyService;
    @Resource
    private MsgKeywordReplyService msgKeywordReplyService;
    @Resource
    private OpenDemoAppService openDemoAppService;
    @Resource
    private WebAuthManager webAuthManager;
    @Resource
    private ServiceNumberManager serviceNumberManager;
    @Resource
    private MaterialAccessPermissionService materialAccessPermissionService;
    @Resource
    private ServicePromotionManager servicePromotionManager;
    @Resource
    private ArticleCategorySwitchService articleCategorySwitchService;
    @Resource
    private MsgSessionService msgSessionService;
    @Autowired
    private CustomerServiceService customerServiceService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private OperatingAppMessageService operatingAppMessageService;

    @Override
    public OpenAppCreateTemplateDO getTemplateById(String templateId) {
        BaseResult<OpenAppCreateTemplateDO> openAppCreateTemplateDOResult = openAppCreateTemplateService.loadById(templateId);
        if (!openAppCreateTemplateDOResult.isSuccess()) {
            logger.error("openAppCreateTemplateService.loadById failed. templateId[{}] result[{}]", templateId, openAppCreateTemplateDOResult);
            throw new BizException(AjaxCode.PARAM_ERROR, openAppCreateTemplateDOResult, "应用创建模板不存在."); // ignoreI18n
        }
        return openAppCreateTemplateDOResult.getResult();
    }

    @Override
    public Map<String, Object> createCustomAppByForm(FsUserVO user, String lang, CustomAppCreateForm form, AppCenterEnum.AppType appType) {
        Map<String, Object> result = new HashMap<>();

        OpenAppCreateTemplateDO openAppCreateTemplateDO = this.getTemplateById(form.getTemplateId());
        String appId = createCustomAppByTemplate(user, lang, form, openAppCreateTemplateDO, appType);
        result.put("appId", appId);
        //新增服务号的同时,删除服务号推广企业服务号数缓存
        if (Objects.equals(appType, AppCenterEnum.AppType.SERVICE)) {
            servicePromotionManager.delEaServiceCountCache(user, appId);
        }
        return result;
    }

    private String createCustomAppByTemplate(FsUserVO user, String lang, CustomAppCreateForm form,
                                             OpenAppCreateTemplateDO openAppCreateTemplateDO,
                                             AppCenterEnum.AppType appType) {
        AppCreateTemplateExecuteJsonVO templateExecuteJsonVO =
                JsonKit.json2object(openAppCreateTemplateDO.getExecuteJson(), AppCreateTemplateExecuteJsonVO.class);
        if (null == form.getOpenCustomMenu() && null != templateExecuteJsonVO && null != templateExecuteJsonVO.getOpenCustomMenu()) {
            form.setOpenCustomMenu(templateExecuteJsonVO.getOpenCustomMenu());
        }
        if (null == form.getOpenAutoReply() && null != templateExecuteJsonVO && null != templateExecuteJsonVO.getOpenAutoReply()) {
            form.setOpenAutoReply(templateExecuteJsonVO.getOpenAutoReply());
        }

        //创建应用
        AppCreateForm appCreateForm = buildAppCreateForm(form, appType, templateExecuteJsonVO);
        appManager.createCustomApp(user, appCreateForm, null, appType);
        String appId = appCreateForm.getAppId();

        //记录模版使用次数
        incTemplateCreateCnt(openAppCreateTemplateDO.getTemplateId());

        if (AppCenterEnum.AppType.LINK_SERVICE == appType) {
            String upstreamEa = user.getEnterpriseAccount();
            //创建上游服务号工作台session
            createServiceWorkBenchSession(user, new OpenAppDO(appId, form.getAppName()),
                    Lists.newArrayList(form.getAppAdmins()), CommonConstant.IS_LINK_SERVICE);

            //保存下游可见企业范围
            List<String> linkServiceDownstreamEas = form.getLinkServiceDownstreamEas();
            if (!CollectionUtils.isEmpty(linkServiceDownstreamEas)) {
                HeaderObj headerObj = HeaderObj.newInstance(upstreamEa, appId, null, null);
                customerServiceService.batchCreateCustomerServiceAuth(headerObj, BatchCreateCustomerServiceAuthArg.builder().upstreamEa(upstreamEa).customerServiceId(appId).downstreamEaAddList(linkServiceDownstreamEas).build());
            }

            //保存下游服务号Session
            createLinkServiceDownstreamSession(appId, upstreamEa, appCreateForm);

            final Map<String, String> materialIdMap = new HashMap<>();
            //初始化素材.
            initMaterial(user, appCreateForm.getAppName(), appId, templateExecuteJsonVO, materialIdMap);
            //初始化自动回复.
            initAutoReply(user, form, appId, templateExecuteJsonVO, materialIdMap);
            //初始化自定义菜单.
            initCustomMenu(user, form, appId, templateExecuteJsonVO, materialIdMap);
            //初始化移动多客服功能
            initServiceNumberSwitch(user, lang, appId, templateExecuteJsonVO);
            //开启"审批单"功能
            initApprovalSwitch(user, appId);
        }

        if (AppCenterEnum.AppType.SERVICE == appType) {
            final Map<String, String> materialIdMap = new HashMap<>();
            // 初始化素材.
            initMaterial(user, appCreateForm.getAppName(), appId, templateExecuteJsonVO, materialIdMap);
            //初始化自动回复.
            initAutoReply(user, form, appId, templateExecuteJsonVO, materialIdMap);
            //初始化自定义菜单.
            initCustomMenu(user, form, appId, templateExecuteJsonVO, materialIdMap);
            //创建服务号工作台Session
            createServiceWorkBenchSession(user, new OpenAppDO(appId, form.getAppName()),
                    Lists.newArrayList(form.getAppAdmins()), CommonConstant.IS_SERVICE);
            //初始化移动多客服功能
            initServiceNumberSwitch(user, lang, appId, templateExecuteJsonVO);
            //初始化问卷
            initQuestionnaire(user, appId, templateExecuteJsonVO);
            //初始化服务工单
            initWorkOrderPaas(user, appId, templateExecuteJsonVO);
            // 初始化服务号安全设置(默认为内部)
            initMaterialAccessPermission(user, appId);
            //为部分模版创建的服务号开启文章分类开关
            setDefaultCategorySwitchOn(user, form, appId);
        }

        // 发送服务号创建MQ消息
        appOrServiceCreateToMq(appCreateForm.getAppName(), appType, user, appId);

        // 保存进埋点日志,同步到神策系统
        if (appType == AppCenterEnum.AppType.LINK_SERVICE || appType == AppCenterEnum.AppType.SERVICE ) {
            dpLogServiceCreateByTemplate(user, appType, appId);
        }
        return appId;
    }

    private void createLinkServiceDownstreamSession(String appId, String upstreamEa, AppCreateForm appCreateForm) {
        EaConnAppSessionConfig eaConnAppSessionConfig  = new EaConnAppSessionConfig();
        eaConnAppSessionConfig.setAppId(appId);
        eaConnAppSessionConfig.setName(appCreateForm.getAppName());
        eaConnAppSessionConfig.setPortraitPath(appCreateForm.getAppLogo());
        eaConnAppSessionConfig.setDescription(appCreateForm.getAppDesc());
        eaConnAppSessionConfig.setUpstreamEnterprise(upstreamEa);
        // 因为上游用户跟下游用户使用的都是互联服务号下游Session，所以将上游企业也设置到Session的下游列表
        List<String> downstreamEnterpriseList = Lists.newArrayList(upstreamEa);
        downstreamEnterpriseList.addAll(appCreateForm.getLinkServiceDownstreamEas());
        eaConnAppSessionConfig.setDownstreamEnterpriseList(downstreamEnterpriseList);
        eaConnAppSessionConfig.setCustomMenu(null); // 菜单设置放到后面保存菜单去更新Session
        eaConnAppSessionConfig.setShowBlockMsgSwitch(true);
        eaConnAppSessionConfig.setDisable(false);
        MsgBaseResult updateEaConnAppSessionConfigResult = msgSessionService.updateEaConnAppSessionConfig(eaConnAppSessionConfig);
        if (!updateEaConnAppSessionConfigResult.isSuccess()) {
            logger.error("msgSessionService.updateEaConnAppSessionConfig failed! config[{}], result[{}]",
                    eaConnAppSessionConfig, updateEaConnAppSessionConfigResult);
            throw new BizException(updateEaConnAppSessionConfigResult);
        }
    }

    private AppCreateForm buildAppCreateForm(CustomAppCreateForm form,
                                             AppCenterEnum.AppType appType,
                                             AppCreateTemplateExecuteJsonVO templateExecuteJsonVO) {
        AppCreateForm appCreateForm = new AppCreateForm();
        appCreateForm.setTemplateId(form.getTemplateId());
        String appName = form.getAppName();
        String appDesc = form.getAppDesc();
        String appLogo = form.getAppLogo();
        appCreateForm.setAppName(appName);
        appCreateForm.setAppDesc(appDesc);
        appCreateForm.setAppLogo(appLogo);
        appCreateForm.setAppAdmins(form.getAppAdmins());
        appCreateForm.setLinkServiceDownstreamEas(form.getLinkServiceDownstreamEas());
        if(Objects.equals(AppCenterEnum.AppType.CUSTOM_APP, appType)) {
            if (StringUtils.isNotBlank(form.getAppLoginUrl())) {
                CustomComponentVO app = new CustomComponentVO();
                app.setComponentName(appName);
                app.setComponentDesc(appDesc);
                app.setLoginUrl(form.getAppLoginUrl());
                app.setComponentLogo(appLogo);
                if(null != templateExecuteJsonVO && StringUtils.isNotBlank(templateExecuteJsonVO.getAppComponentLogo())){
                    app.setComponentLogo(templateExecuteJsonVO.getAppComponentLogo());
                }
                app.setComponentType(AppComponentTypeEnum.APP.getType());
                app.setView(form.getAppView());
                appCreateForm.getAppComponents().add(app);
            }

            if (StringUtils.isNotBlank(form.getWebLoginUrl())) {
                CustomComponentVO web = new CustomComponentVO();
                web.setComponentName(appName);
                web.setComponentDesc(appDesc);
                web.setLoginUrl(form.getWebLoginUrl());
                web.setComponentType(AppComponentTypeEnum.WEB.getType());
                web.setComponentLogo(appLogo);
                web.setView(form.getAppView());
                appCreateForm.getAppComponents().add(web);
            }
        } else if (Objects.equals(AppCenterEnum.AppType.SERVICE, appType)) {
            CustomComponentVO service = new CustomComponentVO();
            service.setComponentLogo(appLogo);
            service.setComponentName(appName);
            service.setComponentDesc(appDesc);
            service.setLoginUrl(CommonConstant.SERVICE_LOGIN_URL);
            service.setComponentType(AppComponentTypeEnum.SERVICE.getType());
            service.setView(form.getServiceView());
            appCreateForm.getAppComponents().add(service);
        } else if (Objects.equals(AppCenterEnum.AppType.LINK_SERVICE, appType)) {
            CustomComponentVO service = new CustomComponentVO();
            service.setComponentLogo(appLogo);
            service.setComponentName(appName);
            service.setComponentDesc(appDesc);
            service.setLoginUrl(CommonConstant.SERVICE_LOGIN_URL);
            service.setComponentType(AppComponentTypeEnum.LINK_SERVICE.getType());
            service.setView(form.getServiceView());
            appCreateForm.getAppComponents().add(service);
        }
        return appCreateForm;
    }

    private void dpLogServiceCreateByTemplate(FsUserVO user, AppCenterEnum.AppType appType, String appId) {
        try {
            Map<String, Object> map = new HashMap<>(10);
            map.put("opUser", user.asStringUser());
            map.put("EI", eieaConverter.enterpriseAccountToId(user.getEnterpriseAccount()));
            map.put("senderEa", user.getEnterpriseAccount());
            map.put("appId", appId);
            map.put("appType", appType.value());
            DataPersistor.asyncLog(BuriedPointBizEnum.serviceCreate(), map);
        } catch (Exception e) {
            logger.error("dpLogServiceCreateByTemplate failed , fsUserVO[{}],", user.asStringUser(), e);
        }
    }

    private void setDefaultCategorySwitchOn(FsUserVO user, CustomAppCreateForm form, String appId) {
        if (!StringUtils.isEmpty(ConfigCenter.ARTICLE_CATEGORY_ON_FOR_TEMPLATE) && ConfigCenter
                .ARTICLE_CATEGORY_ON_FOR_TEMPLATE.contains(form.getTemplateId())){
            BaseResult<Boolean> booleanBaseResult = articleCategorySwitchService.saveCategorySwitch(user.getEnterpriseAccount
                    (), appId, ArticleCategorySwitchEnum.ON);
            if (!booleanBaseResult.isSuccess()){
                logger.error("saveCategorySwitch failed. fsEa[{}], appId[{}]", user.getEnterpriseAccount(),
                        appId);
            }
        }
    }

    private void incTemplateCreateCnt(String openAppCreateTemplateId) {
        BaseResult<Integer> incrementBaseResult = openAppCreateTemplateService.incrementServiceNum(openAppCreateTemplateId);
        if (!incrementBaseResult.isSuccess()){
            logger.warn("incrementBaseResult error. openAppCreateTemplateId[{}], incrementBaseResult[{}]",
                    openAppCreateTemplateId, incrementBaseResult);
        }
    }

    private void initMaterialAccessPermission(FsUserVO user, String appId) {
        MaterialAccessPermissionVO materialAccessPermissionVO = new MaterialAccessPermissionVO();
        materialAccessPermissionVO.setAppId(appId);
        materialAccessPermissionVO.setFsEa(user.getEnterpriseAccount());
        materialAccessPermissionVO.setPermission(com.facishare.open.material.api.enums.PermissionEnum.INNER.getCode());
        materialAccessPermissionVO.setModifiedUser(FsUserVO.toFsUserString(user));
        materialAccessPermissionService.saveOrModifyPermission(materialAccessPermissionVO);
    }

    /**
     * 创建自建应用，需要新增服务号工作标题，管理员描述。客服描述字段。推送给企信
     * @param fsAdminUser 用户
     * @param openAppDO 应用信息
     * @param appAdminIds 应用管理员
     * @param serviceType  与serviceNumberManager.modifyServiceWorkBeanchSession 一致
     * @return boolean
     */
    private void createServiceWorkBenchSession(FsUserVO fsAdminUser, OpenAppDO openAppDO, List<Integer> appAdminIds, int serviceType) {
        //设置企信会话
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setAdmins(appAdminIds);
        // 创建服务号时默认设置管理员为移动客服人员
        platformMetaSessionVO.setCustomers(appAdminIds);
        //工作台session名称
        platformMetaSessionVO.setCustomerSessionName(openAppDO.getServiceName() + CenterConstants.SERVICE_NUMBER_MSG);
        //工作台副标题
        platformMetaSessionVO.setCustomerSessionSubName(CenterConstants.CUSTOMER_SESSION_SUB_NAME);
        //工作台session 头像
        platformMetaSessionVO.setCustomerSessionPortrait(appManager.queryAppIconUrl(openAppDO.getAppId(), IconType.WEB));
        if (CommonConstant.IS_LINK_SERVICE == serviceType) {
            //管理员工作台描述
            platformMetaSessionVO.setAdminDescription(CenterConstants.LINK_SERVICE_ADMIN_DESCRIPTION);
            //客服工作台描述
            platformMetaSessionVO.setCustomerDescription(CenterConstants.LINK_SERVICE_CUSTOMER_DESCRIPTION);
        } else {
            //管理员工作台描述
            platformMetaSessionVO.setAdminDescription(CenterConstants.ADMIN_DESCRIPTION);
            //客服工作台描述
            platformMetaSessionVO.setCustomerDescription(CenterConstants.CUSTOMER_DESCRIPTION);
        }
        serviceNumberManager.modifyServiceWorkBenchSession(fsAdminUser, openAppDO.getAppId(), platformMetaSessionVO, ServiceTypeEnum.CREATE, serviceType);

        // 设置客服人员
        if (!CollectionUtils.isEmpty(appAdminIds)) {
            List<String> admins = appAdminIds.stream().map(Object::toString).collect(Collectors.toList());
            SupportStaffForm supportStaffForm = new SupportStaffForm();
            supportStaffForm.setSupportStaffs(admins.toArray(new String[admins.size()]));
            supportStaffForm.setSupportStaffName(ConfigCenter.SERVICE_CUSTOMER_DEFAULT_NAME);
            supportStaffForm.setAppId(openAppDO.getAppId());
            serviceNumberManager.setSupportStaff(fsAdminUser, supportStaffForm);
        }

    }

    /**
     * 创建服务号，自定义应用需要给管理员推送消息
     * @param appName 名称
     * @param appType 类型
     * @param user 用户
     * @param appId 应用ID
     */
    private void appOrServiceCreateToMq(String appName, AppCenterEnum.AppType appType, FsUserVO user, String appId) {
        CommonThreadPoolUtils.getExecutor().execute(() -> {
            AppOrServiceCreateItem appOrServiceCreateItem = new AppOrServiceCreateItem();
            appOrServiceCreateItem.setAppId(appId);
            appOrServiceCreateItem.setEnterpriseAccount(user.getEnterpriseAccount());
            appOrServiceCreateItem.setUserId(user.getUserId());
            appOrServiceCreateItem.setAppName(appName);
            appOrServiceCreateItem.setAppType(appType.value());
            appOrServiceCreateItem.setOperationTime(new Date());

            CommonThreadPoolUtils.getExecutor().execute(() -> operatingAppMessageService.appOrServiceCreate(appOrServiceCreateItem));
        });
    }

    private void initAutoReply(FsUserVO user, CustomAppCreateForm form, String appId, AppCreateTemplateExecuteJsonVO jsonVO, Map<String, String> materialIdMap) {
        if (null != form.getOpenAutoReply() && CommonConstant.YES == form.getOpenAutoReply()) {
            //开启，0.关闭 1.开启
            MsgBaseResult msgBaseResult = msgAutoReplyService.setAutoReplySwitch(user.getEnterpriseAccount(), appId, 1);
            if (!msgBaseResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, msgBaseResult, "开启自动回复失败"); // ignoreI18n
            }
        }

        if (!ConfigCenter.CREATE_SERVICE_BY_TEMPLATE_NOT_INIT_AUTO_KEY_REPLY) {
            if (null != jsonVO && null != jsonVO.getDefaultAutoReply()) {
                CreateDefaultReplyVO createDefaultReplyVO = new CreateDefaultReplyVO();
                createDefaultReplyVO.setAppID(appId);
                createDefaultReplyVO.setEnterpriseAccount(user.getEnterpriseAccount());
                createDefaultReplyVO.setActiveReplyType(jsonVO.getDefaultAutoReply().getActiveReplyType());
                createDefaultReplyVO.setContentTxt(jsonVO.getDefaultAutoReply().getContentTxt());
                String contentImgTxtID = jsonVO.getDefaultAutoReply().getContentImgTxtID();
                if (StringUtils.isNotBlank(contentImgTxtID)) {
                    createDefaultReplyVO.setContentImgTxtID(materialIdMap.get(contentImgTxtID));
                }
                CreateDefaultReplyResult defaultReply = msgDefaultReplyService.createDefaultReply(createDefaultReplyVO);
                if (!defaultReply.isSuccess()) {
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, defaultReply, "设置默认回复失败."); // ignoreI18n
                }
            }
            if (null != jsonVO && !CollectionUtils.isEmpty(jsonVO.getKeywordAutoReply())) {
                jsonVO.getKeywordAutoReply().forEach(autoReplyJsonVO -> {
                    CreateKeywordReplyVO createKeywordReplyVO = new CreateKeywordReplyVO();
                    createKeywordReplyVO.setAppID(appId);
                    createKeywordReplyVO.setEnterpriseAccount(user.getEnterpriseAccount());
                    createKeywordReplyVO.setActiveReplyType(autoReplyJsonVO.getActiveReplyType());
                    createKeywordReplyVO.setContentTxt(autoReplyJsonVO.getContentTxt());
                    String contentImgTxtID = autoReplyJsonVO.getContentImgTxtID();
                    if (StringUtils.isNotBlank(contentImgTxtID)) {
                        createKeywordReplyVO.setContentImgTxtID(materialIdMap.get(contentImgTxtID));
                    }
                    createKeywordReplyVO.setRuleName(autoReplyJsonVO.getRuleName());
                    List<KeywordTypeInfo> keywordList = new ArrayList<>();
                    autoReplyJsonVO.getKeywordList().forEach(autoReplyKeywordTypeJsonVO -> {
                        KeywordTypeInfo info = new KeywordTypeInfo();
                        info.setName(autoReplyKeywordTypeJsonVO.getName());
                        info.setType(autoReplyKeywordTypeJsonVO.getType());
                        keywordList.add(info);
                    });
                    createKeywordReplyVO.setKeywordList(keywordList);
                    CreateKeywordReplyResult keywordReplyResult = msgKeywordReplyService.createKeywordReply(createKeywordReplyVO);
                    if (!keywordReplyResult.isSuccess()) {
                        throw new BizException(AjaxCode.BIZ_EXCEPTION, keywordReplyResult, "设置关键字回复失败."); // ignoreI18n
                    }
                });
            }
        }

    }

    private void initMaterial(FsUserVO user, String appName, String appId, AppCreateTemplateExecuteJsonVO jsonVO, Map<String, String> materialIdMap) {
        if (null != jsonVO && null != jsonVO.getMaterials() && !jsonVO.getMaterials().isEmpty()) {
            jsonVO.getMaterials().forEach(materialJsonVO -> {
                List<ImageTextParam> paramList = new ArrayList<>();
                ImageTextMaterialForm imageTextMaterialForm = new ImageTextMaterialForm();

                materialJsonVO.getImageTextParams().forEach(imageTextMaterialParamJsonVO -> {
                    ImageTextParam imageTextParam = JsonKit.object2object(imageTextMaterialParamJsonVO, ImageTextParam.class);
                    imageTextParam.setType(ImageTextTypeEnum.getByCode(imageTextMaterialParamJsonVO.getType()));
                    paramList.add(imageTextParam);
                });
                imageTextMaterialForm.setAppId(appId);
                imageTextMaterialForm.setAppName(appName);
                imageTextMaterialForm.setImageTextParams(paramList);
                String materialId = materialManager.createMaterial(user, imageTextMaterialForm);
                materialIdMap.put(materialJsonVO.getKeyId(), materialId);
            });
        }
    }

    private void initCustomMenu(FsUserVO user, CustomAppCreateForm form, String appId, AppCreateTemplateExecuteJsonVO jsonVO, Map<String, String> materialIdMap) {
        //初始化自定义菜单.
        if (null != form.getOpenCustomMenu() && CommonConstant.YES == form.getOpenCustomMenu()) {
            customMenuManager.enableCustomMenu(user, appId);

        }
        if (null != jsonVO && !CollectionUtils.isEmpty(jsonVO.getCustomMenu())) {
            List<CustomMenuTemplateJsonVO> customMenuList = jsonVO.getCustomMenu();
            CustomMenuForm customMenu = new CustomMenuForm();
            if (!CollectionUtils.isEmpty(customMenuList)) {
                customMenu.setAppId(appId);
                customMenu.setMenus(new ArrayList<>());
                customMenuList.forEach(formVO -> {
                    MenuFormVO menuFormVO = JsonKit.object2object(formVO, MenuFormVO.class);
                    if (StringUtils.isNoneBlank(menuFormVO.getMaterialId())) {
                        menuFormVO.setMaterialId(materialIdMap.get(menuFormVO.getMaterialId()));
                    }
                    menuFormVO.getChildren().forEach(menuFormVOChildren -> {
                        if (StringUtils.isNoneBlank(menuFormVOChildren.getMaterialId())) {
                            menuFormVOChildren.setMaterialId(materialIdMap.get(menuFormVOChildren.getMaterialId()));
                        }
                        //如果是绑定应用，需要设置appId
                        if (CustomMenuTypeEnum.BIND_APP.getCode() == menuFormVOChildren.getType() && menuFormVOChildren.getUrl().indexOf("appId") > 0){
                            menuFormVOChildren.setUrl(menuFormVOChildren.getUrl()+appId);
                        }
                    });
                    //如果是绑定应用，需要设置appId
                    if (CustomMenuTypeEnum.BIND_APP.getCode() == menuFormVO.getType() && menuFormVO.getUrl().indexOf("appId") > 0){
                        menuFormVO.setUrl(menuFormVO.getUrl()+appId);
                    }
                    customMenu.getMenus().add(menuFormVO);
                });
                customMenuManager.saveCustomMenuList(user, customMenu);
            }
        }
    }

    @Override
    public List<Map<String, Object>> queryCreateTemplateList(FsUserVO user, int templateType, String lang) {
        BaseResult<List<OpenAppCreateTemplateDO>> listBaseResult = openAppCreateTemplateService.queryCreateTemplateList(user, templateType);
        if (!listBaseResult.isSuccess()) {
            throw new BizException(AjaxCode.PARAM_ERROR, listBaseResult, "应用创建模板不存在."); // ignoreI18n
        }

        List<OpenAppCreateTemplateDO> resultList = listBaseResult.getResult();

        // 服务号创建模板需屏蔽掉空白应用模板及快捷入口模板
        resultList = resultList.stream().
                filter((templateDO) -> !(CommonConstant.BLANK_APP_CREATE_TEMPLATE_ID.equals(templateDO.getTemplateId())
                        || CommonConstant.FAST_APP_CREATE_TEMPLATE_ID.equals(templateDO.getTemplateId()))).
                collect(Collectors.toList());

        //替换多语言
        int ei = eieaConverter.enterpriseAccountToId(user.getEa());
        resultList = I18NUtils.modifyCreateTemplateByLang(ei, resultList, lang);


        return translateTemplateDOToVO(resultList);
    }

    private List<Map<String, Object>> translateTemplateDOToVO(List<OpenAppCreateTemplateDO> templateDOList) {
        return templateDOList.stream().map(entity -> {
            Map<String, Object> vo = new HashMap<>();
            vo.put("templateId", entity.getTemplateId());
            vo.put("templateType", entity.getTemplateType());
            vo.put("templateIcon", entity.getTemplateIcon());
            vo.put("title", entity.getTitle());
            vo.put("categoryId", entity.getCategoryId());
            vo.put("templateOrder", entity.getTemplateOrder());
            AppCreateTemplateProperties properties = JsonKit.json2object(entity.getProperties(), AppCreateTemplateProperties.class);
            vo.put("dtlImgList", properties.getDtlImgList());
            vo.put("resultImgList", properties.getResultImgList());
            vo.put("funcList", properties.getFuncList());

            AppCreateTemplateExecuteJsonVO jsonVO = JsonKit.json2object(entity.getExecuteJson(), AppCreateTemplateExecuteJsonVO.class);
            vo.put("appName", jsonVO.getAppName());
            vo.put("appDesc", jsonVO.getAppDesc());
            vo.put("appLogo", jsonVO.getAppLogo());
            vo.put("appLoginUrl", jsonVO.getAppLoginUrl());
            vo.put("webLoginUrl", jsonVO.getWebLoginUrl());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OpenAppCreateTemplateCategoryVO> queryCreateTemplateCategoryList(FsUserVO user, int templateType) {
        // 获取分类列表
        BaseResult<List<OpenAppCreateTemplateCategoryDO>> categoryListBaseResult = openAppCreateTemplateCategoryService.queryCreateTemplateCategoryList(templateType);
        if (!categoryListBaseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, categoryListBaseResult, "查询应用创建模板分类失败."); // ignoreI18n
        }
        List<OpenAppCreateTemplateCategoryDO> categoryList = categoryListBaseResult.getResult();

        // 获取创建模板列表
        BaseResult<List<OpenAppCreateTemplateDO>> templateListResult = openAppCreateTemplateService.queryCreateTemplateList(user, templateType);
        if (!templateListResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, templateListResult, "查询应用创建模板失败."); // ignoreI18n
        }
        List<OpenAppCreateTemplateDO> allTemplateDOList = templateListResult.getResult();

        // 转换成(Key：分类Id，value:模板列表)的Map
        Map<String, List<OpenAppCreateTemplateDO>> categoryTemplateListMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(allTemplateDOList)) {
            allTemplateDOList.forEach((templateDO) -> {
                String categoryId = templateDO.getCategoryId();
                categoryTemplateListMap.putIfAbsent(categoryId, new ArrayList<>());
                categoryTemplateListMap.get(categoryId).add(templateDO);
            });
        }

        List<OpenAppCreateTemplateCategoryVO> categoryVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(categoryList)) {
            return categoryVOList;
        } else {
            categoryList.forEach(categoryDO -> {
                OpenAppCreateTemplateCategoryVO categoryVO = compositeTemplateCategoryVO(categoryDO, categoryTemplateListMap);
                categoryVOList.add(categoryVO);
            });
        }
        return categoryVOList;
    }

    private OpenAppCreateTemplateCategoryVO compositeTemplateCategoryVO(OpenAppCreateTemplateCategoryDO categoryDO,
                                                                        Map<String, List<OpenAppCreateTemplateDO>> categoryTemplateListMap) {
        OpenAppCreateTemplateCategoryVO categoryVO = new OpenAppCreateTemplateCategoryVO();
        categoryVO.setCategoryId(categoryDO.getCategoryId());
        categoryVO.setCategoryTitle(categoryDO.getCategoryTitle());
        categoryVO.setCategoryDesc(categoryDO.getCategoryDesc());
        categoryVO.setCategoryOrder(categoryDO.getCategoryOrder());

        List<OpenAppCreateTemplateDO> templateDOList = categoryTemplateListMap.get(categoryDO.getCategoryId());
        List<Map<String, Object>> templateVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(templateDOList)) {
            templateVOList = translateTemplateDOToVO(templateDOList);
        }
        categoryVO.setTemplateList(templateVOList);
        return categoryVO;
    }

    @Override
    public void checkAndCreateDemoApp(FsUserVO user, String lang) {
        BaseResult<OpenDemoAppDO> openDemoAppDOBaseResult = openDemoAppService.loadDemoAppByFsEa(user, user.getEnterpriseAccount());
        if (AppCenterCodeEnum.DEMO_APP_NOT_EXISTS.getErrCode() == openDemoAppDOBaseResult.getErrCode()) {
            BaseResult<String> saveDemoAppResult = openDemoAppService.saveDemoApp(user, user.getEnterpriseAccount());
            if (!saveDemoAppResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, saveDemoAppResult, "创建示例应用失败."); // ignoreI18n
            }
            CustomAppCreateForm form = new CustomAppCreateForm();
            BaseResult<OpenAppCreateTemplateDO> openAppCreateTemplateDOResult = openAppCreateTemplateService.loadById(CommonConstant.DEMO_APP_CREATE_TEMPLATE_ID);
            if (!openAppCreateTemplateDOResult.isSuccess()) {
                throw new BizException(AjaxCode.PARAM_ERROR, openAppCreateTemplateDOResult, "应用创建模板不存在."); // ignoreI18n
            }
            OpenAppCreateTemplateDO openAppCreateTemplateDO = openAppCreateTemplateDOResult.getResult();
            AppCreateTemplateExecuteJsonVO jsonVO = JsonKit.json2object(openAppCreateTemplateDO.getExecuteJson(), AppCreateTemplateExecuteJsonVO.class);
            form.setAppName(jsonVO.getAppName());
            form.setAppDesc(jsonVO.getAppDesc());
            form.setAppLogo(jsonVO.getAppLogo());
            form.setAppLoginUrl(jsonVO.getAppLoginUrl());
            form.setWebLoginUrl(jsonVO.getWebLoginUrl());
            List<Integer> adminIds = webAuthManager.queryAdminIds(user.getEnterpriseAccount());
            Integer[] admins = adminIds.toArray(new Integer[adminIds.size()]);
            form.setAppAdmins(admins);
            AppViewDO view = new AppViewDO();
            view.setDepartment(new Integer[0]);
            view.setMember(admins);
            form.setAppView(JsonKit.object2json(view));
            String appId = createCustomAppByTemplate(user, lang, form, openAppCreateTemplateDO, AppCenterEnum.AppType.CUSTOM_APP);
            BaseResult<Void> updateDemoAppIdResult = openDemoAppService.updateDemoAppId(user, saveDemoAppResult.getResult(), appId);
            if (!updateDemoAppIdResult.isSuccess()) {
                throw new BizException(AjaxCode.BIZ_EXCEPTION, updateDemoAppIdResult, "创建示例应用失败!"); // ignoreI18n
            }
        } else if (!openDemoAppDOBaseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, openDemoAppDOBaseResult, "初始化示例应用失败."); // ignoreI18n
        }
    }

    /**
     * 初始化移动多客服功能
     * @param user 用户
     * @param appId 应用id
     * @param jsonVO 开关属性
     */
    private void initServiceNumberSwitch(FsUserVO user, String lang, String appId, AppCreateTemplateExecuteJsonVO jsonVO){
        if (null != jsonVO && null != jsonVO.getOpenServiceNumber() && CommonConstant.YES == jsonVO.getOpenServiceNumber()) {
            String enterpriseAccount = user.getEnterpriseAccount();
            OpenServiceNumberDO entity = new OpenServiceNumberDO();
            entity.setAppId(appId);
            entity.setFsEa(enterpriseAccount);
            entity.setSwitchType(CommonConstant.SERVICE_NUMBER_ON);
            serviceNumberManager.updateServiceNumberOnOff(entity, user, lang, true);
        }
    }

    /**
     * 开启"审批单"功能
     * @param user
     * @param appId
     */
    private void initApprovalSwitch(FsUserVO user, String appId) {
        ServiceNumberForm form = new ServiceNumberForm(appId, CommonConstant.APPROVAL_ON);
        serviceNumberManager.setApproval(user, form);
    }

    /**
     * 开启工单
     * @param user 用户
     * @param appId
     * @param jsonVO 开关属性
     */
    //老工单不用了
/*    private void initWorkOrder(FsUserVO user, String appId, AppCreateTemplateExecuteJsonVO jsonVO){
        if (null != jsonVO && null != jsonVO.getOpenWorkOrder() && CommonConstant.YES == jsonVO.getOpenWorkOrder()){
            serviceNumberManager.setWorkOrder(user, new ServiceNumberForm(appId, jsonVO.getOpenWorkOrder()));
        }
    }*/

    /**
     * 开启问卷
     * @param user 用户
     * @param appId 应用id
     * @param jsonVO 开关属性
     */
    private void initQuestionnaire(FsUserVO user, String appId, AppCreateTemplateExecuteJsonVO jsonVO){
        if (null != jsonVO && null != jsonVO.getOpenQuestionnaire() && CommonConstant.YES == jsonVO.getOpenQuestionnaire()){
            serviceNumberManager.setQuestionnaire(user, new ServiceNumberForm(appId, jsonVO.getOpenQuestionnaire()));
            }
    }

    /**
     * 开启服务工单
     * @param user 用户
     * @param appId
     * @param jsonVO 开关属性
     */
    private void initWorkOrderPaas(FsUserVO user, String appId, AppCreateTemplateExecuteJsonVO jsonVO){
        if (null != jsonVO){
            serviceNumberManager.setWorkOrderPaas(user, new ServiceNumberForm(appId, 1));
        }
    }
}
