package com.facishare.open.app.center.interceptor;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.kits.JsonKit;
import com.facishare.open.app.center.manager.WebAuthManager;
import com.facishare.open.app.center.threadlocal.UserContextHolder;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.github.trace.TraceContext;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * user 信息 。localhost处理 。
 *
 * <AUTHOR>
 * @date on 2016/8/18.
 */
public class UserHandlerInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private WebAuthManager webAuthManager;

    private static final Logger logger = LoggerFactory.getLogger(UserHandlerInterceptor.class);


    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                             Object o) throws Exception {

        //是否是不验证身份请求url
        if (isUnCheckUrl(httpServletRequest.getRequestURI())) {
            return true;
        }
        Cookie[] cookies = httpServletRequest.getCookies();

        if (null == cookies || cookies.length == 0) {
            userCheckFailHandler(httpServletRequest, httpServletResponse, AjaxCode.USER_NOT_LOGIN, "验证用户登录信息失败"); // ignoreI18n
            return false;
        }

        List<String> fsAuthXList = Arrays.asList(cookies).stream().filter(s -> s.getName().equals("FSAuthXC")).map(
                Cookie::getValue)
                .collect(
                        Collectors.toList());

        if (fsAuthXList.size() < 1) {
            userCheckFailHandler(httpServletRequest, httpServletResponse, AjaxCode.USER_NOT_LOGIN, "验证用户登录信息失败"); // ignoreI18n
            return false;
        }

        String fsAuthXCookie = fsAuthXList.get(0);
        FsUserVO userVO = null;
        try {
            userVO = webAuthManager.loadWebFsUser(fsAuthXCookie);
        } catch (Exception e) {
            logger.warn("check user error,url[{}]", httpServletRequest.getRequestURI(), e);
        }
        if (userVO == null) {
            userCheckFailHandler(httpServletRequest, httpServletResponse, AjaxCode.USER_NOT_LOGIN, "验证用户登录信息失败"); // ignoreI18n
            return false;
        }

        //获取语言环境
        List<String> langList = Arrays.asList(cookies).stream().filter(s -> s.getName().equals("lang")).map(Cookie::getValue).collect(Collectors.toList());
        logger.debug("langList[{}]", langList);
        String defaultLang = "zh-CN";
        try {
            if (langList.size() > 0) {
                defaultLang = langList.get(0);
            }
        } catch (Exception e) {
            logger.warn("getLocaleFailed. langList[{}]", langList);
        }


        //根据配置中心配置，可以禁止某些企业的写操作。
        if (BizCommonUtils.isAllowDatabaseOnlySupportQuery(userVO.getEnterpriseAccount())) {
            if (!isQueryRequest(httpServletRequest)) {
                userCheckFailHandler(httpServletRequest, httpServletResponse, AjaxCode.BIZ_EXCEPTION,
                        "系统升级维护中,暂时仅提供查询服务,请稍后再试!"); // ignoreI18n
                return false;
            }
        }

        //埋点数据
        uploadData(userVO, httpServletRequest, httpServletResponse);

        ThreadLocal<FsUserVO> fsUserVOHolder = UserContextHolder.getFsUserVOHolder();
        fsUserVOHolder.set(userVO);

        ThreadLocal<String> langHolder = UserContextHolder.getLangHolder();
        langHolder.set(defaultLang);
        TraceContext.get().setLocale(defaultLang);
        return true;
    }

    private void uploadData(FsUserVO userVO, HttpServletRequest httpServletRequest,
                            HttpServletResponse httpServletResponse) {

        String traceId = httpServletRequest.getParameter("traceId");
        if (!StringUtils.isEmpty(traceId)) {
            httpServletResponse.setHeader("sentHttpXTraceId", traceId);
        } else {
            httpServletResponse.setHeader("sentHttpXTraceId", "-");
        }

        if (userVO != null) {
            httpServletResponse.setHeader("sentHttpXUserId", userVO.getEnterpriseAccount() + "." + userVO.getUserId());
        } else {
            httpServletResponse.setHeader("sentHttpXUserId", "-");
        }

        //默认值
        httpServletResponse.setHeader("sentHttpXDeviceId", "-");
        httpServletResponse.setHeader("sentHttpXPlatFormId", "1103");
        httpServletResponse.setHeader("sentHttpXOsVersion", "-");
        httpServletResponse.setHeader("sentHttpXProductVersion", "-");
    }

    private boolean isUnCheckUrl(String requestURI) {
        if (StringUtils.isEmpty(requestURI)) {
            return false;
        }

        Iterator<String> iterator = Splitter.on(";").split(ConfigCenter.UN_CHECK_LOGIN_URL).iterator();

        while (iterator.hasNext()) {
            String unCheckUrl = iterator.next();
            if (requestURI.startsWith(unCheckUrl)) {
                return true;
            }
        }

        return false;
    }

    private boolean isQueryRequest(HttpServletRequest httpServletRequest) {
        String url = httpServletRequest.getRequestURI();
        String method = url.substring(url.lastIndexOf("/") + 1);
        logger.info("=== filterQueryRequest requestUri[{}] method [{}]", url, method);
        for (String pre : ConfigCenter.FILTER_METHOD_PRE) {
            if (method.startsWith(pre)) {
                return true;
            }
        }
        return false;
    }


    private void userCheckFailHandler(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                      int errorCode, String errorMsg) {
        logger.info("check user error,url[{}]", httpServletRequest.getRequestURI());
        httpServletResponse.setContentType("application/json;charset=UTF-8");
        AjaxResult ajaxResult = new AjaxResult(errorCode, errorMsg);

        PrintWriter writer;
        try {
            writer = httpServletResponse.getWriter();
        } catch (IOException e) {
            logger.error("httpServletResponse error. uri[{}]", httpServletRequest.getRequestURI(), e);
            return;
        }
        writer.append(JsonKit.object2json(ajaxResult));
        writer.flush();
        writer.close();
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                Object o, Exception e) throws Exception {
        UserContextHolder.resetFsUserVO();
        UserContextHolder.resetLang();
    }
}
