package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.result.SelfMenuConfigQueryResult;
import com.facishare.wechat.proxy.model.vo.MenuInfoBaseVo;
import com.facishare.wechat.proxy.model.vo.SelfmenuQueryVo;
import com.facishare.wechat.proxy.service.WechatSelfmenuService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by huyue on 2016/11/10.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-dubbo-test.xml")
public class WechatCustomerMenuControllerTest {
    private org.slf4j.Logger logger = LoggerFactory.getLogger(getClass());

    private final String appId = "FSAID_10b07606";
    private final String wxAppId = "wx48a4ec382fe5583e";

    @Resource
    private WechatSelfmenuService wechatSelfmenuService;

    @Test
    public void queryMenu() throws Exception {
        SelfmenuQueryVo selfmenuQueryVo = new SelfmenuQueryVo();
        selfmenuQueryVo.setWxAppId(wxAppId);
        ModelResult<SelfMenuConfigQueryResult> selfMenuConfigQueryResult = wechatSelfmenuService.querySelfConfigMenu(selfmenuQueryVo);
        if (!selfMenuConfigQueryResult.isSuccess() || null == selfMenuConfigQueryResult) {
            logger.warn("failed to call wechatSelfmenuService.querySelfConfigMenu, selfmenuQueryVo=[{}], selfMenuConfigQueryResult=[{}]",
                    selfmenuQueryVo, selfMenuConfigQueryResult.toString());
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询微信自定义菜单失败");
        }
        // 将结果进行包装
        List<MenuInfoBaseVo.Button> buttons = selfMenuConfigQueryResult.getResult().getMenuInfo().getButtons();
        logger.info("buttons=[{}]", buttons);
    }

}
