package com.facishare.open.custom.menu.api.enums;

import lombok.Getter;
import lombok.ToString;

import java.util.Optional;

/**
 * 响应枚举
 *
 * <AUTHOR>
 * @since on 2015/12/9.
 */
@Getter
@ToString
public enum ResponseEnum {
    OPEN_RESPONSE(1), //开平响应
    THIRD_PARTY_RESPONSE(2) //第三方响应
    ;

    private int code;

    ResponseEnum(int code) {
        this.code = code;
    }

    /**
     * 根据枚举内部值获取枚举常量.
     */
    public static Optional<ResponseEnum> getByCode(int code) {

        for (ResponseEnum e : ResponseEnum.values()) {
            if (e.getCode() == code) {
                return Optional.of(e);
            }
        }

        return Optional.empty();
    }
}
