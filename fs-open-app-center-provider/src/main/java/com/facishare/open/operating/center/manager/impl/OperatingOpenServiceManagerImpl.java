package com.facishare.open.operating.center.manager.impl;

import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.OperationSource;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.mq.item.AppAdminItem;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.jobs.api.model.WorkVO;
import com.facishare.open.jobs.api.service.WorkCallBackService;
import com.facishare.open.operating.center.api.com.EventFlag;
import com.facishare.open.operating.center.com.CommonConstant;
import com.facishare.open.operating.center.com.OperationTypeConstant;
import com.facishare.open.operating.center.manager.*;
import com.facishare.open.operating.center.model.ServiceTemplateTaskDO;
import com.facishare.open.operating.center.utils.OperatingBizCommonUtils;
import com.facishare.open.operating.center.utils.ConfigCenter;
import com.facishare.open.operating.center.utils.DateFormatUtils;
import com.facishare.open.operating.center.utils.JsonKit;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:应用，服务号，管理员变更事件处理类
 * User: zhouq
 * Date: 2016/7/28
 */
@Service
public class OperatingOpenServiceManagerImpl implements OperatingOpenServiceManager {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OperatingAppMessageManager operatingAppMessageManager;

    @Resource
    private FirstTimeEventManager firstTimeEventManager;

    @Resource
    private ServiceOperationLogManager serviceOperationLogManager;

    @Resource
    private OpenAppService openAppService;

    @Resource
    private OpenServiceTemplateManager openServiceTemplateManager;

    @Resource
    private WorkCallBackService workCallBackService;

    @Override
    public void adminsChanged(AppAdminItem appAdminItem) {
        logger.info("receive OpenServiceManagerImpl adminsChanged msg, appAdminItem={}", appAdminItem);

        AppResult openAppResult = openAppService.loadOpenApp(appAdminItem.getAppId());
        if (!openAppResult.isSuccess() || null == openAppResult.getResult()) {
            logger.warn("openAppService.loadOpenApp failed, appAdminItem={}", appAdminItem);
            return;
        }
        OpenAppDO openAppDO = openAppResult.getResult();
        if (!CollectionUtils.isEmpty(appAdminItem.getAdminsAdded())) {
            adminsAdd(appAdminItem, openAppDO);
        }
        if (!CollectionUtils.isEmpty(appAdminItem.getAdminsRemoved())) {
            adminsRemoved(appAdminItem, openAppDO);
        }
    }

    /**
     * 管理员新增事件
     *
     * @param appAdminItem 管理员信息
     * @param openAppDO    应用信息
     */
    private void adminsAdd(AppAdminItem appAdminItem, OpenAppDO openAppDO) {
        if (appAdminItem.getUserId()!= null && appAdminItem.getUserId() < 0) {
            logger.info("skip addminsAdd Biz. userId[{}]", appAdminItem.getUserId());
            return;
        } else {
            //如果是创建服务号，需要先发创建服务号消息，和先保存服务号创建记录
            if (appAdminItem.getAdminsAdded().contains(appAdminItem.getUserId())) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    logger.warn("Thread.sleep(2000) appAdminItem error");
                }
                appAdminItem.setOperationTime(new Date(appAdminItem.getOperationTime().getTime() + 1000));
            }
            sendServiceAdminAddMsg(appAdminItem, openAppDO);
            judgeFirstBeAdmin(appAdminItem, openAppDO);

            // 判断是否是第一次成为服务号模板管理员
            if (OperatingBizCommonUtils.isServiceTemplateAdminOperating(appAdminItem.getEnterpriseAccount()) && StringUtils.isNotBlank(ConfigCenter.IS_OPEN_GROUP_MSG_TASK_FUNCTION) && Boolean.parseBoolean(ConfigCenter.IS_OPEN_GROUP_MSG_TASK_FUNCTION)) {
                firstBeServiceTemplateAdmin(appAdminItem, openAppDO);
            }

            saveAdminsChangeLog(appAdminItem, true);
        }
    }

    /**
     * 首次成为服务号模板的管理员
     * @param appAdminItem 事件信息
     */
    private void firstBeServiceTemplateAdmin(AppAdminItem appAdminItem, OpenAppDO openAppDO) {
        logger.info("step into firstBeServiceTemplateAdmin, appAdminItem={}, openAppDO={}", appAdminItem, openAppDO);
        // 过滤掉不是第一次成为服务号模板的管理员id.
        if (null == appAdminItem || null == openAppDO || AppCenterEnum.AppType.SERVICE.value() != openAppDO.getAppType() || StringUtils.isBlank(openAppDO.getProperties()) || CollectionUtils.isEmpty(appAdminItem.getAdminsAdded())) {
            return;
        }
        List<Integer> adminsAddList = appAdminItem.getAdminsAdded();
        List<Integer> newAdminsAddList = new ArrayList<>();
        try {
            JSONObject propertyObj = JSONObject.fromObject(openAppDO.getProperties());
            if (null == propertyObj || StringUtils.isBlank(propertyObj.getString("createTemplateId"))) {
                return;
            }
            String serviceTemplateId = propertyObj.getString("createTemplateId");
            String eventFlag = EventFlag.FIRST_SERVICE_TEMPLATE_ADMIN_ADD + "-" + serviceTemplateId;
            //去掉不是第一次的模板管理员
            newAdminsAddList.addAll(adminsAddList.stream().filter(id -> firstTimeEventManager.isFirstTime(eventFlag, new FsUserVO(appAdminItem.getEnterpriseAccount(), id).asStringUser())).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(newAdminsAddList)) {
                Date gmtBiz = new Date();
                List<WorkVO> taskList = new ArrayList<>(newAdminsAddList.size());
                for (Integer userId : newAdminsAddList) {
                    // 记录任务事件至本地mongodb
                    openServiceTemplateManager.saveServiceTemplateAdminTask(serviceTemplateId, DateFormatUtils.formatDate(gmtBiz), DateFormatUtils.getDatesDelay(gmtBiz, 3), new FsUserVO(appAdminItem.getEnterpriseAccount(), userId));
                    // 整合为taskList
                    ServiceTemplateTaskDO serviceTemplateTaskDO = new ServiceTemplateTaskDO();
                    serviceTemplateTaskDO.setAppId(appAdminItem.getAppId());
                    serviceTemplateTaskDO.setServiceTemplateId(serviceTemplateId);
                    serviceTemplateTaskDO.setStringUser(new FsUserVO(appAdminItem.getEnterpriseAccount(), userId).asStringUser());
                    serviceTemplateTaskDO.setDelay(3);
                    serviceTemplateTaskDO.setStartTime(gmtBiz);
                    WorkVO workVO = new WorkVO(CommonConstant.SERVICE_ADMIN_UN_USED, JsonKit.object2json(serviceTemplateTaskDO), gmtBiz);
                    taskList.add(workVO);
                }
                // 提交到任务系统
                BaseResult<List<String>> submitResult = workCallBackService.submitBatch(taskList);
                if (!submitResult.isSuccess()) {
                    logger.error("workCallBackService.submitBatch failed, taskList={}, submitResult={}", taskList.toString(), submitResult.getResult());
                }
            }
        } catch (Exception e) {
            logger.warn("firstBeServiceTemplateAdmin failed, appAdminItem={}, openAppDO={}", appAdminItem, openAppDO, e);
        }

    }

    /**
     * 新增服务号管理员
     *
     * @param appAdminItem 事件信息
     * @param openAppDO    应用信息
     */
    private void sendServiceAdminAddMsg(AppAdminItem appAdminItem, OpenAppDO openAppDO) {
        if (ConfigCenter.NO_SEND_MSG_APP_IDS.contains(appAdminItem.getAppId())){
            return;
        }
        String appName = openAppDO.getAppName();
        if (AppCenterEnum.AppType.SERVICE.value() == openAppDO.getAppType()) {
            String notifyMsg = String.format(ConfigCenter.NOTIFY_SERVICE_ADMIN_RIGHT_ADD, appName);
            operatingAppMessageManager.sendTextMsgByFsApp(appAdminItem.getAppId(), appAdminItem.getEnterpriseAccount(), notifyMsg,
                    Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsAdded());
        } else if (AppCenterEnum.AppType.BASE_APP.value() == openAppDO.getAppType()) {
            String notifyMsg;
            //微信互联应用提示需要特殊处理
            if (ConfigCenter.WX_LINK_NOTICE_APP_ID.equals(openAppDO.getAppId())){
                notifyMsg = String.format(ConfigCenter.TZ_APP_ADMIN_ADD, appName);
            }else {
                notifyMsg = String.format(ConfigCenter.NOTIFY_APP_ADMIN_RIGHT_ADD, appName);
            }
            operatingAppMessageManager.sendTextMsgByFsApp(ConfigCenter.FS_APP_BUTLER_APP_ID, appAdminItem.getEnterpriseAccount(), notifyMsg,
                    Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsAdded());
        } else if (AppCenterEnum.AppType.OUT_SERVICE_APP.value() == openAppDO.getAppType()) {
            String notifyMsg = String.format(ConfigCenter.NOTIFY_OUTER_SERVICE_ADMIN_RIGHT_ADD, appName);
            operatingAppMessageManager.sendTextMsgByFsApp(ConfigCenter.PLAY_COMPANY_SERVICE_APP_ID, appAdminItem.getEnterpriseAccount(), notifyMsg,
                    Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsAdded());
        } else if (AppCenterEnum.AppType.LINK_SERVICE.value() == openAppDO.getAppType()) {
            String notifyMsg = String.format(ConfigCenter.NOTIFY_LINK_SERVICE_ADMIN_RIGHT_ADD, appName);
            String appId = appAdminItem.getAppId();
            operatingAppMessageManager.sendTextMsgByFsApp(appId, appAdminItem.getEnterpriseAccount(), notifyMsg,
                    Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsAdded());
        }
    }

    private void judgeFirstBeAdmin(AppAdminItem appAdminItem, OpenAppDO openAppDO) {
        if (ConfigCenter.NO_SEND_MSG_APP_IDS.contains(appAdminItem.getAppId())){
            return;
        }
        // 过滤掉不是第一次成为管理员的id.
        List<Integer> adminsAddList = appAdminItem.getAdminsAdded();
        List<Integer> newAdminsAddList = new ArrayList<>();
        String enterpriseAccount = appAdminItem.getEnterpriseAccount();

        try {
            if (AppCenterEnum.AppType.SERVICE.value() == openAppDO.getAppType()) {
                newAdminsAddList.addAll(adminsAddList.stream().filter(id -> firstTimeEventManager.isFirstTime(EventFlag.FIRST_SERVICE_ADMIN_ADD, new FsUserVO(enterpriseAccount, id).asStringUser())).collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(newAdminsAddList)) {
                    operatingAppMessageManager.sendImageTextMsgByPlayCompanyService(appAdminItem.getEnterpriseAccount(),
                            ConfigCenter.IS_FIRST_ADD_SERVICE_ADMINS_MATERIA_ID, Boolean.FALSE, new ArrayList<>(), newAdminsAddList);
                }
            } else if (AppCenterEnum.AppType.BASE_APP.value() == openAppDO.getAppType()) {
                String eventFlag = EventFlag.FIRST_BASE_APP_ADMIN_ADD + "_" + appAdminItem.getAppId();
                newAdminsAddList.addAll(adminsAddList.stream().filter(id -> firstTimeEventManager.isFirstTime(eventFlag, new FsUserVO(enterpriseAccount, id).asStringUser())).collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(newAdminsAddList) && !ConfigCenter.BASE_APP_ADMIN_MAP.isEmpty()) {
                    logger.info("FIRST_BASE_APP_ADMIN_ADD newAdminsAddList[{}}, BASE_APP_ADMIN_MAP[{}]", newAdminsAddList.size(), ConfigCenter.BASE_APP_ADMIN_MAP.size());
                    String appId = appAdminItem.getAppId();
                    if (ConfigCenter.BASE_APP_ADMIN_MAP.containsKey(appId)){
                        operatingAppMessageManager.sendImageTextMsgByFsAppButlerApp(appAdminItem.getEnterpriseAccount(), ConfigCenter.BASE_APP_ADMIN_MAP.get(appId),
                                Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsAdded());
                    }
                }
            } else if (AppCenterEnum.AppType.LINK_SERVICE.value() == openAppDO.getAppType()) {
                newAdminsAddList.addAll(adminsAddList.stream().filter(id -> firstTimeEventManager.isFirstTime(EventFlag.FIRST_SERVICE_ADMIN_ADD, new FsUserVO(enterpriseAccount, id).asStringUser())).collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(newAdminsAddList)) {
                    operatingAppMessageManager.sendImageTextMsgByPlayCompanyService(appAdminItem.getEnterpriseAccount(),
                            ConfigCenter.IS_FIRST_ADD_LINK_SERVICE_ADMINS_MATERIA_ID, Boolean.FALSE, new ArrayList<>(), newAdminsAddList);
                }
            }
        } catch (Exception e) {
            logger.error("call OpenServiceManagerImpl.judgeFirstBeAdmin failed. avoid mq message Retry. appAdminItem=[{}], openAppDO=[{}]", appAdminItem, openAppDO, e);
        }
    }

    /**
     * 管理员删除事件
     *
     * @param appAdminItem 管理员信息
     * @param openAppDO    应用信息
     */
    private void adminsRemoved(AppAdminItem appAdminItem, OpenAppDO openAppDO) {
        if (appAdminItem.getUserId() != null && appAdminItem.getUserId() < 0) {
            logger.info("skip adminsRemoved Biz. userId[{}]", appAdminItem.getUserId());
            return;
        } else {
            sendServiceAdminRemovedMsg(appAdminItem, openAppDO);
            saveAdminsChangeLog(appAdminItem, false);
        }
    }

    /**
     * 删除服务号管理员
     *
     * @param appAdminItem 事件信息
     */
    private void sendServiceAdminRemovedMsg(AppAdminItem appAdminItem, OpenAppDO openAppDO) {
        //如果是删除服务号，不需要再推送管理员变更消息
        if (CollectionUtils.isEmpty(appAdminItem.getAppAdmins())) {
            return;
        }
        String appName = openAppDO.getAppName();
        if (AppCenterEnum.AppType.SERVICE.value() == openAppDO.getAppType() ||
                AppCenterEnum.AppType.LINK_SERVICE.value() == openAppDO.getAppType()) {
            String notifyMsg = String.format(ConfigCenter.NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE, appName);
            operatingAppMessageManager.sendTextMsgByFsApp(appAdminItem.getAppId(), appAdminItem.getEnterpriseAccount(), notifyMsg,
                    Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsRemoved());
        } else if (AppCenterEnum.AppType.BASE_APP.value() == openAppDO.getAppType()) {
            String notifyMsg;
            //微信互联应用提示需要特殊处理
            if (ConfigCenter.WX_LINK_NOTICE_APP_ID.equals(openAppDO.getAppId())){
                notifyMsg = String.format(ConfigCenter.TZ_APP_ADMIN_REMOVE, appName);
            }else {
                notifyMsg = String.format(ConfigCenter.NOTIFY_APP_ADMIN_RIGHT_REMOVE, appName);
            }
            operatingAppMessageManager.sendTextMsgByFsApp(ConfigCenter.FS_APP_BUTLER_APP_ID, appAdminItem.getEnterpriseAccount(), notifyMsg,
                    Boolean.FALSE, new ArrayList<>(), appAdminItem.getAdminsRemoved());
        }

    }

    /**
     * 管理员变更日志写入DB
     * @param appAdminItem
     */
    private void saveAdminsChangeLog(AppAdminItem appAdminItem, boolean isAdminAdd){
        try {
            if (OperationSource.SYSTEM.value() != appAdminItem.getOperationSource()) {
                if (!CollectionUtils.isEmpty(appAdminItem.getAdminsAdded()) && isAdminAdd){
                    String nameOfAddUsers = serviceOperationLogManager.IdsToUserName(appAdminItem.getEnterpriseAccount(), appAdminItem.getAdminsAdded());
                    if (!StringUtils.isBlank(nameOfAddUsers)) {
                        serviceOperationLogManager.commonSaveOperationLog(appAdminItem.getEnterpriseAccount(), appAdminItem.getAppId(),
                                appAdminItem.getUserId(),appAdminItem.getOperationTime().getTime() + "", "设置\""+nameOfAddUsers+"\"为管理员", OperationTypeConstant.APP_CREATE); // ignoreI18n
                        // OperationTypeConstant.APP_CREATE 这个类型写得有问题，应该是应用增加类型，下面的代码也一样，择机改掉 by czy.
                    }
                }

                if (!CollectionUtils.isEmpty(appAdminItem.getAdminsRemoved()) && !isAdminAdd){
                    String nameOfRemoveUsers = serviceOperationLogManager.IdsToUserName(appAdminItem.getEnterpriseAccount(), appAdminItem.getAdminsRemoved());
                    if (!StringUtils.isBlank(nameOfRemoveUsers)) {
                        serviceOperationLogManager.commonSaveOperationLog(appAdminItem.getEnterpriseAccount(), appAdminItem.getAppId(),
                                appAdminItem.getUserId(),appAdminItem.getOperationTime().getTime()+"", "取消了\""+nameOfRemoveUsers+"\"的管理员权限", OperationTypeConstant.APP_CREATE); // ignoreI18n
                    }
                }
            }
        } catch (Exception e) {
           logger.warn("saveAdminsChangeLog failed, appAdminItem={}", appAdminItem, e);
        }
    }
}
