package com.facishare.open.app.center.manager;

import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.common.model.FsUserVO;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouq
 * on 2016/4/25.
 */
public interface AppIconManager {

    /**
     * 获取图片LogoId
     * @param appIdOrComponentId 应用ID or 组件ID
     * @param deviceType 类型 1.ios ; 2.android；3，web，4，service
     * @return iconId
     */
    String queryIconId(String appIdOrComponentId, IconType deviceType);

    /**
     * 获取图片LogoUrl
     * @param appIdOrComponentId 应用ID or 组件ID
     * @param deviceType 类型 1.ios ; 2.android；3，web，4，service
     * @return iconId
     */
    String queryIconUrl(String appIdOrComponentId, IconType deviceType);

    Map<String, String> batchQueryIconUrl(List<String> appIdOrComponentIdList, IconType deviceType);

    String queryIconUrlByUser(FsUserVO fsUserVO, String appIdOrComponentId, IconType deviceType);

    /**
     * 批量清空图片地址缓存
     * @param appIdOrComponentIds 应用ID or 组件ID集合
     */
    void resetIconUrlCacheBatch(List<String> appIdOrComponentIds);

    @Deprecated
    int deleteAppIcon(String appIdOrComponentId);
}
