<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.fxiaoke.common</groupId>
		<artifactId>fxiaoke-parent-pom</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<modules>
		<module>fs-open-app-center-api</module>
		<module>fs-open-app-center-provider</module>
		<module>fs-open-app-center-web</module>
		<module>fs-open-app-manage</module>
		<module>fs-open-center-fcp-provider</module>
        <module>fs-open-app-center-common</module>
	</modules>
	<groupId>com.facishare.open</groupId>
	<artifactId>fs-open-app-center</artifactId>
	<packaging>pom</packaging>
	<version>1.0.0-SNAPSHOT</version>

	<properties>
		<!-- <groupId>com.facishare.open</groupId>
		<artifactId>fs-open-parent-pom</artifactId>
		<version>2.0.0-SNAPSHOT</version>
		    配置开始-->
		<fs-enterprise-id-account-converter.version>1.0-SNAPSHOT</fs-enterprise-id-account-converter.version>
		<fs-open-common-result.version>0.0.7</fs-open-common-result.version>
		<fs-open-common-storage.version>0.0.1</fs-open-common-storage.version>
		<h2.version>1.4.192</h2.version>
		<easymock.version>3.0</easymock.version>
		<httpcore.version>4.4.8</httpcore.version>
		<httpmime.version>4.5.1</httpmime.version>
		<commons-fileupload.version>1.3.1</commons-fileupload.version>
		<commons-collections.version>3.2.2</commons-collections.version>
		<dubbox-rpc.version>1.0.0-SNAPSHOT</dubbox-rpc.version>
		<zkclient.version>0.10</zkclient.version>
		<rabbitmq-support.version>1.0.0-SNAPSHOT</rabbitmq-support.version>
		<!-- <groupId>com.facishare.open</groupId>
		<artifactId>fs-open-parent-pom</artifactId>
		<version>2.0.0-SNAPSHOT</version>
			配置结束-->
		<app-center.version>1.0.28</app-center.version>
		<app-center-api.version>9.4.0-SNAPSHOT</app-center-api.version>
		<fs-open-jobs-api.version>1.0.0</fs-open-jobs-api.version>
		<fs-eservice-cases.version>0.0.6-SNAPSHOT</fs-eservice-cases.version>
		<fs-online-consult-core-api.version>1.0.3-SNAPSHOT</fs-online-consult-core-api.version>
		<zkclient.version>0.11</zkclient.version>
		<config-core.version>7.6.2</config-core.version>
        <app-center-common.version>9.2.5-SNAPSHOT</app-center-common.version>
		<restproxy.version>6.6.5-SNAPSHOT</restproxy.version>
		<license-api.version>1.4.1-SNAPSHOT</license-api.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- <groupId>com.facishare.open</groupId>
			<artifactId>fs-open-parent-pom</artifactId>
			<version>2.0.0-SNAPSHOT</version>
				配置开始-->
			<dependency>
				<groupId>com.101tec</groupId>
				<artifactId>zkclient</artifactId>
				<version>${zkclient.version}</version>
				<exclusions>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>netty</artifactId>
						<groupId>io.netty</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>dubbox-rpc</artifactId>
				<version>${dubbox-rpc.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>dubbo</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.github.colin-lee</groupId>
				<artifactId>rabbitmq-support</artifactId>
				<version>${rabbitmq-support.version}</version>
			</dependency>
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql-connector-java.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-collections</groupId>
				<artifactId>commons-collections</artifactId>
				<version>${commons-collections.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpcore</artifactId>
				<version>${httpcore.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpmime</artifactId>
				<version>${httpmime.version}</version>
			</dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-enterprise-id-account-converter</artifactId>
				<version>${fs-enterprise-id-account-converter.version}</version>
			</dependency>

			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-fsc-api</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-stone-client</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-shorturl-api</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>org.easymock</groupId>
				<artifactId>easymock</artifactId>
				<version>${easymock.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>com.h2database</groupId>
				<artifactId>h2</artifactId>
				<version>${h2.version}</version>
				<scope>test</scope>
			</dependency>
			<!-- 添加mockito版本管理 -->
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-all</artifactId>
				<version>1.10.19</version>
				<scope>test</scope>
			</dependency>
			<!--mongo的单元测试框架-->
			<dependency>
				<groupId>com.github.fakemongo</groupId>
				<artifactId>fongo</artifactId>
				<version>2.1.0</version>
				<scope>test</scope>
			</dependency>
			<!--fs-open组件开始-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-common-storage</artifactId>
				<version>${fs-open-common-storage.version}</version>
			</dependency>
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-common-result</artifactId>
				<version>${fs-open-common-result.version}</version>
			</dependency>
			<!--林宝日志-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-logback-support</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<!-- <groupId>com.facishare.open</groupId>
				<artifactId>fs-open-parent-pom</artifactId>
				<version>2.0.0-SNAPSHOT</version>
					配置结束-->
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-app-center-common</artifactId>
                <version>${app-center-common.version}</version>
            </dependency>
			<dependency>
				<artifactId>xstream</artifactId>
				<groupId>com.thoughtworks.xstream</groupId>
				<version>1.4.20</version>
			</dependency>

			<!--服务通-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-eservice-cases-api</artifactId>
				<version>${fs-eservice-cases.version}</version>
			</dependency>

			<!--组织架构 替换通讯录-->
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-organization-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-organization-adapter-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-servicelib-fsi</artifactId>
				<version>1.3-PROTO-1.3.7-SNAPSHOT</version>
			</dependency>

			<!--企业互联平台 start-->
			<dependency>
				<groupId>com.fxiaoke</groupId>
				<artifactId>fs-enterpriserelation-rest-api</artifactId>
				<version>1.2.3-SNAPSHOT</version>
			</dependency>
			<!--企业互联平台 end-->

			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-notify-proxy-api</artifactId>
				<version>1.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-enterprise-id-account-converter</artifactId>
				<version>1.1-SNAPSHOT</version>
			</dependency>
			<!--微信百川 api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-wechat-baichuan-api</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<!--微信代理引擎api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-wechat-proxy-core-api</artifactId>
				<version>0.0.2</version>
			</dependency>
			<!-- 培训助手 -->
			<dependency>
				<groupId>com.fxiaoke.training</groupId>
				<artifactId>fs-training-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.facishare.appserver</groupId>
				<artifactId>fs-appserver-training-api</artifactId>
				<version>5.3-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-jobs-api</artifactId>
				<version>${fs-open-jobs-api.version}</version>
			</dependency>
			<!--应用中心api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-app-center-api</artifactId>
				<version>${app-center-api.version}</version>
			</dependency>
			<!--问卷api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-surey-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<!-- 工单api-->
			<!--
			老工单不用了
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-workorder-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			-->
			<!-- 工单api(paas)-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-work-order-paas-api</artifactId>
				<version>1.0.0</version>
			</dependency>
			<!-- 智能表单-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-intelligence-form-api</artifactId>
				<version>1.0.8-SNAPSHOT</version>
			</dependency>

			<!--网页轮询改造api-->
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-polling-api</artifactId>
				<version>1.1.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>com.facishare</groupId>
						<artifactId>fs-common-mq</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

					<!--开平事件订阅api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-event-subscriber-api</artifactId>
				<version>0.0.2</version>
			</dependency>
			<!--app-access-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>app-access-control-api</artifactId>
				<version>0.0.2-SNAPSHOT</version>
			</dependency>
			<!--工具包-->
			<dependency>
				<groupId>com.fxiaoke.common</groupId>
				<artifactId>java-utils</artifactId>
				<version>${jutil.version}</version>

			</dependency>
			<!--消息系统api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-msg-query-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<!--自动消息回复api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-msg-auto-reply-api</artifactId>
				<version>0.0.10-SNAPSHOT</version>
			</dependency>
			<!--开平消息api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-broker-message-api</artifactId>
				<version>0.0.3</version>
				<exclusions>
					<exclusion>
						<groupId>com.facishare.open</groupId>
						<artifactId>fs-open-common-result</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--评论系统api-->
			<dependency>
				<groupId>com.facishare.appserver</groupId>
				<artifactId>fs-appserver-comment-api</artifactId>
				<version>1.3-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--素材api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-material-api</artifactId>
				<version>1.0.48-SNAPSHOT</version>
			</dependency>
			<!--oauth api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-oauth-base-api</artifactId>
				<version>2.1.0-SNAPSHOT</version>
			</dependency>
			<!--通讯录api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-addressbook-api</artifactId>
				<version>0.0.8-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>javassist</groupId>
						<artifactId>javassist</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--图片系统api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-warehouse-api</artifactId>
				<version>0.0.4</version>
			</dependency>
			<!--图片系统api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-assets-api</artifactId>
				<version>1.0.1</version>
			</dependency>
			<!--开平登录api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-broker-login-api</artifactId>
				<version>0.0.5-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>com.facishare.open</groupId>
						<artifactId>fs-open-common-result</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.github.dubbof</groupId>
						<artifactId>dubbo-support</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--CookieToAuthXC api-->
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-active-session-manage-api</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<!--开平oauth api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-broker-oauth-api</artifactId>
				<version>0.0.7</version>
				<exclusions>
					<exclusion>
						<groupId>com.github.dubbof</groupId>
						<artifactId>dubbo-support</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--消息系统api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-msg-api</artifactId>
				<version>0.0.19-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.ow2.util.bundles</groupId>
						<artifactId>javassist-3.14.0-GA</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.javassist</groupId>
						<artifactId>javassist</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>log4j-over-slf4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--工具类api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-common-utils</artifactId>
				<version>1.0.1-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>log4j-over-slf4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--公共日志-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-common-logger</artifactId>
				<version>0.0.1</version>
			</dependency>
			<!--帮助中心api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-support-center-api</artifactId>
				<version>1.1.7</version>
				<exclusions>
					<exclusion>
						<groupId>com.facishare.open</groupId>
						<artifactId>fs-open-common-result</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!--fcp-->
			<dependency>
				<groupId>com.facishare.dubbo</groupId>
				<artifactId>rpc-fcp</artifactId>
				<version>1.4.1-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-common-result</artifactId>
				<version>0.7.7-SNAPSHOT</version>
			</dependency>
			<!--crm使用gateway api-->
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-xtcrm-gateway-api</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
			<!--http协议pb格式的rest转换服务-->
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-resteasy-ext</artifactId>
				<version>1.0.2</version>
			</dependency>
			<!--管理后台登录api-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-portal-api</artifactId>
				<version>0.0.1</version>
			</dependency>
			<!--ehcache-->
			<dependency>
				<groupId>com.googlecode.ehcache-spring-annotations</groupId>
				<artifactId>ehcache-spring-annotations</artifactId>
				<version>1.1.2</version>
			</dependency>
			<dependency>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache-core</artifactId>
				<version>2.6.6</version>
			</dependency>
			<!--MQ依赖-->
			<dependency>
				<groupId>com.github.penggle</groupId>
				<artifactId>kaptcha</artifactId>
				<version>2.3.2</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.amqp</groupId>
				<artifactId>spring-rabbit</artifactId>
				<version>1.5.6.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>io.protostuff</groupId>
				<artifactId>protostuff-runtime</artifactId>
				<version>1.8.0</version>
			</dependency>
			<!--for csv file-->
			<dependency>
				<groupId>com.opencsv</groupId>
				<artifactId>opencsv</artifactId>
				<version>3.8</version>
			</dependency>
			<!--应用中心公共代码库-->
			<dependency>
				<groupId>com.facishare.open</groupId>
				<artifactId>fs-open-app-center-common-utils</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<!--自定义tab-->
			<dependency>
				<groupId>com.fxiaoke.fs-user-extension</groupId>
				<artifactId>fs-user-extension-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<!--多语言-->
			<dependency>
				<groupId>com.fxiaoke</groupId>
				<artifactId>i18n-client</artifactId>
				<version>${i18n-client.version}</version>
			</dependency>

			<dependency>
				<groupId>net.sf.json-lib</groupId>
				<artifactId>json-lib</artifactId>
				<version>2.4</version>
				<classifier>jdk15</classifier>
				<exclusions>
					<exclusion>
						<groupId>commons-logging</groupId>
						<artifactId>commons-logging</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--pay迁移-->
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>ibss-data-fda-api</artifactId>
				<version>1.2-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<artifactId>validation-api</artifactId>
						<groupId>javax.validation</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-experience-account-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>org.quartz-scheduler</groupId>
				<artifactId>quartz</artifactId>
				<version>2.2.2</version>
			</dependency>
			<dependency>
				<groupId>org.quartz-scheduler</groupId>
				<artifactId>quartz-jobs</artifactId>
				<version>2.2.2</version>
			</dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-online-consult-core-api</artifactId>
				<version>${fs-online-consult-core-api.version}</version>
			</dependency>
			<dependency>
				<groupId>io.protostuff</groupId>
				<artifactId>protostuff-api</artifactId>
				<version>1.8.0</version>
			</dependency>
			<dependency>
				<groupId>io.protostuff</groupId>
				<artifactId>protostuff-core</artifactId>
				<version>1.8.0</version>
			</dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-api</artifactId>
                <version>9.2.5-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-core</artifactId>
                <version>9.2.5-SNAPSHOT</version>
            </dependency>
			<dependency>
				<groupId>com.facishare</groupId>
				<artifactId>fs-rest-client-common-api</artifactId>
				<version>${restproxy.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.facishare</groupId>
						<artifactId>fs-metadata-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
		</dependencies>

    </dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>jetty-maven-plugin</artifactId>
					<version>9.4.25.v20191220</version>
					<configuration>
						<scanIntervalSeconds>0</scanIntervalSeconds>
						<webApp>
							<contextPath>/</contextPath>
						</webApp>
						<useTestScope>false</useTestScope>
					</configuration>
				</plugin>

				<!-- tomcat 插件 -->
				<plugin>
					<groupId>org.apache.tomcat.maven</groupId>
					<artifactId>tomcat7-maven-plugin</artifactId>
					<version>2.2</version>
					<configuration>
						<port>8080</port>
						<path>/</path>
						<server>tomcat-development-server</server>
						<useTestClasspath>false</useTestClasspath>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

</project>
